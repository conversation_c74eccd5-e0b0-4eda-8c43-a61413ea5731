# 背景
文件名：2025-01-17_6_lottery-dynamic-sections.md
创建于：2025-01-17_15:30:00
创建者：rainkygong
主分支：task/implement-redemption-feature_2025-01-17_5
任务分支：task/lottery-dynamic-sections_2025-01-17_6
Yolo模式：Off

# 任务描述
将大转盘配置中的分区数量由原来的固定12个，修改为与盲盒一样，由用户自由设定，范围4-12

# 项目概览
团团转是一个基于SwiftUI和CoreData的iOS班级管理应用，包含积分管理、大转盘抽奖、盲盒抽奖等功能。

⚠️ 警告：永远不要修改此部分 ⚠️
核心RIPER-5协议规则：
- 必须在每个响应开头声明当前模式
- RESEARCH模式：只允许信息收集，禁止建议和实施
- INNOVATE模式：只允许讨论解决方案，禁止具体规划和实施
- PLAN模式：只允许详细规划，禁止任何实施
- EXECUTE模式：只允许按计划实施，禁止偏离
- REVIEW模式：只允许验证实施与计划的符合程度
⚠️ 警告：永远不要修改此部分 ⚠️

# 分析

## 当前实现结构分析

### 1. 核心数据模型（`LotteryToolConfig+CoreDataClass.swift`）
- 第26-42行：`ToolType`枚举定义了三种抽奖道具的属性
- **大转盘当前限制**：
  - `defaultItemCount`: 固定返回12
  - `maxItemCount`: 固定返回12  
  - `minItemCount`: 返回1（通用）
- **盲盒参考实现**：
  - `defaultItemCount`: 返回5
  - `maxItemCount`: 返回20
  - `minItemCount`: 返回1

### 2. 表单数据模型（`LotteryToolFormData.swift`）
- 第67-70行：`isItemCountAdjustable`属性控制数量是否可调整
- **当前逻辑**：`return toolType != .wheel` (大转盘不可调整)
- 包含完整的数量调整逻辑：`updateItemCount()`、`adjustItemsArray()`等方法

### 3. UI界面层（`LotteryToolConfigFormView.swift`）
- **当前实现**：
  - 第220-240行：`fixedItemCountDisplay`专门显示"大转盘固定为12个分区"
  - 第173-218行：`itemCountConfig`提供滑块调整（仅盲盒/刮刮卡使用）
- **条件判断**：通过`formData.isItemCountAdjustable`决定显示哪个控件

### 4. 本地化文件
- `zh-Hans.lproj/Localizable.strings`中包含相关描述文字
- 需要更新"大转盘固定为12个分区"等描述

## 目标实现分析

将大转盘分区数量改为用户自定义（范围4-12），与盲盒实现方式保持一致：

### 需要修改的核心文件：
1. `tuantuanzhuan/CoreData/Entities/LotteryToolConfig+CoreDataClass.swift`
2. `tuantuanzhuan/Models/LotteryToolFormData.swift`  
3. `tuantuanzhuan/Views/Components/LotteryToolConfigFormView.swift`
4. `tuantuanzhuan/zh-Hans.lproj/Localizable.strings`
5. `tuantuanzhuan/en.lproj/Localizable.strings`

### 技术实现要点：
- 修改大转盘的数量范围限制（4-12）
- 启用大转盘的数量可调整性
- 复用现有的滑块调整UI组件
- 更新相关描述文字和默认值

## 详细修改清单

### 1. 数据模型修改
**文件**: `tuantuanzhuan/CoreData/Entities/LotteryToolConfig+CoreDataClass.swift`
- 第27行：修改`defaultItemCount`，大转盘从12改为8（中间值）
- 第36行：修改`maxItemCount`，大转盘从12改为12（保持上限）
- 第42行：修改`minItemCount`，大转盘从1改为4（设置下限）
- 更新相关注释文字

### 2. 表单数据模型修改
**文件**: `tuantuanzhuan/Models/LotteryToolFormData.swift`
- 第67-70行：修改`isItemCountAdjustable`逻辑，允许大转盘调整数量
- 确保相关的数量调整方法支持大转盘

### 3. UI界面修改
**文件**: `tuantuanzhuan/Views/Components/LotteryToolConfigFormView.swift`
- 第158行：移除对`fixedItemCountDisplay`的调用，改为使用`itemCountConfig`
- 第225-242行：删除`fixedItemCo0untDisplay`相关代码
- 第231行：删除硬编码的"大转盘固定为12个分区"文本

### 4. 道具选项描述修改
**文件**: `tuantuanzhuan/Views/Components/LotteryToolOptionsView.swift`
- 第191行：修改大转盘描述文字，从"12个固定分区"改为"4-12个可选分区"

### 5. 本地化文字修改
**文件**: `tuantuanzhuan/zh-Hans.lproj/Localizable.strings`
- 第396行：修改`"lottery_tool_config.form.fixed_sections"`的值
- 可能需要添加新的本地化键值来描述范围

### 6. 英文本地化文件
**文件**: `tuantuanzhuan/en.lproj/Localizable.strings`
- 检查是否需要添加对应的英文翻译

### 预期效果
修改完成后，大转盘配置将：
- 默认设置为8个分区（4-12范围的中间值）
- 支持用户通过滑块在4-12之间调整分区数量
- UI界面与盲盒保持一致的调整体验
- 保持向下兼容，现有12分区配置继续有效

# 提议的解决方案
[待制定行动计划]

# 当前执行步骤："1. 研究代码结构 - 已完成"

研究发现：
- 已识别大转盘固定12分区的所有限制点
- 确认盲盒的动态调整实现可作为参考模式
- 列出详细的6个文件修改清单
- 设计了合理的4-12分区范围和默认值策略

# 任务进度

## 2025-01-17_16:00:00
- 已修改：LotteryToolConfig+CoreDataClass.swift、LotteryToolFormData.swift、LotteryToolConfigFormView.swift、LotteryToolOptionsView.swift、zh-Hans.lproj/Localizable.strings
- 更改：将大转盘分区数量从固定12个改为用户可自定义4-12个范围，默认8个分区
- 原因：用户需求要求大转盘分区数量与盲盒一样支持用户自由设定
- 阻碍因素：无（编译测试通过）
- 状态：等待用户确认

# 最终审查
[完成后的总结] 