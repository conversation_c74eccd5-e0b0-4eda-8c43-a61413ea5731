# 背景
文件名：2025-01-17_5_implement-redemption-feature.md
创建于：2025-01-17_11:45:00
创建者：rainkygong
主分支：task/implement-history-records_2025-01-17_4
任务分支：task/implement-redemption-feature_2025-01-17_5
Yolo模式：Off

# 任务描述
实现学生详情页的"兑换"功能，具体需求：
1. 点击学生详情页的兑换按钮，弹出选项列表
2. 显示班级配置中设置的班级奖品和自定义按钮
3. 点击任意一个奖品卡片，扣除所需积分，无需弹窗确认
4. 点击自定义按钮，弹窗显示表单，需要填写名称和分值
5. 支持点"+"添加多个表单项
6. 点击确认后，扣除所需分值
7. 所有兑换操作，生成兑换记录

# 项目概览
团团转是一个基于SwiftUI+CoreData+CloudKit架构的班级积分管理应用，已有完整的数据模型和基础功能。刚刚完成了学生详情页的积分操作功能实现，现在需要实现兑换功能。

⚠️ 警告：永远不要修改此部分 ⚠️
本任务遵循RIPER-5协议：
- RESEARCH模式：深入分析代码结构和数据模型
- INNOVATE模式：设计解决方案架构
- PLAN模式：制定详细实施计划
- EXECUTE模式：准确实施计划内容
- REVIEW模式：验证实施与计划的符合程度
⚠️ 警告：永远不要修改此部分 ⚠️

# 分析

## 现有架构分析

### 1. 数据模型完整性 ✅
- **Prize模型**：包含name, cost, type, schoolClass属性，有create便利方法
- **RedemptionRecord模型**：包含prizeName, cost, timestamp, student属性，实现HistoryRecordProtocol
- **Student.redeemPrize()**：已实现兑换逻辑，检查积分并扣除，返回RedemptionRecord
- **CoreDataManager.addRedemptionRecord()**：已实现添加兑换记录的方法

### 2. 数据获取方法 ✅
- **schoolClass.sortedPrizes**：获取班级奖品列表，按名称排序
- **student.sortedRedemptionRecords**：获取学生兑换记录，按时间排序
- **CoreDataManager.addPrize()**：为班级添加奖品的方法

### 3. 现有UI结构
- **StudentDetailView.handleExchange()**：目前只有TODO注释，需要实现
- **ActionButtonsGrid**：兑换按钮已存在，回调已连接
- **HistoryRecordsView**：已支持显示兑换记录，选项卡切换
- **StudentDetailViewModel**：已有addRedemptionRecord()和deleteRedemptionRecord()方法

### 4. 参考架构模式
刚刚实现的积分操作功能提供了完美的架构参考：
- **StudentPointsOptionsView**：选项弹窗组件
- **StudentPointsFormView**：自定义表单组件  
- **StudentPointsOperation**：操作数据模型
- **StudentPointsFormData**：表单数据管理

## 功能缺口分析

### 需要实现的组件：

1. **数据模型文件**：
   - `StudentRedemptionOperation.swift`：兑换操作类型和数据结构定义
   - `StudentRedemptionFormData.swift`：自定义兑换表单数据管理和验证

2. **UI组件文件**：  
   - `StudentRedemptionOptionsView.swift`：兑换选项弹窗，显示班级奖品+自定义按钮
   - `StudentRedemptionFormView.swift`：自定义兑换表单，支持多项添加

3. **功能扩展**：
   - `StudentDetailViewModel`：添加兑换操作相关方法和状态管理
   - `StudentDetailView`：集成新组件，修改handleExchange()方法

4. **本地化支持**：
   - 中英文字符串添加到Localizable.strings

## 技术实现要点

### 1. 兑换选项弹窗设计
- 显示班级配置的奖品卡片（名称、积分成本、类型）
- 自定义兑换按钮
- 积分不足时禁用相应奖品
- 点击奖品直接兑换，无需确认

### 2. 自定义兑换表单设计  
- 支持填写奖品名称和积分成本
- 动态添加/删除表单项（"+"按钮）
- 表单验证：名称不能为空，积分必须为正整数
- 总积分检查：确保学生积分足够

### 3. 数据流程设计
```
点击兑换按钮 → 显示选项弹窗 → 
├── 点击奖品卡片 → 直接兑换 → 扣除积分 → 生成记录
└── 点击自定义 → 显示表单 → 填写信息 → 确认兑换 → 扣除积分 → 生成记录
```

### 4. 状态管理
- `showRedemptionOptions`：兑换选项弹窗显示状态
- `showRedemptionForm`：自定义表单显示状态
- `redemptionFormData`：表单数据对象
- 错误处理和成功反馈

# 提议的解决方案
[行动计划]

# 当前执行步骤："1. 研究分析现有代码结构"

# 任务进度
[带时间戳的变更历史]

# 最终审查
[完成后的总结] 