# 背景
文件名：2025-01-17_2_implement-class-operation-popup.md
创建于：2025-01-17_15:30:00
创建者：rainkygong
主分支：main
任务分支：task/implement-class-operation-popup_2025-01-17_2
Yolo模式：Ask

# 任务描述
实现首页"全班操作"按钮的完整功能，包括：
1. 点击"全班操作"按钮后，弹出全班加分和全班扣分两个选项按钮
2. 点击"全班加分"，弹窗显示表单（名称和分值），确定后给当前班级全班加分并生成记录
3. 点击"全班扣分"，弹窗显示表单（名称和分值），确定后给当前班级全班扣分并生成记录
4. 所有操作需要写入每个学生的历史记录中

# 项目概览
这是一个基于SwiftUI + MVVM + CoreData + CloudKit架构的班级积分管理iOS应用。
- 主要用于教师管理学生积分、奖惩记录
- 支持多班级管理、学生信息管理、积分统计等功能
- 已具备基础的全班加/扣分底层功能，需要完善用户交互界面

⚠️ 警告：永远不要修改此部分 ⚠️
核心RIPER-5协议规则：
- 只能在明确模式信号下转换模式
- EXECUTE模式必须100%遵循计划
- 任何偏离都必须返回PLAN模式
- 所有代码修改必须有完整上下文
- 禁止未经授权的修改和改进
⚠️ 警告：永远不要修改此部分 ⚠️

# 分析
## 现有代码结构
1. **HomeView.swift**: 包含`handleClassOperation()`占位符方法
2. **ActionButtonsView.swift**: 已实现"全班操作"按钮UI和回调
3. **HomeViewModel.swift**: 包含`addScoreToAllStudents()`和`deductScoreFromAllStudents()`方法
4. **SchoolClass+CoreDataClass.swift**: 提供`addPointsToAllStudents()`和`deductPointsFromAllStudents()`数据操作方法

## 技术架构要点
- 使用SwiftUI声明式UI框架
- MVVM架构模式：View -> ViewModel -> CoreData
- 多语言支持：使用`.localized`字符串本地化
- 统一设计系统：DesignSystem中定义颜色、间距、字体等
- 动画效果：使用SwiftUI内置动画系统

## 数据流分析
1. 用户点击"全班操作"按钮
2. HomeView.handleClassOperation() -> HomeViewModel状态管理
3. 显示选项弹窗 -> 用户选择加分/扣分
4. 显示表单弹窗 -> 用户输入名称和分值
5. ViewModel调用CoreData方法执行全班操作
6. 自动生成每个学生的积分记录

# 提议的解决方案
基于现有代码架构，采用组件化设计方案：

## 方案A: 双层弹窗设计（推荐）
1. **ClassOperationOptionsView**: 选项弹窗组件（加分/扣分选择）
2. **ClassOperationFormView**: 表单弹窗组件（名称和分值输入）
3. **HomeViewModel扩展**: 添加状态管理和业务逻辑方法
4. **多语言支持**: 添加相关字符串到Localizable.strings

优势：
- 符合现有组件设计模式
- 代码结构清晰，便于维护
- 复用现有设计系统和动画效果
- 支持完整的错误处理和验证

## 方案B: 单一弹窗设计
将选项和表单合并在一个弹窗中，使用选项卡切换。
缺点：界面复杂，不够直观。

## 方案C: 底部动作表单设计
使用系统原生的ActionSheet样式。
缺点：自定义程度低，与应用整体设计不符。

**选择方案A**：符合现有设计语言，用户体验最佳，代码结构最清晰。

# 当前执行步骤："1. 研究分析现有代码"

# 任务进度
[2025-01-17_15:30:00]
- 已创建：功能分支 task/implement-class-operation-popup_2025-01-17_2
- 已完成：项目代码结构分析
- 已完成：现有功能梳理和数据流分析
- 已完成：技术方案设计和评估
- 状态：研究阶段完成，待进入创新阶段

# 最终审查
[待完成] 