# 背景
文件名：2025-01-17_1_complete-localization-cleanup.md
创建于：2025-01-17_14:30:00
创建者：rainkygong
主分支：task/lottery-wheel-fixes_2025-01-17_7
任务分支：task/complete-localization-cleanup_2025-01-17_1
Yolo模式：Ask

# 任务描述
对团团转项目进行全面的硬编码中文字符串筛查和本地化处理，确保所有用户界面文本都通过本地化系统管理

# 项目概览
团团转是一个基于SwiftUI的班级积分管理应用，已经具备完整的本地化框架：
- String+Localization.swift 扩展提供 .localized 方法
- zh-Hans.lproj/Localizable.strings (823行中文本地化)
- en.lproj/Localizable.strings (572行英文本地化)

⚠️ 警告：永远不要修改此部分 ⚠️
核心RIPER-5协议规则摘要：
- 必须在每个响应开头声明当前模式
- RESEARCH模式：只允许信息收集，禁止建议和实施
- INNOVATE模式：只允许讨论解决方案想法，禁止具体规划  
- PLAN模式：创建详尽技术规范，禁止任何实施
- EXECUTE模式：严格按计划实施，禁止偏离
- REVIEW模式：验证实施与计划符合度
- 只有明确信号才能转换模式
⚠️ 警告：永远不要修改此部分 ⚠️

# 分析

## 已发现的硬编码中文字符串

### 1. 用户界面相关硬编码
**tuantuanzhuanApp.swift**
- Line 52: `"数据迁移"` → 需要本地化
- Line 53: `"知道了"` → 需要本地化
- Line 99: `"您已升级到付费会员..."` → 需要本地化
- Line 107: `"您的会员已到期..."` → 需要本地化
- Line 144: `"团团转"` → 需要本地化

### 2. 表单和示例数据硬编码
**ExcelImportView.swift**
- Line 84-90: 表格头部示例 `"姓名"`, `"学号"`, `"性别"`, `"初始积分"`
- Line 101-118: 示例数据 `"张三"`, `"李四"`, `"男"`, `"女"`

**LotteryToolConfigFormView.swift**
- Line 49: `"\(toolType.displayName)配置"`
- Line 135: `"未命名班级"`
- Line 187: `"\(formData.itemTitlePrefix)数量"`
- Line 266: `"\(formData.itemTitlePrefix)配置"`

### 3. 用户信息硬编码
**ProfileView.swift**
- Line 21: `"龚老师"` → 需要本地化
- Line 23: `"高级会员"` → 需要本地化

**UserInfoSection.swift**
- Line 190: `"龚老师"` → 需要本地化
- Line 192: `"高级会员"` → 需要本地化

### 4. 搜索框和输入提示硬编码
**HeaderView.swift**
- Line 63: `"搜索学生姓名或学号"` → 需要本地化

### 5. 组件中的硬编码文本
**SubscriptionBannerSection.swift**
- Line 149: `"个人信息卡片"` → 需要本地化

**PrizeSelectionView.swift**
- Line 41: `"选择奖品"` → 需要本地化
- Line 74: `"暂无奖品可选择\n请先在设置中配置奖品库"` → 需要本地化

### 6. 其他界面硬编码
**StudentCardView.swift**
- Line 116: `"未知"` → 需要本地化
- Line 332: `"学生卡片预览"` → 需要本地化

**SettingsView.swift**
- Line 456: `"加载中..."` → 需要本地化
- Line 458: `"返回"` → 需要本地化  
- Line 512: `"加载配置中..."` → 需要本地化
- Line 514: `"返回"` → 需要本地化

## 需要添加的本地化键值

### 系统级别本地化
```
// 应用级别
"app.data_migration.title" = "数据迁移";
"app.data_migration.acknowledge" = "知道了";
"app.migration.paid_member_message" = "您已升级到付费会员，现在可以享受多设备数据同步功能！应用将在下次启动时启用CloudKit同步。";
"app.migration.expired_member_message" = "您的会员已到期，多设备同步功能将被禁用。您的数据仍将保存在本地设备上。";
"app.name" = "团团转";

// 用户界面通用
"common.loading" = "加载中...";
"common.loading_config" = "加载配置中...";
"common.return" = "返回";
"common.go_back" = "返回";
"common.unknown" = "未知";
"common.unnamed_class" = "未命名班级";
```

### 组件级别本地化
```
// 搜索相关
"search.placeholder.student" = "搜索学生姓名或学号";

// 奖品相关
"prize.selection.title" = "选择奖品";
"prize.selection.no_prizes" = "暂无奖品可选择\n请先在设置中配置奖品库";

// 表单相关
"form.config_title_format" = "%@配置";
"form.item_count_format" = "%@数量";

// 预览相关
"preview.student_card" = "学生卡片预览";
"preview.teacher_name" = "龚老师";
"preview.premium_member" = "高级会员";

// Excel示例
"excel.example.name" = "姓名";
"excel.example.student_id" = "学号";
"excel.example.gender" = "性别";
"excel.example.initial_points" = "初始积分";
"excel.example.sample_name1" = "张三";
"excel.example.sample_name2" = "李四";
"excel.example.gender_male" = "男";
"excel.example.gender_female" = "女";
```

# 提议的解决方案

## 方案1: 系统性替换 (推荐)
**优势**：
- 全面覆盖所有硬编码字符串
- 保持代码一致性
- 易于维护和更新

**实施步骤**：
1. 将所有硬编码字符串添加到 Localizable.strings 文件
2. 使用 .localized 扩展方法替换所有硬编码字符串
3. 为所有新增的本地化键添加英文翻译
4. 验证所有界面显示正确

## 方案2: 渐进式替换
**优势**：
- 风险较低
- 可以分阶段实施

**劣势**：
- 可能遗漏某些字符串
- 代码不够统一

# 当前执行步骤："分析和规划阶段"

# 任务进度
[2025-01-17_14:30:00]
- 已完成：硬编码中文字符串全面筛查
- 发现：约30+个硬编码字符串需要处理
- 分析：主要集中在用户界面、示例数据、用户信息等
- 状态：待进入规划阶段

# 最终审查
[待完成] 