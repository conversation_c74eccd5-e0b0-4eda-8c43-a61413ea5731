# 背景
文件名：2025-01-15_2_add-student-functionality.md
创建于：2025-01-15_14:30:00
创建者：rainkygong
主分支：task/multilingual-support_2025-01-15_1
任务分支：task/add-student-functionality_2025-01-15_2
Yolo模式：Off

# 任务描述
实现首页"添加学生"按钮的完整功能：
1. 点击后先判断用户是否已经创建班级，若未创建班级，则弹窗提示
2. 若已创建班级，则为当前选中的班级添加学生，显示下拉菜单：
   - "手动添加"：弹出表单，填写姓名、学号、性别、初始分数，支持批量添加（右上角"+"）
   - "批量导入"：通过Excel表格导入学生信息

# 项目概览
团团转应用是一个基于SwiftUI + CoreData + CloudKit的班级积分管理系统。现有功能包括：
- 完整的班级管理系统
- 学生信息管理和积分记录
- 优秀的设计系统和组件库
- 完备的本地化支持

⚠️ 警告：永远不要修改此部分 ⚠️
RIPER-5协议要求：
- 必须在每个响应开头声明当前模式
- PLAN模式只能创建详细规范，禁止任何代码实施
- 必须提供精确的文件路径和函数签名
- 所有更改必须与原始需求相连接
- 最后转换为编号的顺序清单
⚠️ 警告：永远不要修改此部分 ⚠️

# 分析
通过研究现有代码发现：
1. HomeView.swift中handleAddStudent()方法仅有打印语句，需要完善
2. HomeViewModel.swift具备完整的班级管理功能，可通过classes.isEmpty判断班级状态
3. 已有优秀的弹窗组件：DeleteConfirmationDialog、DateRangePickerView可作为设计参考
4. CoreDataManager提供完整的学生管理API
5. DesignSystem.swift定义了统一的设计规范
6. 项目支持完整的中文本地化

# 提议的解决方案
创建模块化组件系统：

1. **添加学生选项菜单组件** (AddStudentOptionsView)
   - 下拉式菜单显示两个选项
   - 采用现有弹窗设计风格

2. **手动添加学生表单组件** (ManualAddStudentView)
   - 支持单个/批量学生信息输入
   - 动态添加表单行功能
   - 表单验证和提交

3. **Excel导入组件** (ExcelImportView)
   - 文件选择器集成
   - Excel数据解析和验证
   - 批量学生创建

4. **无班级提示弹窗** (NoClassAlertView)
   - 提示用户先创建班级
   - 提供快速创建班级入口

5. **HomeViewModel扩展**
   - 添加学生相关状态管理
   - Excel解析逻辑
   - 错误处理机制

# 当前执行步骤："12. 测试所有组件的集成和数据流"

# 任务进度
[2025-01-15_14:30:00]
- 已修改：创建功能分支 task/add-student-functionality_2025-01-15_2
- 更改：初始化任务环境
- 原因：开始添加学生功能开发
- 阻碍因素：无
- 状态：未确认

[2025-01-15_14:45:00]
- 已修改：StudentFormData.swift, NoClassAlertView.swift, AddStudentOptionsView.swift, ManualAddStudentView.swift, ExcelImportView.swift
- 更改：创建了核心组件文件：学生表单数据模型、无班级提示弹窗、选项菜单、手动添加表单和Excel导入界面
- 原因：实施清单项目1-5，建立UI组件基础
- 阻碍因素：无
- 状态：未确认

[2025-01-15_15:15:00]
- 已修改：ExcelParser.swift, HomeViewModel.swift, HomeView.swift, zh-Hans.lproj/Localizable.strings, en.lproj/Localizable.strings
- 更改：完成了Excel解析工具类、HomeViewModel业务逻辑扩展、HomeView界面集成和完整的本地化支持
- 原因：实施清单项目6-11，完成核心业务逻辑和界面集成
- 阻碍因素：需要扩展CoreDataManager以支持学号重复检查和初始积分功能
- 状态：未确认

# 最终审查
待完成 