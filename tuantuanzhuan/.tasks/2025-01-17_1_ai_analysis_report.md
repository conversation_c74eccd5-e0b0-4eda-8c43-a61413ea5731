# 背景
文件名：2025-01-17_1_ai_analysis_report.md
创建于：2025-01-17_14:30:00
创建者：rainkygong
主分支：main
任务分支：task/ai_analysis_report_2025-01-17_1
Yolo模式：Ask

# 任务描述
实现AI生成分析报告功能，调用DeepSeek API对学生的积分记录进行智能分析，生成结构化的行为分析报告。该功能仅高级会员可用，需要学生有10条以上的加/扣分记录。

## 具体要求：
1. 集成DeepSeek API (***********************************)
2. 实现数据脱敏处理（不传输学生真实姓名和学号）
3. 只分析积分记录，不包含兑换和抽奖记录
4. 权限控制：仅高级会员可用
5. 记录数要求：≥10条加/扣分记录
6. 网络状态检查：离线时禁用功能
7. 支持本地化（中英文）
8. 报告展示和复制功能

# 项目概览
团团转是一个基于SwiftUI + CoreData + CloudKit的班级积分管理iOS应用。
- 技术栈：SwiftUI, CoreData, CloudKit
- 会员系统：免费版、初级会员、高级会员
- AI功能权限：仅高级会员可用
- 数据模型：Student -> PointRecord (积分记录)
- 认证系统：AuthenticationManager管理用户状态
- 支持iOS 15.6+，完整本地化支持

⚠️ 警告：永远不要修改此部分 ⚠️
核心RIPER-5协议规则：
1. 必须在每个响应开头声明模式
2. RESEARCH模式：仅收集信息，禁止实施
3. INNOVATE模式：仅讨论方案，禁止规划
4. PLAN模式：详细规划，禁止实施
5. EXECUTE模式：严格按计划实施
6. REVIEW模式：验证实施与计划的一致性
⚠️ 警告：永远不要修改此部分 ⚠️

# 分析
通过代码分析发现：

## 现有结构
1. **学生详情页**：StudentDetailView中已有`onAnalysisReportTapped`回调，但未实现
2. **数据模型**：Student实体包含多个PointRecord，具备完整的积分记录数据
3. **会员系统**：Subscription实体有`supportsAIAnalysis`属性用于权限控制
4. **认证管理**：AuthenticationManager管理用户登录和会员状态
5. **本地化支持**：已有"分析报告"相关的本地化字符串

## 关键发现
1. PointRecord包含：reason(原因)、value(分值)、timestamp(时间)
2. 学生实体有`sortedPointRecords`属性获取排序后的记录
3. 会员验证通过`user.isAdvancedUser`或`subscription.supportsAIAnalysis`
4. DeepSeek API兼容OpenAI格式，base_url: https://api.deepseek.com

## 技术要点
1. 数据脱敏：仅传输reason、value、timestamp，不传输学生姓名/学号
2. 网络检查：使用Reachability或URLSession检查网络连接
3. 异步处理：API调用需要async/await处理
4. 错误处理：网络错误、API错误、权限错误的处理
5. UI展示：新增AI分析报告页面，支持复制功能

# 提议的解决方案

## 确定方案
- **架构方案**：分层架构方案（方案二）
- **数据脱敏**：传送完整积分记录（时间、原因、分值）+ 性别 + 年级，不传输姓名学号和兑换/抽奖记录
- **权限控制**：双重权限验证（前端+API层）

## 架构设计
1. **数据层**：AIAnalysisDataModel - 脱敏数据结构
2. **服务层**：AIAnalysisService - DeepSeek API封装
3. **业务层**：AIAnalysisReportViewModel - 业务逻辑管理
4. **展示层**：AIAnalysisReportView - 报告展示页面
5. **集成层**：在StudentDetailView中集成调用

# 当前执行步骤："1. 研究分析"

# 任务进度
[2025-01-17_14:30:00]
- 已完成：项目结构分析和现有代码理解
- 状态：research_completed

# 最终审查
[完成后的总结] 