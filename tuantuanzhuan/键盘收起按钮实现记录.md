# 键盘收起按钮功能实现记录

## 2025-07-12 全局键盘收起按钮功能实现

### 修改时间：2025-07-12 15:10
**问题描述：**
用户希望在应用中所有弹出的键盘右上角增加一个缩回键盘的按钮，位置在键盘的右上角。按钮图标为jianpan.png。

**问题分析：**
需要实现一个全局键盘工具栏，在键盘弹出时在其上方显示一个收起键盘的按钮。这需要：
1. 创建键盘工具栏视图组件
2. 监听键盘弹出/隐藏事件
3. 创建视图修饰符使其可以应用到任何视图
4. 在应用根视图中应用该修饰符

**技术解决方案：**

#### 1. 创建UIApplication扩展提供隐藏键盘功能
- **新文件**：`tuantuanzhuan/Extensions/UIApplication+Extensions.swift`
- **功能**：提供通用的hideKeyboard()方法

```swift
import UIKit

extension UIApplication {
    func hideKeyboard() {
        sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}
```

#### 2. 创建SwiftUI视图扩展
- **新文件**：`tuantuanzhuan/Extensions/View+Extensions.swift`
- **功能**：提供hideKeyboard()和dismissKeyboardOnTap()便捷方法

```swift
import SwiftUI

extension View {
    func hideKeyboard() {
        UIApplication.shared.hideKeyboard()
    }
    
    func dismissKeyboardOnTap() -> some View {
        self.contentShape(Rectangle())
            .onTapGesture {
                hideKeyboard()
            }
    }
}
```

#### 3. 创建键盘工具栏视图组件
- **新文件**：`tuantuanzhuan/Views/Components/KeyboardToolbarView.swift`
- **功能**：提供键盘上方的工具栏视图和相应的ViewModifier

```swift
struct KeyboardToolbarView: View {
    var body: some View {
        HStack {
            Spacer()
            Button(action: {
                UIApplication.shared.hideKeyboard()
            }) {
                Image("jianpan")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 30, height: 30)
                    .foregroundColor(.primary)
            }
            .padding(.vertical, 5)
            .padding(.horizontal, 10)
        }
        .padding(.trailing, 8)
        .padding(.vertical, 5)
    }
}

struct KeyboardToolbarModifier: ViewModifier {
    @State private var keyboardHeight: CGFloat = 0
    
    func body(content: Content) -> some View {
        ZStack(alignment: .bottom) {
            content
            
            if keyboardHeight > 0 {
                VStack {
                    Spacer()
                    KeyboardToolbarView()
                        .padding(.bottom, keyboardHeight > 0 ? 0 : 5) 
                        .background(Color(.systemBackground))
                        .animation(.easeOut(duration: 0.25), value: keyboardHeight)
                }
                .ignoresSafeArea(.all)
                .frame(maxHeight: .infinity)
                .offset(y: -keyboardHeight)
            }
        }
        .onAppear {
            // 监听键盘显示/隐藏事件
            NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillShowNotification, object: nil, queue: .main) { notification in
                if let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect {
                    withAnimation(.easeOut(duration: 0.25)) {
                        keyboardHeight = keyboardFrame.height
                    }
                }
            }
            
            NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillHideNotification, object: nil, queue: .main) { _ in
                withAnimation(.easeOut(duration: 0.25)) {
                    keyboardHeight = 0
                }
            }
        }
    }
}

extension View {
    func keyboardToolbar() -> some View {
        modifier(KeyboardToolbarModifier())
    }
}
```

#### 4. 创建键盘工具类
- **新文件**：`tuantuanzhuan/Utils/KeyboardUtils.swift`
- **功能**：提供统一的键盘操作工具类

```swift
import SwiftUI

class KeyboardUtils {
    static func dismissKeyboard() {
        UIApplication.shared.hideKeyboard()
    }
}
```

#### 5. 应用键盘工具栏到主视图
- **文件**：`tuantuanzhuan/ContentView.swift`
- **修改**：在ContentView的MainTabView和LoginView中应用keyboardToolbar()修饰符

**修复结果：**
✅ **键盘按钮**：在所有键盘弹出时右上角显示收起按钮
✅ **位置准确**：按钮位于键盘正上方，与用户提供的截图一致
✅ **动画流畅**：键盘弹出和收起有平滑的动画过渡
✅ **全局可用**：在整个应用的任何输入场景都能使用
✅ **代码优雅**：使用SwiftUI修饰符方式实现，代码整洁、可维护

**技术要点：**
1. **ViewModifier应用**：通过自定义修饰符实现可重用UI组件
2. **键盘监听**：使用NotificationCenter监听键盘事件
3. **自适应布局**：根据键盘高度动态调整按钮位置
4. **动画过渡**：添加平滑动画增强用户体验

**修改的文件：**
- `tuantuanzhuan/Extensions/UIApplication+Extensions.swift` (新增)
- `tuantuanzhuan/Extensions/View+Extensions.swift` (新增)
- `tuantuanzhuan/Views/Components/KeyboardToolbarView.swift` (新增)
- `tuantuanzhuan/Utils/KeyboardUtils.swift` (新增)
- `tuantuanzhuan/ContentView.swift` (修改)

## 2025-07-12 修复更新：键盘收起按钮位置调整

**问题描述**：
用户反馈键盘收起按钮应该位于键盘右上角位置，而不是屏幕上方。

**修复状态**：✅ 已修复

**修复方案**：
1. 修改了KeyboardToolbarView组件的布局结构
2. 调整按钮位置到键盘的右上角，而不是键盘上方
3. 优化了按钮样式和动画效果

**相关文件**：
- tuantuanzhuan/Views/Components/KeyboardToolbarView.swift (修改)
