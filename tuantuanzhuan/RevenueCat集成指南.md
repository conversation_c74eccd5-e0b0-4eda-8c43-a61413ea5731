# RevenueCat完整集成指南

## 🎯 概述

本指南将帮助您完成团团转应用的RevenueCat订阅系统集成。我们已经为您实现了完整的代码架构，您只需要完成以下配置工作。

## 📋 功能特性

### 订阅等级
- **免费版**：1个班级，基础功能，多设备同步
- **初级会员**：2个班级，大转盘抽奖，多设备同步
- **高级会员**：5个班级，盲盒/刮刮卡，AI分析报告，多设备同步

### 技术特性
- ✅ StoreKit 2 支持
- ✅ 自动恢复购买
- ✅ 多设备同步
- ✅ 权限验证
- ✅ 代码赠送试用
- ✅ 订阅状态实时更新

## 🚀 集成步骤

### 第一步：RevenueCat Dashboard配置

1. **注册RevenueCat账户**
   - 访问 https://app.revenuecat.com/
   - 创建免费账户
   - 创建新项目

2. **配置App Store Connect集成**
   - 在RevenueCat Dashboard中点击"项目设置"
   - 选择"App Store Connect"
   - 上传您的 App Store Connect API Key (.p8文件)
   - 填写 Issuer ID 和 Key ID

3. **创建产品(Products)**
   ```
   产品ID                                    类型        名称
   com.tuantuanzhuan.subscription.monthly.basic    月订阅      初级会员(月)
   com.tuantuanzhuan.subscription.yearly.basic     年订阅      初级会员(年)
   com.tuantuanzhuan.subscription.monthly.premium  月订阅      高级会员(月)
   com.tuantuanzhuan.subscription.yearly.premium   年订阅      高级会员(年)
   ```

4. **配置Entitlements**
   ```
   Entitlement ID    名称
   basic_member      初级会员权限
   premium_member    高级会员权限
   ```

5. **创建Offerings**
   - 创建Default Offering
   - 添加Package并关联产品
   - 配置产品显示信息

6. **获取API Key**
   - 在Dashboard中找到"API Keys"
   - 复制"Apple App Store" API Key

### 第二步：App Store Connect配置

1. **创建订阅产品**
   在App Store Connect中创建与RevenueCat匹配的订阅产品：
   
   ```
   产品ID：com.tuantuanzhuan.subscription.monthly.basic
   名称：初级会员
   持续时间：1个月
   价格：¥19.00（建议）
   
   产品ID：com.tuantuanzhuan.subscription.yearly.basic
   名称：初级会员
   持续时间：1年
   价格：¥128.00（建议）
   
   产品ID：com.tuantuanzhuan.subscription.monthly.premium
   名称：高级会员
   持续时间：1个月
   价格：¥38.00（建议）
   
   产品ID：com.tuantuanzhuan.subscription.yearly.premium
   名称：高级会员
   持续时间：1年
   价格：¥298.00（建议）
   ```

2. **配置订阅群组**
   - 创建订阅群组（例如："团团转会员"）
   - 将所有产品添加到同一个群组

### 第三步：更新API Key

打开 `tuantuanzhuan/tuantuanzhuanApp.swift` 文件，将API Key替换为您的实际Key：

```swift
private func configureRevenueCat() {
    // 将此API Key替换为您从RevenueCat Dashboard获取的真实API Key
    let apiKey = "appl_GzPHLwaDLPnAcusWETDnYhSfJDv"  // ← 替换这里
    
    // ... 其他代码保持不变
}
```

### 第四步：Xcode项目配置

1. **启用In-App Purchase能力**
   - 在Xcode中选择您的项目
   - 点击您的Target
   - 选择"Signing & Capabilities"
   - 点击"+ Capability"
   - 添加"In-App Purchase"

2. **添加RevenueCat SDK（如果尚未添加）**
   - 在Xcode中选择 File → Add Package Dependencies
   - 输入URL：`https://github.com/RevenueCat/purchases-ios.git`
   - 选择版本：5.16.0 或更高
   - 添加`RevenueCat`库

### 第五步：测试配置

1. **沙盒测试**
   - 在App Store Connect中创建沙盒测试账户
   - 使用沙盒账户测试购买流程
   - 验证权限正确授予

2. **功能测试清单**
   ```
   ✅ 应用启动时RevenueCat正确初始化
   ✅ 产品价格正确显示
   ✅ 购买流程正常工作
   ✅ 恢复购买功能正常
   ✅ 权限验证正确
   ✅ 订阅状态同步到CoreData
   ✅ 班级数量限制生效
   ✅ 功能锁定/解锁正常
   ```

## 💡 使用示例

### 检查权限
```swift
// 检查抽奖权限
let permissionManager = PermissionManager.shared
if permissionManager.hasPermission(for: .lottery) {
    // 用户可以使用抽奖功能
} else {
    // 显示升级提示
}

// 检查AI分析权限
let (canUse, reason) = permissionManager.canUseAIAnalysisForStudent(student)
if canUse {
    // 可以生成AI分析报告
} else {
    // 显示原因：reason
}
```

### 购买订阅
```swift
// 通过SubscriptionViewModel购买
let viewModel = SubscriptionViewModel()
viewModel.purchaseBasicMonthly()
```

### 赠送试用
```swift
// 赠送30天高级会员试用
let success = await RevenueCatManager.shared.grantPremiumTrialDays(30)
```

## 🔧 故障排除

### 常见问题

1. **API Key错误**
   ```
   错误：RevenueCat API Key不能为空
   解决：确保在tuantuanzhuanApp.swift中设置了正确的API Key
   ```

2. **产品价格显示"加载中..."**
   ```
   原因：RevenueCat未能获取产品信息
   检查：App Store Connect中的产品是否正确配置
   解决：确保产品ID完全匹配
   ```

3. **购买失败**
   ```
   原因：沙盒环境配置问题
   检查：确保使用正确的沙盒账户
   解决：重新登录沙盒账户，清除应用数据
   ```

### 调试技巧

1. **启用详细日志**
   ```swift
   // 已在RevenueCatManager中配置
   Purchases.logLevel = .debug
   ```

2. **检查订阅状态**
   ```swift
   let manager = RevenueCatManager.shared
   print("当前订阅级别：\(manager.currentSubscriptionLevel)")
   print("到期日期：\(manager.expirationDate)")
   ```

## 📝 重要注意事项

1. **生产环境**
   - 确保使用生产环境的API Key
   - 移除或注释调试日志
   - 充分测试所有订阅流程

2. **隐私合规**
   - RevenueCat会收集用户数据用于订阅管理
   - 确保在隐私政策中说明相关数据收集

3. **价格策略**
   - 建议的价格仅供参考
   - 请根据您的商业模式调整

4. **升级路径**
   - 免费→基础：¥19/月 或 ¥128/年
   - 基础→高级：¥38/月 或 ¥298/年
   - 免费→高级：直接订阅高级套餐

## 🎉 完成！

完成以上步骤后，您的团团转应用将拥有完整的订阅系统：

- ✅ 三个订阅等级
- ✅ 功能权限控制
- ✅ 自动恢复购买
- ✅ 多设备同步
- ✅ 试用赠送功能
- ✅ 本地化支持

如有任何问题，请参考RevenueCat官方文档：https://docs.revenuecat.com/