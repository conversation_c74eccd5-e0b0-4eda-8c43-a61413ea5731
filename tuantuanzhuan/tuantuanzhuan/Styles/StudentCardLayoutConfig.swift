//
//  StudentCardLayoutConfig.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/6/24.
//

import SwiftUI

/**
 * 位置配置结构
 */
struct PositionConfig {
    let xRatio: CGFloat     // 0.0-1.0 相对x坐标
    let yRatio: CGFloat     // 0.0-1.0 相对y坐标  
    let anchor: UnitPoint   // 锚点位置
    let offsetX: CGFloat    // 像素偏移
    let offsetY: CGFloat    // 像素偏移
    
    init(xRatio: CGFloat, yRatio: CGFloat, anchor: UnitPoint = .topLeading, offsetX: CGFloat = 0, offsetY: CGFloat = 0) {
        self.xRatio = xRatio
        self.yRatio = yRatio
        self.anchor = anchor
        self.offsetX = offsetX
        self.offsetY = offsetY
    }
}

/**
 * 尺寸策略枚举
 */
enum SizeStrategy {
    case fixed(CGFloat)         // 固定尺寸
    case ratio(CGFloat)         // 相对于卡片的比例
    case content                // 内容自适应
    case available(CGFloat)     // 可用空间的比例
}

/**
 * 尺寸配置结构
 */
struct SizeConfig {
    let widthStrategy: SizeStrategy
    let heightStrategy: SizeStrategy
    let aspectRatio: CGFloat?
    let scaleRatio: CGFloat
    
    init(widthStrategy: SizeStrategy, heightStrategy: SizeStrategy, aspectRatio: CGFloat? = nil, scaleRatio: CGFloat = 1.0) {
        self.widthStrategy = widthStrategy
        self.heightStrategy = heightStrategy
        self.aspectRatio = aspectRatio
        self.scaleRatio = scaleRatio
    }
}

/**
 * 样式配置结构
 */
struct StyleConfig {
    let fontSize: CGFloat?
    let fontWeight: Font.Weight?
    let color: Color?
    let opacity: Double
    let cornerRadius: CGFloat?
    
    init(fontSize: CGFloat? = nil, fontWeight: Font.Weight? = nil, color: Color? = nil, opacity: Double = 1.0, cornerRadius: CGFloat? = nil) {
        self.fontSize = fontSize
        self.fontWeight = fontWeight
        self.color = color
        self.opacity = opacity
        self.cornerRadius = cornerRadius
    }
}

/**
 * 约束配置结构
 */
struct ConstraintsConfig {
    let minWidth: CGFloat?
    let maxWidth: CGFloat?
    let minHeight: CGFloat?
    let maxHeight: CGFloat?
    let maxLines: Int?
    
    init(minWidth: CGFloat? = nil, maxWidth: CGFloat? = nil, minHeight: CGFloat? = nil, maxHeight: CGFloat? = nil, maxLines: Int? = nil) {
        self.minWidth = minWidth
        self.maxWidth = maxWidth
        self.minHeight = minHeight
        self.maxHeight = maxHeight
        self.maxLines = maxLines
    }
}

/**
 * 通用元素配置协议
 */
protocol ElementLayerConfig {
    var position: PositionConfig { get }
    var size: SizeConfig { get }
    var style: StyleConfig { get }
    var constraints: ConstraintsConfig { get }
}

/**
 * 头像层配置
 */
struct AvatarLayerConfig: ElementLayerConfig {
    let position: PositionConfig
    let size: SizeConfig
    let style: StyleConfig
    let constraints: ConstraintsConfig
    
    static let `default` = AvatarLayerConfig(
        position: PositionConfig(xRatio: 0.15, yRatio: 0.45, anchor: .topLeading),
        size: SizeConfig(widthStrategy: .ratio(1), heightStrategy: .ratio(1)),
        style: StyleConfig(cornerRadius: 1000), // 完全圆形
        constraints: ConstraintsConfig(minWidth: 70, maxWidth: 70, minHeight: 70, maxHeight: 70)
    )
}

/**
 * 姓名层配置
 */
struct NameLayerConfig: ElementLayerConfig {
    let position: PositionConfig
    let size: SizeConfig
    let style: StyleConfig
    let constraints: ConstraintsConfig
    
    static let `default` = NameLayerConfig(
        position: PositionConfig(xRatio: 0.55, yRatio: 0.35, anchor: .leading),
        size: SizeConfig(widthStrategy: .available(0.6), heightStrategy: .content),
        style: StyleConfig(
            fontSize: 18,
            fontWeight: .regular,
            color: DesignSystem.Colors.textPrimary
        ),
        constraints: ConstraintsConfig(maxLines: 1)
    )
}

/**
 * 积分层配置
 */
struct ScoreLayerConfig: ElementLayerConfig {
    let position: PositionConfig
    let size: SizeConfig
    let style: StyleConfig
    let constraints: ConstraintsConfig
    
    static let `default` = ScoreLayerConfig(
        position: PositionConfig(xRatio: 0.9, yRatio: 0.8, anchor: .bottomTrailing),
        size: SizeConfig(widthStrategy: .content, heightStrategy: .content),
        style: StyleConfig(
            fontSize: 30,
            fontWeight: .regular,
            color: DesignSystem.Colors.textScore
        ),
        constraints: ConstraintsConfig()
    )
}

/**
 * 背景水印层配置
 */
struct WatermarkLayerConfig: ElementLayerConfig {
    let position: PositionConfig
    let size: SizeConfig
    let style: StyleConfig
    let constraints: ConstraintsConfig
    
    static let `default` = WatermarkLayerConfig(
        position: PositionConfig(xRatio: 0.55, yRatio: 0.5, anchor: .center),
        size: SizeConfig(widthStrategy: .content, heightStrategy: .content),
        style: StyleConfig(
            fontSize: 80,
            fontWeight: .heavy,
            color: Color(hex: "#f8f8f8"),
            opacity: 1.0
        ),
        constraints: ConstraintsConfig()
    )
}

/**
 * 设备断点枚举
 */
enum DeviceBreakpoint {
    case compact    // iPhone SE类设备
    case regular    // iPhone标准设备
    case large      // iPhone Plus/Max设备
    case extraLarge // iPad设备
}

/**
 * 响应式断点配置
 */
struct ResponsiveBreakpoints {
    
    /**
     * 根据几何尺寸判断当前设备断点
     */
    static func currentBreakpoint(for geometry: GeometryProxy) -> DeviceBreakpoint {
        let width = geometry.size.width
        _ = geometry.size.height // 暂未使用，避免警告
        
        // 基于卡片宽度判断设备类型
        if width < 140 {
            return .compact
        } else if width < 180 {
            return .regular
        } else if width < 220 {
            return .large
        } else {
            return .extraLarge
        }
    }
    
    /**
     * 根据断点获取对应的配置
     */
    static func configForBreakpoint(_ breakpoint: DeviceBreakpoint) -> StudentCardLayoutConfig {
        switch breakpoint {
        case .compact:
            return StudentCardLayoutConfig.compact
        case .regular:
            return StudentCardLayoutConfig.default
        case .large:
            return StudentCardLayoutConfig.large
        case .extraLarge:
            return StudentCardLayoutConfig.extraLarge
        }
    }
}

/**
 * 布局计算引擎
 */
class LayoutCalculator {
    private let geometry: GeometryProxy
    private let config: StudentCardLayoutConfig
    
    init(geometry: GeometryProxy, config: StudentCardLayoutConfig) {
        self.geometry = geometry
        self.config = config
    }
    
    // MARK: - 头像计算
    var avatarPosition: CGPoint {
        let x = geometry.size.width * config.avatar.position.xRatio + config.avatar.position.offsetX
        let y = geometry.size.height * config.avatar.position.yRatio + config.avatar.position.offsetY
        return CGPoint(x: x, y: y)
    }
    
    var avatarSize: CGSize {
        let width = calculateSize(strategy: config.avatar.size.widthStrategy, dimension: .width)
        let height = calculateSize(strategy: config.avatar.size.heightStrategy, dimension: .height)
        
        // 应用约束
        let constrainedWidth = applyConstraints(width, 
                                              min: config.avatar.constraints.minWidth, 
                                              max: config.avatar.constraints.maxWidth)
        let constrainedHeight = applyConstraints(height,
                                               min: config.avatar.constraints.minHeight,
                                               max: config.avatar.constraints.maxHeight)
        
        return CGSize(width: constrainedWidth, height: constrainedHeight)
    }
    
    // MARK: - 姓名计算
    var namePosition: CGPoint {
        let x = geometry.size.width * config.name.position.xRatio + config.name.position.offsetX
        let y = geometry.size.height * config.name.position.yRatio + config.name.position.offsetY
        return CGPoint(x: x, y: y)
    }
    
    var nameStyle: StyleConfig {
        return config.name.style
    }
    
    var nameConstraints: ConstraintsConfig {
        return config.name.constraints
    }
    
    // MARK: - 积分计算
    var scorePosition: CGPoint {
        let x = geometry.size.width * config.score.position.xRatio + config.score.position.offsetX
        let y = geometry.size.height * config.score.position.yRatio + config.score.position.offsetY
        return CGPoint(x: x, y: y)
    }
    
    var scoreStyle: StyleConfig {
        return config.score.style
    }
    
    // MARK: - 背景水印计算
    var watermarkPosition: CGPoint {
        let x = geometry.size.width * config.watermark.position.xRatio + config.watermark.position.offsetX
        let y = geometry.size.height * config.watermark.position.yRatio + config.watermark.position.offsetY
        return CGPoint(x: x, y: y)
    }
    
    var watermarkStyle: StyleConfig {
        return config.watermark.style
    }
    
    // MARK: - 私有辅助方法
    private enum Dimension {
        case width, height
    }
    
    private func calculateSize(strategy: SizeStrategy, dimension: Dimension) -> CGFloat {
        let referenceSize = dimension == .width ? geometry.size.width : geometry.size.height
        
        switch strategy {
        case .fixed(let value):
            return value
        case .ratio(let ratio):
            return referenceSize * ratio
        case .content:
            return 0 // 由内容决定，这里返回0表示不限制
        case .available(let ratio):
            return referenceSize * ratio
        }
    }
    
    private func applyConstraints(_ value: CGFloat, min: CGFloat?, max: CGFloat?) -> CGFloat {
        var result = value
        if let minValue = min {
            result = Swift.max(result, minValue)
        }
        if let maxValue = max {
            result = Swift.min(result, maxValue)
        }
        return result
    }
}

/**
 * 学生卡片布局配置主结构
 */
struct StudentCardLayoutConfig {
    let avatar: AvatarLayerConfig
    let name: NameLayerConfig
    let score: ScoreLayerConfig
    let watermark: WatermarkLayerConfig
    
    // MARK: - 默认配置
    static let `default` = StudentCardLayoutConfig(
        avatar: AvatarLayerConfig.default,
        name: NameLayerConfig.default,
        score: ScoreLayerConfig.default,
        watermark: WatermarkLayerConfig.default
    )
    
    // MARK: - 紧凑设备配置 (缩小10%)
    static let compact = StudentCardLayoutConfig(
        avatar: AvatarLayerConfig(
            position: AvatarLayerConfig.default.position,
            size: SizeConfig(
                widthStrategy: .ratio(0.495), // 0.55 * 0.9
                heightStrategy: .ratio(0.495)
            ),
            style: AvatarLayerConfig.default.style,
            constraints: AvatarLayerConfig.default.constraints
        ),
        name: NameLayerConfig(
            position: NameLayerConfig.default.position,
            size: NameLayerConfig.default.size,
            style: StyleConfig(
                fontSize: 14, // 调整为更小的字体
                fontWeight: NameLayerConfig.default.style.fontWeight,
                color: NameLayerConfig.default.style.color
            ),
            constraints: NameLayerConfig.default.constraints
        ),
        score: ScoreLayerConfig(
            position: ScoreLayerConfig.default.position,
            size: ScoreLayerConfig.default.size,
            style: StyleConfig(
                fontSize: 16, // 18 * 0.9
                fontWeight: ScoreLayerConfig.default.style.fontWeight,
                color: ScoreLayerConfig.default.style.color
            ),
            constraints: ScoreLayerConfig.default.constraints
        ),
        watermark: WatermarkLayerConfig(
            position: WatermarkLayerConfig.default.position,
            size: WatermarkLayerConfig.default.size,
            style: StyleConfig(
                fontSize: 72, // 80 * 0.9
                fontWeight: WatermarkLayerConfig.default.style.fontWeight,
                color: WatermarkLayerConfig.default.style.color,
                opacity: WatermarkLayerConfig.default.style.opacity
            ),
            constraints: WatermarkLayerConfig.default.constraints
        )
    )
    
    // MARK: - 大屏设备配置 (放大15%)
    static let large = StudentCardLayoutConfig(
        avatar: AvatarLayerConfig(
            position: AvatarLayerConfig.default.position,
            size: SizeConfig(
                widthStrategy: .ratio(0.633), // 0.55 * 1.15
                heightStrategy: .ratio(0.633)
            ),
            style: AvatarLayerConfig.default.style,
            constraints: AvatarLayerConfig.default.constraints
        ),
        name: NameLayerConfig(
            position: NameLayerConfig.default.position,
            size: NameLayerConfig.default.size,
            style: StyleConfig(
                fontSize: 18, // 16 * 1.15
                fontWeight: NameLayerConfig.default.style.fontWeight,
                color: NameLayerConfig.default.style.color
            ),
            constraints: NameLayerConfig.default.constraints
        ),
        score: ScoreLayerConfig(
            position: ScoreLayerConfig.default.position,
            size: ScoreLayerConfig.default.size,
            style: StyleConfig(
                fontSize: 21, // 18 * 1.15
                fontWeight: ScoreLayerConfig.default.style.fontWeight,
                color: ScoreLayerConfig.default.style.color
            ),
            constraints: ScoreLayerConfig.default.constraints
        ),
        watermark: WatermarkLayerConfig(
            position: WatermarkLayerConfig.default.position,
            size: WatermarkLayerConfig.default.size,
            style: StyleConfig(
                fontSize: 92, // 80 * 1.15
                fontWeight: WatermarkLayerConfig.default.style.fontWeight,
                color: WatermarkLayerConfig.default.style.color,
                opacity: WatermarkLayerConfig.default.style.opacity
            ),
            constraints: WatermarkLayerConfig.default.constraints
        )
    )
    
    // MARK: - 超大屏设备配置 (iPad)
    static let extraLarge = StudentCardLayoutConfig(
        avatar: AvatarLayerConfig(
            position: AvatarLayerConfig.default.position,
            size: SizeConfig(
                widthStrategy: .ratio(0.715), // 0.55 * 1.3
                heightStrategy: .ratio(0.715)
            ),
            style: AvatarLayerConfig.default.style,
            constraints: AvatarLayerConfig.default.constraints
        ),
        name: NameLayerConfig(
            position: NameLayerConfig.default.position,
            size: NameLayerConfig.default.size,
            style: StyleConfig(
                fontSize: 17, // 调整为更小的字体
                fontWeight: NameLayerConfig.default.style.fontWeight,
                color: NameLayerConfig.default.style.color
            ),
            constraints: NameLayerConfig.default.constraints
        ),
        score: ScoreLayerConfig(
            position: ScoreLayerConfig.default.position,
            size: ScoreLayerConfig.default.size,
            style: StyleConfig(
                fontSize: 23, // 18 * 1.3
                fontWeight: ScoreLayerConfig.default.style.fontWeight,
                color: ScoreLayerConfig.default.style.color
            ),
            constraints: ScoreLayerConfig.default.constraints
        ),
        watermark: WatermarkLayerConfig(
            position: WatermarkLayerConfig.default.position,
            size: WatermarkLayerConfig.default.size,
            style: StyleConfig(
                fontSize: 104, // 80 * 1.3
                fontWeight: WatermarkLayerConfig.default.style.fontWeight,
                color: WatermarkLayerConfig.default.style.color,
                opacity: WatermarkLayerConfig.default.style.opacity
            ),
            constraints: WatermarkLayerConfig.default.constraints
        )
    )
}

// MARK: - 独立视图层组件

/**
 * 背景水印层组件
 */
struct WatermarkLayer: View {
    let text: String
    let position: CGPoint
    let style: StyleConfig
    
    var body: some View {
        Text(text)
            .font(.custom("Impact", size: style.fontSize ?? 80))
            .fontWeight(style.fontWeight ?? .heavy)
            .foregroundColor(style.color ?? Color(hex: "#f8f8f8"))
            .opacity(style.opacity)
            .position(position)
    }
}

/**
 * 头像层组件
 */
struct AvatarLayer: View {
    let imageName: String
    let position: CGPoint
    let size: CGSize
    
    var body: some View {
        Image(imageName)
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(width: size.width, height: size.height)
            .clipShape(Circle())
            .position(position)
    }
}

/**
 * 姓名层组件
 */
struct NameLayer: View {
    let text: String
    let position: CGPoint
    let style: StyleConfig
    let constraints: ConstraintsConfig
    
    var body: some View {
        Text(text)
            .font(.system(
                size: style.fontSize ?? 16,
                weight: style.fontWeight ?? .regular
            ))
            .foregroundColor(style.color ?? DesignSystem.Colors.textPrimary)
            .lineLimit(constraints.maxLines ?? 1)
            .position(position)
    }
}

/**
 * 积分层组件
 */
struct ScoreLayer: View {
    let score: Int
    let position: CGPoint
    let style: StyleConfig
    
    var body: some View {
        Text("\(score)")
            .font(.system(
                size: style.fontSize ?? 18,
                weight: style.fontWeight ?? .medium
            ))
            .foregroundColor(style.color ?? DesignSystem.Colors.textScore)
            .position(position)
    }
} 