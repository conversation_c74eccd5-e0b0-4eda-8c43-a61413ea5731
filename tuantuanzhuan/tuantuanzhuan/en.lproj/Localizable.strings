/* 
 * Localizable.strings (English)
 * tuantuanzhuan
 * 
 * English localization file for TuanTuanZhuan app
 */

// MARK: - Common Button Text
"common.button.cancel" = "Cancel";
"common.button.confirm" = "Confirm";
"common.button.delete" = "Delete";
"common.button.save" = "Save";
"common.button.close" = "Close";

// MARK: - Home Action Buttons
"home.button.class_total_score" = "Class Total Points";
"home.button.add_student" = "Add Student";
"home.button.class_operation" = "Class Operations";

// MARK: - Student Management
"student.empty.title" = "No Students";
"student.empty.description" = "Tap \"Add Student\" button to get started";
"student.delete.title" = "Delete Student";
"student.delete.message" = "Are you sure to delete student \"%@\"?";
"student.delete.confirm" = "Confirm Delete";
"student.info.unknown_name" = "Unknown";
"student.info.default_number" = "000";
"student.info.class_grade" = "Grade 4 Class 1";

// MARK: - Settings Page
"settings.title" = "Settings";
"settings.class_management.title" = "Class Management";
"settings.class_management.create_button" = "Create Class";
"settings.class_management.config_button" = "Configure";
"settings.class_management.options_button" = "Options";
"settings.class_management.empty.title" = "No Classes";
"settings.class_management.empty.description" = "Tap the top right corner to create your first class";
"settings.class_management.student_count" = "%d students";

"settings.function_config.title" = "Function Configuration";
"settings.function_config.rule.title" = "Rules Library";
"settings.function_config.rule.description" = "Configure common point rules";
"settings.function_config.reward.title" = "Rewards Library";
"settings.function_config.reward.description" = "Configure exchange rewards";
"settings.function_config.lottery.title" = "Lottery Tools";
"settings.function_config.lottery.description" = "Configure wheel, mystery box and scratch card rewards";

// MARK: - Profile
"profile.title" = "Profile";
"profile.user_info.id_label" = "ID: %@";
"profile.user_info.membership_expires" = "Member Expires: %@";
"profile.subscription.banner_text" = "Upgrade membership to unlock exclusive features";
"profile.subscription.view_plans_button" = "View Plans";
"profile.subscription.card_title" = "Profile Card";

// MARK: - System Settings Items
"settings.item.language" = "Language";
"settings.item.feedback" = "Help & Feedback";
"settings.item.about" = "About";
"settings.item.user_agreement" = "User Agreement";
"settings.item.privacy_policy" = "Privacy Policy";
"settings.item.children_privacy_policy" = "Children's Personal Information Protection Policy";
"login.agreement.children_privacy_policy" = "Children's Personal Privacy Protection Policy";
"settings.item.delete_account" = "Delete Account";
"settings.item.logout" = "Log Out";

// MARK: - Help & Feedback
"feedback.contact_email.title" = "Contact Us";
"feedback.contact_email.message" = "Contact Email: <EMAIL>";
"feedback.contact_email.confirm" = "OK";

// MARK: - Student Detail Page
"student_detail.history.points" = "Point Records";
"student_detail.history.exchange" = "Exchange Records";
"student_detail.history.empty.title" = "No Records";
"student_detail.history.empty.description" = "Student point activity records will appear here";
"student_detail.actions.add_points" = "Add Points";
"student_detail.actions.deduct_points" = "Deduct Points";
"student_detail.actions.exchange" = "Exchange";
"student_detail.actions.lottery" = "Lottery";
"student_detail.actions.analysis" = "Analysis Report";

// MARK: - Subscription Page
"subscription.membership.basic" = "Basic Member";
"subscription.membership.premium" = "Premium Member";
"subscription.subscribe_button" = "Subscribe Now";
"subscription.agreement_notice" = "Please read the Membership Service Agreement before subscribing";
"subscription.agreement_prompt" = "Please read before subscribing";
"subscription.agreement_link" = "Membership Service Agreement";
"subscription.user_info.not_member" = "Not a member, unable to unlock all features";

// MARK: - Navigation and Tabs
"tab.home" = "Home";
"tab.settings" = "Settings";
"tab.profile" = "Profile";
"tab.current_selection" = "Current: %d";

// MARK: - Placeholder Page Text
"placeholder.settings.title" = "Settings Page";
"placeholder.profile.title" = "Profile Page";
"placeholder.developing" = "Under Development...";

// MARK: - Class Selector
"class_selector.preview" = "Class Selector Preview";

// MARK: - App Title
"app.title" = "TuanTuanZhuan";

// MARK: - Login Page
"login.subtitle" = "Class points management tool designed for teachers";
"login.agreement.prefix" = "I have read and agreed ";
"login.agreement.user_agreement" = "User Agreement";
"login.agreement.and" = " and ";
"login.agreement.privacy_policy" = "Privacy Policy";
"login.error.title" = "Login Error";
"login.error.failed" = "Login failed, please try again";
"login.error.invalid_credential" = "Invalid login credentials";
"login.error.invalid_response" = "Invalid login response";
"login.error.not_handled" = "Login request not handled";
"login.error.unknown" = "Unknown login error";
"login.error.general" = "An error occurred during login, please try again later";
"login.webview.placeholder" = "Web content will be displayed here";
"common.button.ok" = "OK";
"common.button.close" = "Close";

// MARK: - Language Switch
"language.restart.title" = "Restart App";
"language.restart.message" = "Language change requires app restart to take effect. Continue?";
"language.selection.title" = "Select Language";
"language.selection.description" = "Choose the app interface language. Restart required after change";

// MARK: - Date Range Selection
"date_range.this_week" = "This Week";
"date_range.this_month" = "This Month";
"date_range.custom" = "Custom";
"date_range.select_range" = "Select Time Range";
"date_range.start_date" = "Start Date";
"date_range.end_date" = "End Date";
"date_range.invalid_range" = "End date cannot be earlier than start date";
"date_range.total_score_format" = "Total Class Points in %@";

// MARK: - Common Actions
"common.cancel" = "Cancel";
"common.confirm" = "Confirm";

// MARK: - Add Student Feature
"add_student.no_class.title" = "No Class Created";
"add_student.no_class.message" = "You haven't created any classes yet, cannot add students";
"add_student.no_class.instruction" = "Please create a class first, then add students";
"add_student.no_class.create_hint" = "You can create classes in Settings";
"add_student.no_class.create_button" = "Go Create Class";

"add_student.menu.title" = "Choose Add Method";
"add_student.menu.manual" = "Manual Add";
"add_student.menu.manual_description" = "Fill in student info individually";
"add_student.menu.excel_import" = "Batch Import";
"add_student.menu.excel_description" = "Import from Excel file";

"add_student.form.title" = "Add Students";
"add_student.form.student_number_label" = "Student %@";
"add_student.form.name" = "Name";
"add_student.form.name_placeholder" = "Enter student name";
"add_student.form.student_number" = "Student ID";
"add_student.form.number_placeholder" = "Enter student ID";
"add_student.form.gender" = "Gender";
"add_student.form.gender_male" = "Male";
"add_student.form.gender_female" = "Female";
"add_student.form.initial_points" = "Points";
"add_student.form.submit" = "Add";
"add_student.form.submitting" = "Adding...";

"add_student.excel.title" = "Excel Import";
"add_student.excel.description" = "Select an Excel file containing student information for batch import";
"add_student.excel.format_hint" = "File must contain four columns: Name, Student ID, Gender, Points";
"add_student.excel.format_example" = "Format Example:";
"add_student.excel.select_file" = "Select Excel File";
"add_student.excel.supported_formats" = "Supports CSV format files";
"add_student.excel.selected_file" = "Selected file:";
"add_student.excel.import_button" = "Start Import";
"add_student.excel.importing" = "Importing...";
"add_student.excel.file_selection_error" = "File selection failed, please try again";
"add_student.excel.read_error" = "File read failed, please check file format";
"add_student.excel.parse_error" = "File parsing failed, please check file content format";

// MARK: - Student Form Validation
"student.form.validation.name_empty" = "Name cannot be empty";
"student.form.validation.name_too_long" = "Name cannot exceed 20 characters";
"student.form.validation.number_empty" = "Student ID cannot be empty";
"student.form.validation.number_too_long" = "Student ID cannot exceed 15 characters";
"student.form.validation.points_invalid" = "Please enter a valid point value";
"student.form.validation.points_negative" = "Initial points cannot be negative";
"student.form.validation.points_too_high" = "Initial points cannot exceed 1000";
"student.form.validation.row_error" = "Row %@: %@";
"student.form.validation.duplicate_number" = "Student ID %@ is duplicated";

// MARK: - Excel Parse Errors
"excel.parse.error.invalid_encoding" = "File encoding format not supported";
"excel.parse.error.empty_file" = "File content is empty";
"excel.parse.error.invalid_headers" = "File format error. Expected columns: %@, Found: %@";
"excel.parse.error.no_valid_data" = "No valid student data found in file";
"excel.parse.error.validation_errors" = "Data validation failed:\n%@";
"excel.parse.error.duplicate_numbers" = "Duplicate student IDs found in file: %@";
"excel.parse.error.insufficient_fields" = "Row %@ data incomplete, requires 4 columns, found %@";
"excel.parse.error.invalid_data" = "Row %@ data invalid: %@";

"excel.parse.recovery.invalid_encoding" = "Please ensure file is UTF-8 encoded CSV format";
"excel.parse.recovery.empty_file" = "Please select a file containing student data";
"excel.parse.recovery.invalid_headers" = "Please ensure first row contains: Name, Student ID, Gender, Points";
"excel.parse.recovery.no_valid_data" = "Please check file format and data content";
"excel.parse.recovery.validation_errors" = "Please correct data errors and re-import";
"excel.parse.recovery.duplicate_numbers" = "Please ensure student IDs are unique";

// MARK: - Add Student Error Messages
"add_student.error.no_class" = "No class currently selected";
"add_student.error.duplicate_numbers" = "Following student IDs already exist: %@";
"add_student.error.partial_failure" = "Partial student addition failed: %@ succeeded, %@ failed";

// MARK: - Create Class Feature
"create_class.permission_denied.title" = "Permission Denied";
"create_class.permission_denied.message" = "You are currently a %@ user, can create up to %d classes. Already created %d classes, cannot create more.";
"create_class.permission_denied.upgrade_button" = "Upgrade Membership";
"create_class.permission_denied.free_user" = "Free";
"create_class.permission_denied.basic_user" = "Basic Member";
"create_class.permission_denied.premium_user" = "Premium Member";

"create_class.dialog.title" = "Create Class";
"create_class.dialog.message" = "Please enter class name";
"create_class.dialog.placeholder" = "e.g., Grade 1 Class 1";
"create_class.dialog.create_button" = "Create";

// MARK: - Class Reset Feature
"class_reset.options.title" = "Class Actions";
"class_reset.options.config" = "Class Configuration";
"class_reset.options.config_description" = "Configure rules and rewards";
"class_reset.options.reset_points" = "Reset Student Points";
"class_reset.options.reset_points_description" = "Reset all students' points to specified value";
"class_reset.options.reset_history" = "Reset History Records";
"class_reset.options.reset_history_description" = "Clear all students' history records";

"class_reset.points.title" = "Reset Student Points";
"class_reset.points.description" = "Reset all students' points in %@ to specified value";
"class_reset.points.student_count" = "Will affect %d students";
"class_reset.points.input_label" = "New Points Value";
"class_reset.points.input_placeholder" = "Enter integer between 0-1000";
"class_reset.points.confirm_button" = "Confirm Reset";
"class_reset.points.resetting" = "Resetting...";

"class_reset.history.title" = "Reset History Records";
"class_reset.history.description" = "This will clear all history records for students in %@";
"class_reset.history.warning" = "⚠️ This action cannot be undone";
"class_reset.history.details" = "Will delete %d records from %d students";
"class_reset.history.confirm_button" = "Confirm Clear";
"class_reset.history.resetting" = "Clearing...";

"class_reset.validation.points_empty" = "Please enter points value";
"class_reset.validation.points_invalid" = "Please enter a valid number";
"class_reset.validation.points_negative" = "Points cannot be negative";
"class_reset.validation.points_too_high" = "Points cannot exceed 1000";

"class_reset.success.points_title" = "Points Reset Successfully";
"class_reset.success.points_message" = "Reset \"%@\": %d students' points are now %d";
"class_reset.success.history_title" = "History Cleared Successfully";
"class_reset.success.history_message" = "Cleared \"%@\": %d history records removed";

"class_reset.error.points_failed" = "Points reset failed: %@";
"class_reset.error.history_failed" = "History clear failed: %@";
"class_reset.error.no_students" = "No students in class";
"create_class.dialog.cancel_button" = "Cancel";

// MARK: - Delete Class Feature
"delete_class.button.title" = "Delete";
"delete_class.confirmation.title" = "Delete Class";
"delete_class.confirmation.message" = "Are you sure you want to delete class \"%@\"?\nDeleting the class will also delete all student data in the class. This action cannot be undone.";
"delete_class.confirmation.confirm_button" = "Confirm Delete";
"delete_class.confirmation.cancel_button" = "Cancel";

"create_class.validation.name_empty" = "Class name cannot be empty";
"create_class.validation.name_too_long" = "Class name cannot exceed 30 characters";
"create_class.validation.name_exists" = "Class name already exists, please choose a different name";

"create_class.success.message" = "Class created successfully";
"create_class.error.save_failed" = "Failed to create class, please try again";

// MARK: - Class Operation Feature
"class_operation.options.title" = "Class Operations";
"class_operation.options.add_points" = "Add Points to All";
"class_operation.options.add_description" = "Add points to all students in current class";
"class_operation.options.deduct_points" = "Deduct Points from All";
"class_operation.options.deduct_description" = "Deduct points from all students in current class";

"class_operation.form.add_title" = "Add Points to All";
"class_operation.form.deduct_title" = "Deduct Points from All";
"class_operation.form.name_label" = "Operation Name";
"class_operation.form.name_placeholder" = "e.g. Good classroom performance";
"class_operation.form.value_label" = "Points";
"class_operation.form.value_placeholder" = "Enter points value";
"class_operation.form.submit" = "Confirm";
"class_operation.form.submitting" = "Processing...";

"class_operation.type.add" = "Add Points";
"class_operation.type.deduct" = "Deduct Points";

"class_operation.validation.name_empty" = "Operation name cannot be empty";
"class_operation.validation.name_too_long" = "Operation name cannot exceed 20 characters";
"class_operation.validation.value_empty" = "Points value cannot be empty";
"class_operation.validation.value_invalid" = "Please enter a valid points value";
"class_operation.validation.value_positive" = "Points value must be positive";
"class_operation.validation.value_too_high" = "Points value cannot exceed 100";

"class_operation.error.no_class_selected" = "No class selected";

// MARK: - Student Points Operations Feature
"student_points.options.add_title" = "Add Points to Student";
"student_points.options.deduct_title" = "Deduct Points from Student";
"student_points.options.frequent_rules_title" = "Frequent Rules";
"student_points.options.no_frequent_rules" = "No frequent rules available\nConfigure rules in Settings";
"student_points.options.custom_option" = "Custom";

"student_points.form.title" = "Custom Points Operation";
"student_points.form.items_title" = "Operation Items";
"student_points.form.add_item" = "Add Item";
"student_points.form.name_label" = "Operation Name";
"student_points.form.name_placeholder" = "e.g., Excellent classroom performance";
"student_points.form.value_label" = "Points Value";
"student_points.form.value_placeholder" = "e.g., 5";
"student_points.form.preview_title" = "Operation Preview";
"student_points.form.cancel" = "Cancel";
"student_points.form.confirm" = "Confirm";
"student_points.form.validation_errors" = "Input Errors";

"student_points.validation.name_empty" = "Operation name cannot be empty";
"student_points.validation.value_empty" = "Points value cannot be empty";
"student_points.validation.value_invalid" = "Please enter a valid points value";
"student_points.validation.value_positive" = "Points value must be positive";

// MARK: - Redemption Feature
"redemption.operation.success" = "Redemption successful";
"redemption.operation.failed" = "Redemption failed";
"redemption.operation.insufficient" = "Insufficient points";
"redemption.operation.invalid" = "Invalid redemption";

// MARK: - Redemption Options Popup
"redemption.options.title" = "Redeem Prizes";
"redemption.options.current_points" = "Current Points: %@ pts";
"redemption.options.prizes_title" = "Available Prizes";
"redemption.options.points_unit" = "pts";
"redemption.options.insufficient" = "Insufficient";
"redemption.options.no_prizes" = "No prizes available";
"redemption.options.or" = "or";
"redemption.options.custom_title" = "Custom Redemption";
"redemption.options.custom_button_title" = "Custom Redemption";
"redemption.options.custom_button_description" = "Redeem other items";
"redemption.options.custom_subtitle" = "Fill in custom redemption items";
"redemption.options.success_message" = "Redeemed Successfully!";

// MARK: - Redemption Form
"redemption.form.title" = "Custom Redemption";
"redemption.form.info.title" = "Redemption Info";
"redemption.form.info.subtitle" = "Current Points: %@ pts";
"redemption.form.items.title" = "Redemption Items";
"redemption.form.item.title" = "Item %@";
"redemption.form.item.name" = "Item Name";
"redemption.form.item.name.placeholder" = "Enter item name";
"redemption.form.item.cost" = "Points Cost";
"redemption.form.item.cost.placeholder" = "Enter points cost";
"redemption.form.add_item" = "Add Item";
"redemption.form.summary.title" = "Redemption Summary";
"redemption.form.summary.total" = "Total Cost";
"redemption.form.summary.points" = "pts";
"redemption.form.confirm" = "Confirm";

// MARK: - Redemption Form Validation
"redemption.form.error.no_valid_items" = "No valid redemption items";
"redemption.form.error.empty_name" = "Item %@ name cannot be empty";
"redemption.form.error.invalid_cost" = "Item %@ points cost is invalid";
"redemption.form.error.insufficient_points" = "Insufficient points, need %@ pts, current %@ pts";

// MARK: - Lottery Tool Configuration Feature
"lottery_tool_config.title" = "Lottery Tool Configuration";
"lottery_tool_config.select_class.title" = "Select Class";
"lottery_tool_config.select_class.description" = "Please select the class to configure lottery tools";
"lottery_tool_config.select_tool_type.title" = "Select Lottery Tool";
"lottery_tool_config.select_tool_type.description" = "Please select the type of lottery tool to configure";

"lottery_tool_config.tool_type.wheel" = "Wheel";
"lottery_tool_config.tool_type.box" = "Mystery Box";
"lottery_tool_config.tool_type.scratch" = "Scratch Card";

"lottery_tool_config.tool_description.wheel" = "Classic wheel lottery, 4-12 selectable, exciting and fun";
"lottery_tool_config.tool_description.box" = "Mysterious box lottery, 1-20 selectable, full of surprises";
"lottery_tool_config.tool_description.scratch" = "Scratch card lottery, 1-20 selectable, realistic touch";

"lottery_tool_config.status.configured" = "Configured";
"lottery_tool_config.status.not_configured" = "Not Configured";
"lottery_tool_config.action.configure" = "Tap to start configuration";
"lottery_tool_config.action.modify" = "Tap to modify configuration";

"lottery_tool_config.form.basic_config" = "Basic Configuration";
"lottery_tool_config.form.item_config" = "Item Configuration";
"lottery_tool_config.form.section_config" = "Section Configuration";
"lottery_tool_config.form.item_count" = "Tool Count";
"lottery_tool_config.form.section_count" = "Section Count";
"lottery_tool_config.form.cost_per_play" = "Points Cost Per Play";
"lottery_tool_config.form.prize_name" = "Prize Name";
"lottery_tool_config.form.fixed_sections" = "Wheel supports 4-12 selectable sections";
"lottery_tool_config.form.suggested_range" = "Suggested range: 1-50 points";
"lottery_tool_config.form.range_format" = "Range: %d - %d";

"lottery_tool_config.item.section_prefix" = "Section";
"lottery_tool_config.item.box_prefix" = "Box";
"lottery_tool_config.item.scratch_prefix" = "Card";
"lottery_tool_config.item.section_title" = "Section %d";
"lottery_tool_config.item.box_title" = "Box %d";
"lottery_tool_config.item.scratch_title" = "Card %d";
"lottery_tool_config.item.section_placeholder" = "Enter prize name for section %d";
"lottery_tool_config.item.box_placeholder" = "Enter prize name for box %d";
"lottery_tool_config.item.scratch_placeholder" = "Enter prize name for card %d";

"lottery_tool_config.validation.error_title" = "Configuration Error";
"lottery_tool_config.validation.item_count_invalid" = "Tool count must be between %d and %d";
"lottery_tool_config.validation.cost_invalid" = "Cost per play must be greater than 0";
"lottery_tool_config.validation.cost_too_high" = "Cost per play cannot exceed 1000";
"lottery_tool_config.validation.prize_name_empty" = "There are %d %@ with empty prize names";
"lottery_tool_config.validation.prize_name_too_long" = "Prize name for %d %@ cannot exceed 20 characters";
"lottery_tool_config.validation.duplicate_prize_names" = "Duplicate prize names found, please check and modify";

"lottery_tool_config.result.save_success" = "Configuration saved!";
"lottery_tool_config.result.update_success" = "Configuration updated!";
"lottery_tool_config.result.save_success_title" = "Configuration Success";
"lottery_tool_config.result.update_success_title" = "Update Success";
"lottery_tool_config.result.save_failed" = "Save failed: %@";

"lottery_tool_config.empty_state.no_classes" = "No classes available";
"lottery_tool_config.empty_state.create_class_first" = "Please create a class first, then configure lottery tools";
"lottery_tool_config.empty_state.no_matching_classes" = "No matching classes found";
"lottery_tool_config.empty_state.try_other_keywords" = "Please try other search keywords";

"lottery_tool_config.class_info.current_class" = "Current Class";
"lottery_tool_config.class_info.student_count" = "Student Count";
"lottery_tool_config.class_info.student_count_format" = "%d students";
"lottery_tool_config.class_info.configured_tools" = "%d lottery tools configured";
"lottery_tool_config.class_info.total_points" = "%d total points";

"lottery_tool_config.button.save" = "Save";
"lottery_tool_config.button.complete" = "Complete";
"lottery_tool_config.button.cancel" = "Cancel";
"lottery_tool_config.button.back" = "Back";
"lottery_tool_config.button.clear" = "Clear";
"lottery_tool_config.button.search_placeholder" = "Search class name";

// MARK: - Configuration Info Formatting
"lottery_tool_config.config_info.item_count_format" = "%d items";
"lottery_tool_config.config_info.cost_per_play_format" = "%d pts/play";

// MARK: - Navigation Tab Bar
"tab.home" = "Home";
"tab.settings" = "Settings";
"tab.profile" = "Profile";
"tab.current_selection_format" = "Current: %d";

// MARK: - Student Grid and Management
"student_grid.no_class.title" = "No class created yet";
"student_grid.no_class.description" = "Please create a class first, then add students";
"student_grid.no_class.create_button" = "Create Class";
"student_grid.search_placeholder" = "Search student name or...";
"student_grid.unknown_name" = "Unknown";
"student_grid.unnamed_class" = "Unnamed Class";

// MARK: - Excel Import Interface
"excel_import.headers.name" = "Name";
"excel_import.headers.student_id" = "Student ID";
"excel_import.headers.gender" = "Gender";
"excel_import.headers.initial_points" = "Points";
"excel_import.example.name1" = "Zhang San";
"excel_import.example.name2" = "Li Si";
"excel_import.example.gender_male" = "Male";
"excel_import.example.gender_female" = "Female";

// MARK: - Scratch Card Interface
"scratch_card.title" = "Scratch Cards";
"scratch_card.remaining_format" = "Remaining: %d cards";
"scratch_card.cost_format" = "Per card: %d points";
"scratch_card.current_points_format" = "Current points: %d";
"scratch_card.points_sufficient" = "Points sufficient";
"scratch_card.points_insufficient" = "Points insufficient";
"scratch_card.loading" = "Loading scratch card configuration...";
"scratch_card.no_config" = "No scratch card configuration";
"scratch_card.config_description" = "Please configure scratch card rewards in settings";
"scratch_card.go_settings" = "Go to Settings";
"scratch_card.back" = "Back";
"scratch_card.happy_title" = "Happy Scratch Cards";
"scratch_card.happy_subtitle" = "Scratch the surface to discover surprise rewards";
"scratch_card.hint_text" = "Scratch here";
"scratch_card.congratulations" = "Congratulations!";
"scratch_card.big_reveal" = "Scratch card jackpot revealed";
"scratch_card.points_consumed_format" = "Points consumed: %d";
"scratch_card.confirm_claim" = "Confirm Claim";
"scratch_card.insufficient_points_title" = "Insufficient Points";
"scratch_card.insufficient_points_message_format" = "Scratch card requires %d points, current points: %d";

// MARK: - Scratch Card Status
"scratch_card.status.available" = "Available";
"scratch_card.status.scratched" = "Scratched";
"scratch_card.status.selected" = "Selected";
"scratch_card.status.scratching" = "Scratching";
"scratch_card.status.revealing" = "Revealing";
"scratch_card.status.completed" = "Completed";
"scratch_card.status.prize_reveal" = "Discover Surprise";
"scratch_card.status.won" = "Won";
"scratch_card.status.congratulations" = "Congratulations";

// MARK: - Blind Box Interface
"blind_box.title" = "Mystery Box";
"blind_box.no_config" = "No mystery box configuration";
"blind_box.loading" = "Loading mystery box configuration...";
"blind_box.config_description" = "Please configure mystery box rewards in settings first";
"blind_box.go_config" = "Go to Configure";
"blind_box.stats.total" = "Total";
"blind_box.stats.unopened" = "Unopened";
"blind_box.stats.opened" = "Opened";
"blind_box.stats.cost_per_open" = "Per open";
"blind_box.item_format" = "Mystery Box %d";
"blind_box.status.waiting" = "Waiting";
"blind_box.status.opening" = "Opening";
"blind_box.status.completed" = "Completed";
"blind_box.status.opened" = "Opened";
"blind_box.congratulations" = "🎉 Congratulations! 🎉";
"blind_box.cost_info" = "Points consumed";
"blind_box.points_format" = "%d points";
"blind_box.obtained" = "Obtained";
"blind_box.insufficient_points_title" = "Insufficient Points";
"blind_box.insufficient_points_message_format" = "Opening mystery box requires %d points, current points: %d";
"blind_box.no_config_title" = "Mystery box not configured";
"blind_box.no_config_message" = "Please configure mystery box rewards in settings first";

// MARK: - Lottery Wheel Interface  
"lottery_wheel.title" = "Lucky Wheel";
"lottery_wheel.page_title" = "Lucky Wheel";
"lottery_wheel.back_button" = "Back";
"lottery_wheel.current_points" = "%d pts";
"lottery_wheel.no_config.title" = "No Wheel Configuration";
"lottery_wheel.no_config.description" = "This class hasn't configured the wheel lottery yet";
"lottery_wheel.go_settings" = "Go to Settings";
"lottery_wheel.mystery_prize" = "Mystery Prize";
"lottery_wheel.empty_prize" = "Empty Prize";
"lottery_wheel.female_avatar" = "Female Avatar";
"lottery_wheel.male_avatar" = "Male Avatar";
"lottery_wheel.fireworks" = "Fireworks";
"lottery_wheel.cost_info" = "Cost per play: %d pts";
"lottery_wheel.affordable" = "Points sufficient";
"lottery_wheel.insufficient_points" = "Points insufficient";
"lottery_wheel.confirm_result" = "Claim Prize";
"lottery_wheel.insufficient_points_title" = "Insufficient Points";
"lottery_wheel.insufficient_points_message" = "Wheel spin requires %@ points, but you only have %@ points";
"lottery_wheel.empty_prizes.title" = "No Prizes Set";
"lottery_wheel.empty_prizes.description" = "Please configure prizes for the wheel in Settings";
"lottery_wheel.spin_button" = "Spin";
"lottery_wheel.spinning" = "Spinning...";

// MARK: - Class Configuration
"class_config.unnamed_class" = "Unnamed Class";
"class_config.configured_tools_format" = "Configured %d lottery tools";
"class_config.student_count_with_unit" = "%d students";
"class_config.unknown_class" = "Unknown Class";

// MARK: - Rule and Prize Configuration
"rule_config.type.add_points" = "Add Points";
"rule_config.type.deduct_points" = "Deduct Points";
"prize_config.type.virtual" = "Virtual";
"prize_config.type.physical" = "Physical";
"prize_config.form.type_label" = "Prize Type";
"prize_config.cost_format" = "%d points - %@";

// MARK: - Loading and Progress States
"common.loading" = "Loading...";
"common.loading_config" = "Loading configuration...";
"common.return" = "Return";
"common.go_back" = "Go Back";

// MARK: - Subscription Interface
"subscription.not_member" = "Not a member, unable to unlock all features";
"subscription.crown_icon" = "Crown (Subscription)";
"subscription.premium_user" = "Premium User";
"subscription.regular_user" = "Regular User";
"subscription.not_activated" = "Not activated";

// MARK: - User Interface
"user_info.teacher_nickname" = "Teacher";
"user_info.premium_member" = "Premium Member";

// MARK: - Form Data Models 
"form_data.item_range_format" = "Range: %d - %d";
"form_data.section_prefix" = "Section";
"form_data.box_prefix" = "Mystery Box";  
"form_data.scratch_prefix" = "Scratch Card";
"form_data.section_title_format" = "Section %d";
"form_data.box_title_format" = "Mystery Box %d";
"form_data.scratch_title_format" = "Scratch Card %d";
"form_data.section_placeholder_format" = "Enter prize name for section %d";
"form_data.box_placeholder_format" = "Enter prize name for mystery box %d";
"form_data.scratch_placeholder_format" = "Enter prize name for scratch card %d";

// MARK: - Validation Messages
"validation.item_count_range" = "Tool count must be between %d and %d";
"validation.cost_positive" = "Points cost per play must be greater than 0";
"validation.cost_too_high" = "Points cost per play cannot exceed 1000";
"validation.prize_name_length" = "Prize name for %d %@ cannot exceed 20 characters";
"validation.empty_prize_names" = "%d %@ prize names are still empty";
"validation.duplicate_prize_names" = "Duplicate prize names exist, please check and modify";

// MARK: - Preview Data
"preview_data.preview_user" = "Preview User";
"preview_data.grade_class" = "Grade 3 Class 1";
"preview_data.student_format" = "Student %d";
"preview_data.male" = "Male";
"preview_data.female" = "Female";
"preview_data.big_wheel" = "Big Wheel";
"preview_data.zhang_xiaoming" = "Zhang Xiaoming";
"preview_data.grade_three_class_one" = "Grade 3 Class 1";
"preview_data.mysterious_prize" = "Mysterious Prize";
"preview_data.little_red_flower" = "Little Red Flower";
"preview_data.prize_format" = "Prize %d";

// MARK: - Student Detail Action Buttons
"student_detail.action.add_points" = "Add Points";
"student_detail.action.deduct_points" = "Deduct Points";
"student_detail.action.exchange" = "Exchange";
"student_detail.action.lottery" = "Lottery";
"student_detail.action.analysis" = "Analysis";

// MARK: - Gender
"gender.male" = "Male";
"gender.female" = "Female";
"gender.avatar.male" = "男生头像";
"gender.avatar.female" = "女生头像";

// MARK: - Rule Configuration Form
"rule_config.form.configured_rules" = "Configured Rules";

// MARK: - Rule Configuration List
"rule_config.configured_list.points_unit" = "pts";
"rule_config.configured_list.add_label" = "Add";
"rule_config.configured_list.deduct_label" = "Deduct";

// MARK: - Rule Count Limits
"rule_config.max_count_format" = "Max %@";
"rule_config.limit.free_user" = "Free users can set up to 5 rules";
"rule_config.limit.paid_user" = "Paid users can set up to 10 rules";

// MARK: - Student Points Additional
"student_points.options.custom_description_add" = "Custom points addition reason and value";
"student_points.options.custom_description_deduct" = "Custom points deduction reason and value";
"student_points.form.description_add" = "Custom points addition reason and value";
"student_points.form.description_deduct" = "Custom points deduction reason and value";
"student_points.form.submitting" = "Submitting...";

// MARK: - Blind Box Additional
"blind_box.page_title" = "Blind Box Opening";
"blind_box.loading_config" = "Loading blind box configuration...";
"blind_box.back_button" = "Back";
"blind_box.result.congratulations_text" = "🎉 Congratulations! 🎉";
"blind_box.result.cost_label_text" = "Points Used";
"blind_box.result.obtained_label_text" = "Obtained";
"blind_box.result.confirm_button_text" = "Confirm";
"blind_box.alert.insufficient_points_title_text" = "Insufficient Points";
"blind_box.alert.insufficient_points_message_text" = "Opening blind box requires %@ points, current points: %@";
"blind_box.alert.confirm_button_text" = "OK";

// MARK: - Lottery Tool Config Additional
"lottery_tool_config.wheel_title" = "Wheel Configuration";
"lottery_tool_config.box_title" = "Blind Box Configuration";
"lottery_tool_config.scratch_title" = "Scratch Card Configuration";
"lottery_tool_config.section_count" = "Section Count";
"lottery_tool_config.box_count" = "Blind Box Count";
"lottery_tool_config.scratch_count" = "Scratch Card Count";
"lottery_tool_config.section_config" = "Section Configuration";
"lottery_tool_config.box_config" = "Blind Box Configuration";
"lottery_tool_config.scratch_config" = "Scratch Card Configuration";
"lottery_tool_config.section_number" = "Section %d";
"lottery_tool_config.box_number" = "Blind Box %d";
"lottery_tool_config.scratch_number" = "Scratch Card %d";

// MARK: - Subscription Additional
"subscription.user_level_regular" = "(Regular User)";
"subscription.user_level_premium" = "(Premium User)";
"subscription.not_activated_message" = "Not activated, unable to unlock all features";
"subscription.membership.basic" = "Basic Member";
"subscription.membership.premium" = "Premium Member";
"subscription.feature.manage_classes_two" = "Manage up to 2 classes";
"subscription.feature.unlock_wheel" = "Unlock wheel tool";
"subscription.feature.multi_device_sync" = "Configure more common class rules";
"subscription.feature.basic_functions" = "Student point management, prize redemption and other basic functions";
"subscription.feature.all_basic_benefits" = "All basic member benefits";
"subscription.feature.manage_classes_five" = "Manage up to 5 classes";
"subscription.feature.unlock_box_scratch" = "Unlock blind box and scratch card tools";
"subscription.feature.ai_reports" = "Unlock AI-generated analysis reports";
"subscription.pricing.monthly" = "Monthly";
"subscription.pricing.yearly" = "Yearly";
"subscription.subscribe_button" = "Subscribe Now";
"subscription.agreement_notice" = "Please read the Membership Service Agreement before subscribing";

// MARK: - Lottery Tool Config Form Additional
"lottery_tool_config.tool_type.wheel" = "Sector";
"lottery_tool_config.tool_type.box" = "Box";
"lottery_tool_config.tool_type.scratch" = "Scratch Card";
"lottery_tool_config.form.item_count_title" = "%@ Count";
"lottery_tool_config.form.item_count_range" = "Range: %@ - %@";
"lottery_tool_config.form.item_config_title" = "%@ Configuration";
"lottery_tool_config.item.display_title_wheel" = "Sector %@";
"lottery_tool_config.item.display_title_box" = "Box %@";
"lottery_tool_config.item.display_title_scratch" = "Scratch Card %@";
"lottery_tool_config.item.placeholder_wheel" = "Enter prize name for sector %@";
"lottery_tool_config.item.placeholder_box" = "Enter prize name for box %@";
"lottery_tool_config.item.placeholder_scratch" = "Enter prize name for scratch card %@";
"lottery_tool_config.validation.prize_name_too_long" = "Prize name for %@ %@ cannot exceed 20 characters";
"lottery_tool_config.validation.empty_prize_names" = "%@ %@ still have empty prize names";
"lottery_tool_config.validation.duplicate_prize_names" = "Duplicate prize names found, please check and modify";

// MARK: - AI Analysis Report
"ai_analysis.title" = "AI Analysis Report";
"ai_analysis.report_title" = "Student Behavior Analysis Report";
"ai_analysis.parent_feedback_title" = "Parent Feedback";
"ai_analysis.analysis_content" = "Analysis Content";
"ai_analysis.professional_analysis" = "Professional Analysis Content";
"ai_analysis.feedback_content" = "Communication Feedback Content";
"ai_analysis.copy_report" = "Copy Report";
"ai_analysis.copy_professional" = "Copy Analysis Report";
"ai_analysis.copy_feedback" = "Copy Parent Feedback";
"ai_analysis.save_tip" = "Analysis report will not be saved and will disappear when you return. Please save it by copying.";
"ai_analysis.student_info" = "Student Info";
"ai_analysis.generated_time" = "Generated Time";
"ai_analysis.total_records" = "Total Records";
"ai_analysis.positive_records" = "Positive Records";
"ai_analysis.negative_records" = "Negative Records";
"ai_analysis.positive_ratio" = "Positive Behavior Ratio";
"ai_analysis.record_count" = "Record Count";
"ai_analysis.current_points" = "Current Points";
"ai_analysis.unknown_student" = "Unknown Student";
"ai_analysis.generate_button" = "Generate Report";
"ai_analysis.no_network" = "Network unavailable. Please check your connection and try again";
"ai_analysis.generating" = "AI is generating report...";
"ai_analysis.please_wait" = "This might take a few seconds, please wait";
"ai_analysis.copy_success" = "Report copied to clipboard";
"ai_analysis.permission_title" = "Advanced Membership Required";
"ai_analysis.upgrade_button" = "Upgrade";
"ai_analysis.select_report_type" = "Select Report Type";
"ai_analysis.report_type.professional" = "Student Analysis Report";
"ai_analysis.report_type.parent_feedback" = "Parent Feedback";
"ai_analysis.professional_description" = "Professional analysis report";
"ai_analysis.feedback_description" = "Teacher-parent communication";

// Error Messages
"ai_analysis.error.no_user" = "User information not found, please login again";
"ai_analysis.error.no_student" = "Student information not found";
"ai_analysis.error.not_premium" = "AI analysis is only available for advanced members";
"ai_analysis.error.insufficient_records" = "Insufficient records, at least 10 records needed (current: %d)";
"ai_analysis.error.no_network" = "Network connection unavailable, please check your settings";
"ai_analysis.error.network" = "Network error, please check your connection and try again";
"ai_analysis.error.permission_unknown" = "Permission verification failed, please contact support";
"ai_analysis.error.no_report" = "Report does not exist";
"ai_analysis.error.unknown" = "Unknown error";
"ai_analysis.error.generic" = "Failed to generate report";

// Status Descriptions
"ai_analysis.status.checking" = "Verifying permissions...";
"ai_analysis.status.ready" = "Ready to generate AI analysis report";
"ai_analysis.status.unknown" = "Unknown status";

// Recovery Suggestions
"ai_analysis.recovery.upgrade" = "Please upgrade to advanced membership to use this feature";
"ai_analysis.recovery.more_records" = "Please add more records and try again";
"ai_analysis.recovery.check_network" = "Please check your network connection and try again";
"ai_analysis.recovery.try_later" = "Please try again later";
"ai_analysis.recovery.try_again" = "Please try again";
"ai_analysis.recovery.contact_support" = "If the problem persists, please contact support";

// MARK: - Subscription and Permissions
"subscription.upgrade" = "Upgrade";

// MARK: - Lottery Permission Alerts
"lottery.permission.wheel_title" = "Basic Membership Required";
"lottery.permission.wheel_message" = "Lottery wheel requires Basic or Premium Membership";
"lottery.permission.advanced_title" = "Premium Membership Required";
"lottery.permission.advanced_message" = "Mystery Box and Scratch Card features require Premium Membership";

// MARK: - Lottery Menu
"lottery.menu.title" = "Choose Lottery Tool";
"lottery.menu.wheel" = "Lottery Wheel";
"lottery.menu.wheel_description" = "Spin the wheel for random prizes";
"lottery.menu.blind_box" = "Mystery Box";
"lottery.menu.blind_box_description" = "Open boxes to find prizes";
"lottery.menu.scratch_card" = "Scratch Card";
"lottery.menu.scratch_card_description" = "Scratch to reveal prizes";

// MARK: - Logout
"logout.title" = "Log Out";
"logout.message" = "Are you sure you want to log out?";
"logout.cancel" = "Cancel";
"logout.confirm" = "Log Out";

// MARK: - Class Freeze/Unfreeze Dialogs
"class_freeze.title" = "Membership Downgrade";
"class_freeze.subtitle" = "Your membership has been downgraded from %@ to %@";
"class_freeze.instruction" = "Please select %d class(es) to keep active";
"class_freeze.description" = "Other classes will be frozen. Upgrade membership to unfreeze.";
"class_freeze.confirm_button" = "Confirm";
"class_freeze.subscribe_button" = "Subscribe to unlock more classes";
"class_freeze.status.active" = "Active";
"class_freeze.status.will_freeze" = "Will freeze";
"class_freeze.student_count_format" = "%d students · Created on %@";

"class_unfreeze.title" = "Membership Upgrade";
"class_unfreeze.subtitle" = "You have successfully upgraded to %@";
"class_unfreeze.instruction" = "You can unfreeze %d class(es)";
"class_unfreeze.description" = "Please select classes to unfreeze. They'll be available immediately.";
"class_unfreeze.confirm_button" = "Confirm Unfreeze";
"class_unfreeze.status.frozen" = "Frozen";
"class_unfreeze.status.will_unfreeze" = "Will unfreeze";

// Account Deletion Feature
"account_deletion.title" = "Delete Account";
"account_deletion.subtitle" = "Permanently delete your account and all data";
"account_deletion.warning.title" = "⚠️ Important Notice";
"account_deletion.warning.message" = "After deleting your account, all your data will be permanently lost and cannot be recovered, including:\n\n• All class and student information\n• Point records and redemption records\n• Lottery records and historical data\n• Subscription information\n\nThis operation is irreversible, please proceed with caution.";
"account_deletion.first_confirmation.title" = "Confirm Account Deletion";
"account_deletion.first_confirmation.message" = "Are you sure you want to delete your account? This action cannot be undone.";
"account_deletion.second_confirmation.title" = "Final Confirmation";
"account_deletion.second_confirmation.message" = "Final confirmation: Please type \"DELETE\" to confirm the deletion";
"account_deletion.second_confirmation.placeholder" = "Type: DELETE";
"account_deletion.second_confirmation.confirm_text" = "DELETE";
"account_deletion.confirm_button" = "Confirm Delete";
"account_deletion.cancel_button" = "Cancel";
"account_deletion.delete_input_button" = "Delete";
"account_deletion.deleting_progress" = "Deleting account...";
"account_deletion.completed" = "Account deletion completed";

// Deletion Steps
"account_deletion.step.preparing" = "Preparing to delete data...";
"account_deletion.step.marking" = "Marking for multi-device deletion...";
"account_deletion.step.local_data" = "Deleting local data...";
"account_deletion.step.cloud_data" = "Deleting cloud data...";
"account_deletion.step.revoke_login" = "Revoking login credentials...";
"account_deletion.step.final_cleanup" = "Final cleanup...";

// Deletion Errors
"account_deletion.error.user_not_found" = "User information not found";
"account_deletion.error.cloudkit_unavailable" = "CloudKit service unavailable";
"account_deletion.error.backup_failed" = "Data backup failed";
"account_deletion.error.local_deletion_failed" = "Local data deletion failed";
"account_deletion.error.cloud_deletion_failed" = "Cloud data deletion failed";
"account_deletion.error.revocation_failed" = "Login credential revocation failed";
"account_deletion.error.multi_device_conflict" = "Multi-device conflict";

// MARK: - Trial Feature
"trial.banner.available_text" = "You have a limited-time benefit to claim";
"trial.button.claim" = "Claim Now";
"trial.status.available" = "30-day premium trial awaits you";
"trial.status.active" = "%d days remaining in trial";
"trial.status.expired" = "Trial has expired";
"trial.error.already_claimed" = "You have already claimed the trial";
"trial.error.claim_failed" = "Trial claim failed, please try again later";

// MARK: - Trial Modal
"trial.modal.title" = "Limited-Time Benefit";
"trial.modal.message" = "Dear teacher, thank you for your contribution to education. We are gifting you one month of premium membership. Wish you success in your career!";
"trial.modal.benefits_title" = "During the trial, you will enjoy:";
"trial.modal.benefit_1" = "Create up to 5 classes";
"trial.modal.benefit_2" = "Unlock all game features";
"trial.modal.benefit_3" = "AI intelligent analysis reports";
"trial.modal.benefit_4" = "Configure more common class rules";
"trial.modal.claim_button" = "Claim Now";

// MARK: - Subscription Membership
"subscription.membership.premium_trial" = "Premium Member (Trial)";
"account_deletion.error.network_error" = "Network error, please check your connection";
"account_deletion.error.unknown" = "Unknown error";

// Data Statistics
"account_deletion.data_stats.classes" = "Classes";
"account_deletion.data_stats.students" = "Students";
"account_deletion.data_stats.records" = "Records";
"account_deletion.data_stats.total" = "Total";

// Multi-device Notice
"account_deletion.multi_device.title" = "Multi-device Sync";
"account_deletion.multi_device.message" = "Deletion will be synced to all logged-in devices. Please ensure important data is backed up on other devices.";

// MARK: - Profile Misc
"profile.no_email" = "Email not available";

// MARK: - Class Reset - History Dialog (Additional)
"class_reset.history.record_types_title" = "Record types to be cleared:";
"class_reset.history.record_type.points" = "• All points change records";
"class_reset.history.record_type.exchange" = "• All reward redemption records";
"class_reset.history.record_type.lottery" = "• All lottery activity records";
"class_reset.history.notice" = "Note: students' current points will not change, only history records will be cleared";
"class_reset.history.final_confirm_title" = "Final Confirmation";
"class_reset.history.final_cancel" = "Think Again";
"class_reset.history.final_confirm_message" = "Are you sure you want to clear all history records in %@ (%d records)?\n\nThis action will permanently delete all students' points, redemption and lottery records and cannot be undone.";

// MARK: - Class Reset - Points Dialog (Additional)
"class_reset.points.preview_title" = "Preview:";
"class_reset.points.preview_description" = "Each student's points will reset to:";
"class_reset.points.alert_title" = "Confirm Points Reset";
"class_reset.points.alert_message" = "Are you sure to reset points for %@'s %d students to %d pts? This action cannot be undone.";
"common.points_unit" = "pts";

// MARK: - Rule & Prize Form Placeholders
"rule name" = "Rule Name";
"Points" = "Points";
"prize_config.form.title" = "Prize Library Configuration";
"prize_config.form.prize_number_label" = "Prize %@";
"prize_config.form.name_placeholder" = "e.g. Little Red Flower";
"prize_config.form.prize_type" = "Prize Type";
"prize_config.form.cost_placeholder" = "10";
"prize_config.form.submit" = "Add";
"prize_config.form.submitting" = "Adding...";
"prize_config.form.configured_prizes" = "Configured Prizes";

"prize_config.validation.name_empty" = "Prize name cannot be empty";
"prize_config.validation.name_too_long" = "Prize name cannot exceed 20 characters";
"prize_config.validation.cost_empty" = "Points cost cannot be empty";
"prize_config.validation.cost_invalid" = "Please enter a valid points value";
"prize_config.validation.cost_positive" = "Points cost must be greater than 0";
"prize_config.validation.cost_too_high" = "Points cost cannot exceed 1000";
"prize_config.validation.duplicate_name" = "Prize name already exists";
"prize_config.validation.row_error" = "Row %@: %@";
"prize_config.validation.internal_duplicate" = "Duplicate with row %@";

"prize_config.success.title" = "Added Successfully";
"prize_config.success.message" = "Prize successfully added to the prize library";
"prize_config.error.partial_failure" = "Some prizes failed to add: %@ succeeded, %@ failed";

"prize_config.configured_list.title" = "Configured Prizes";
"prize_config.configured_list.empty" = "No configured prizes yet\nGo create the first one";

"prize_config.delete.confirmation.title" = "Delete Prize";
"prize_config.delete.confirmation.message" = "Are you sure to delete prize \"%@\"?\nThis action cannot be undone.";
"prize_config.delete.confirmation.confirm" = "Confirm Delete";
"prize_config.delete.confirmation.cancel" = "Cancel";

// MARK: - RevenueCat Subscription System
"subscription.level.free" = "Free";
"subscription.level.basic" = "Basic Member";
"subscription.level.premium" = "Premium Member";

"subscription.feature.basic_scoring" = "Basic Point Management";
"subscription.feature.prize_exchange" = "Prize Exchange";
"subscription.feature.lottery" = "Wheel Lottery";
"subscription.feature.advanced_games" = "Mystery Box/Scratch Card";
"subscription.feature.ai_analysis" = "AI Analysis Report";
"subscription.feature.multi_device_sync" = "Multi-Device Sync";
"subscription.feature.create_multiple_classes" = "Create Multiple Classes";

"subscription.status.not_subscribed" = "Not Subscribed";
"subscription.valid_until" = "Valid Until";
"subscription.trial_success_message" = "Congratulations! You have successfully claimed a 30-day premium trial!";

// MARK: - Permission Management
"permission.feature.available" = "Feature Available";
"permission.feature.free" = "Free Feature";
"permission.feature.requires_basic" = "Requires Basic Membership";
"permission.feature.requires_premium" = "Requires Premium Membership";

"permission.ai_analysis.requires_premium" = "AI Analysis Report requires Premium membership";
"permission.ai_analysis.insufficient_records" = "Insufficient point records, currently {count} records, need at least 10";

"permission.lottery.wheel_requires_basic" = "Wheel lottery requires Basic membership";
"permission.lottery.advanced_requires_premium" = "Mystery box and scratch card require Premium membership";

"permission.upgrade_hint.free_to_basic" = "Upgrade to Basic membership to unlock more features";
"permission.upgrade_hint.basic_to_premium" = "Upgrade to Premium membership for full functionality";

// MARK: - Purchase Flow
"purchase.agreement.title" = "Purchase Agreement";
"purchase.agreement.message" = "Please read the Membership Service Agreement before purchase";
"purchase.agreement.reminder" = "Please check to agree to the Membership Service Agreement first";
"purchase.in_progress" = "Purchasing...";
"purchase.processing" = "Processing...";
"purchase.success" = "Subscription Successful";
"purchase.success_processing" = "Subscription successful! Redirecting for you...";
"purchase.failed" = "Purchase Failed";
"purchase.restore.success" = "Subscription Status Check Complete!";
"purchase.restore.failed" = "Subscription Status Check Failed";
"subscription.success.message" = "Congratulations! You have successfully subscribed to our membership service!\nYou can now enjoy more features.";

// MARK: - Product Information
"product.loading" = "Loading...";
"product.unavailable" = "Product Unavailable";
"product.monthly" = "Monthly";
"product.yearly" = "Yearly";

// MARK: - Trial Feature
"trial.claim" = "Claim Trial";
"trial.duration" = "30-Day Trial";
"trial.already_claimed" = "You have already used the trial";
"trial.premium_member" = "You are already a premium member";

// MARK: - Trial Subscription Reminder
"trial.reminder.title" = "Friendly Reminder";
"trial.reminder.message" = "Subscribing now will end your trial early. We recommend subscribing after the trial period ends";
"trial.reminder.confirm" = "Thanks for the reminder";

// MARK: - Error Messages
"error.revenuecat.configuration" = "Subscription service configuration failed";
"error.revenuecat.purchase" = "Purchase failed, please try again";
"error.revenuecat.restore" = "Failed to check subscription status";
"error.revenuecat.network" = "Network connection failed, please check your network";
"error.revenuecat.invalid_product" = "Invalid product information";

// MARK: - Class Limit
"class_limit.free.title" = "Class Creation Limited";
"class_limit.free.message" = "Free users can only create 1 class, upgrade to Basic to create 2 classes";
"class_limit.basic.title" = "Class Creation Limited";
"class_limit.basic.message" = "Basic members can create up to 2 classes, upgrade to Premium to create 5 classes";
"class_limit.premium.title" = "Class Creation Limited";
"class_limit.premium.message" = "Premium members can create up to 5 classes";
"class_limit.upgrade_button" = "Upgrade Membership";

// MARK: - Subscription Downgrade Handling
"subscription.downgrade.title" = "Membership Downgrade Notice";
"subscription.downgrade.message" = "Your membership has been downgraded, please select which class to keep managing";
"subscription.downgrade.select_class" = "Select class to keep";
"subscription.downgrade.freeze_others" = "Other classes will be frozen";

"subscription.upgrade.title" = "Membership Upgrade Notice";
"subscription.upgrade.message" = "Your membership has been upgraded, you can unfreeze previously frozen classes";
"subscription.upgrade.unfreeze_classes" = "Unfreeze Classes";

// MARK: - Feature Lock Prompts
"feature_lock.lottery.title" = "Lottery Feature";
"feature_lock.lottery.message" = "Wheel lottery requires Basic membership or higher";
"feature_lock.advanced_games.title" = "Advanced Games";
"feature_lock.advanced_games.message" = "Mystery box and scratch card require Premium membership";
"feature_lock.ai_analysis.title" = "AI Analysis Report";
"feature_lock.ai_analysis.message" = "AI Analysis Report requires Premium membership and student must have at least 10 point records";

// MARK: - Subscription Error Messages (User-Friendly)
"subscription.error.configuration_failed" = "Subscription service initialization failed, please try again later";
"subscription.error.loading_failed" = "Failed to load subscription information, please check your network connection";
"subscription.error.products_not_loaded" = "Product information is loading, please try again later";
"subscription.error.product_not_found" = "Selected product is temporarily unavailable, please try again later";
"subscription.error.purchase_failed" = "Purchase failed, please try again later";
"subscription.error.restore_failed" = "Failed to restore purchases, please try again later";
"subscription.error.user_not_found" = "User information error, please log in again";
"subscription.error.already_premium" = "You are already a premium member";

// MARK: - CloudKit Error Messages
"cloudkit.sync.error" = "Sync issue occurred, please try again later";