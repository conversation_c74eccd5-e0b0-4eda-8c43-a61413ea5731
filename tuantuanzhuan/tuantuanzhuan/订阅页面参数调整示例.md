# 订阅页面参数调整示例

## 📝 参数调整示例

### 示例1：调整背景图位置和大小

如果您希望将背景图向右偏移50像素，向上偏移30像素，并缩小到80%：

```swift
// 在 DesignSystem.swift 的 SubscriptionPage.BackgroundImage 中修改
static var offsetX: CGFloat = 50         // 向右偏移50像素
static var offsetY: CGFloat = -30        // 向上偏移30像素  
static var uniformScale: CGFloat = 0.8   // 缩小到80%
```

### 示例2：添加背景图模糊效果

如果您希望为背景图添加轻微的模糊效果以突出前景内容：

```swift
// 在 DesignSystem.swift 的 SubscriptionPage.BackgroundImage 中修改
static var blur: CGFloat = 3.0           // 添加3像素的模糊效果
static var opacity: Double = 0.8         // 降低透明度到80%
static var brightness: Double = 0.1      // 稍微提高亮度
```

### 示例3：调整分段选项卡的位置

如果您希望将分段选项卡向下移动一些，并增加选项卡的大小：

```swift
// 在 DesignSystem.swift 的 SubscriptionPage.MembershipTab 中修改
static var topOffsetPercentage: CGFloat = 0.35  // 距离顶部35%（原来是25%）
static var tabHeight: CGFloat = 50               // 增加高度到50像素
static var tabWidth: CGFloat = 140               // 增加宽度到140像素
```

### 示例4：美化分段选项卡外观

如果您希望增强分段选项卡的视觉效果：

```swift
// 在 DesignSystem.swift 的 SubscriptionPage.MembershipTab 中修改
static var tabCornerRadius: CGFloat = 20         // 增加圆角
static var shadowRadius: CGFloat = 8             // 增加阴影半径
static var shadowOpacity: Double = 0.4           // 增加阴影不透明度
static var selectedBackgroundOpacity: Double = 0.9  // 增加选中状态背景不透明度
```

### 示例5：调整背景图裁剪区域

如果背景图的顶部和底部有多余的内容需要裁剪：

```swift
// 在 DesignSystem.swift 的 SubscriptionPage.BackgroundImage 中修改
static var cropTop: CGFloat = 0.1        // 裁剪顶部10%
static var cropBottom: CGFloat = 0.15    // 裁剪底部15%
static var cropLeft: CGFloat = 0.05      // 裁剪左侧5%
static var cropRight: CGFloat = 0.05     // 裁剪右侧5%
```

### 示例6：创建动态交互效果

如果您希望增强用户交互体验：

```swift
// 在 DesignSystem.swift 的 SubscriptionPage.MembershipTab 中修改
static var pressedScale: CGFloat = 0.92          // 按下时缩小更多
static var animationDuration: Double = 0.25      // 更快的动画
static var animationSpringResponse: Double = 0.4 // 更快的弹簧响应
static var animationSpringDamping: Double = 0.8  // 更高的阻尼，减少弹跳
```

## 🎨 预设方案

### 方案A：简约风格
```swift
// 背景图设置
static var opacity: Double = 0.6
static var blur: CGFloat = 2.0
static var brightness: Double = 0.15

// 选项卡设置  
static var tabCornerRadius: CGFloat = 12
static var selectedBackgroundOpacity: Double = 0.7
static var shadowRadius: CGFloat = 4
```

### 方案B：醒目风格
```swift
// 背景图设置
static var uniformScale: CGFloat = 1.1
static var contrast: Double = 1.2
static var opacity: Double = 0.9

// 选项卡设置
static var tabHeight: CGFloat = 48
static var tabCornerRadius: CGFloat = 24
static var shadowRadius: CGFloat = 10
static var selectedBackgroundOpacity: Double = 0.95
```

### 方案C：柔和风格
```swift
// 背景图设置
static var blur: CGFloat = 1.5
static var brightness: Double = 0.2
static var opacity: Double = 0.7

// 选项卡设置
static var tabCornerRadius: CGFloat = 16
static var selectedBackgroundOpacity: Double = 0.6
static var shadowRadius: CGFloat = 6
static var shadowOpacity: Double = 0.2
```

## ⚠️ 注意事项

1. **参数范围**：
   - 透明度参数 (`opacity`) 应在 0.0-1.0 之间
   - 亮度参数 (`brightness`) 应在 -1.0 到 1.0 之间
   - 对比度参数 (`contrast`) 应在 0.0 到 2.0 之间
   - 裁剪参数 (`crop*`) 应在 0.0-1.0 之间

2. **性能考虑**：
   - 过高的模糊值可能影响性能
   - 过多的阴影效果可能在低端设备上造成卡顿

3. **视觉协调**：
   - 确保背景图和分段选项卡的视觉层次合理
   - 保持足够的对比度以确保文字可读性

4. **设备适配**：
   - 在不同设备尺寸上测试调整效果
   - 考虑横屏和竖屏模式的显示差异

## 🔧 调试技巧

1. **实时预览**：修改参数后立即运行预览查看效果
2. **分步调试**：一次只调整一个参数，观察其影响
3. **版本对比**：保存不同版本的参数设置，便于对比选择
4. **用户测试**：在真实设备上测试最终效果

通过这些参数，您可以完全自定义订阅页面的视觉效果，创造出符合您品牌风格的独特界面！ 