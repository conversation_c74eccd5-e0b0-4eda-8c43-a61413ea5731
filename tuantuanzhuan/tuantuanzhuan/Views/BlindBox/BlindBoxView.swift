//
//  BlindBoxView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 盲盒主视图
 * 集成所有盲盒相关组件，提供完整的盲盒开箱体验
 */
struct BlindBoxView: View {
    
    // MARK: - Properties
    let student: Student
    let schoolClass: SchoolClass
    let onNavigateToSettings: (() -> Void)?
    
    // MARK: - View Models
    @StateObject private var viewModel: BlindBoxViewModel
    
    // MARK: - State
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Initialization
    
    init(student: Student, schoolClass: SchoolClass, onNavigateToSettings: (() -> Void)? = nil) {
        self.student = student
        self.schoolClass = schoolClass
        self.onNavigateToSettings = onNavigateToSettings
        self._viewModel = StateObject(wrappedValue: BlindBoxViewModel(student: student, schoolClass: schoolClass))
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                backgroundGradient
                
                // 主要内容
                mainContent(geometry: geometry)
                
                // 结果弹窗
                if viewModel.showResult {
                    resultOverlay
                }
                
                // 积分不足提示
                if viewModel.showInsufficientPoints {
                    insufficientPointsAlert
                }
                

                
                // 粒子效果层
                particleEffectLayer
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("blind_box.page_title".localized)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                backButton
            }
        }
        .onAppear {
            viewModel.loadBlindBoxConfig()
        }
    }
    
    // MARK: - Background Gradient
    
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color(hex: "#667eea").opacity(0.1),
                Color(hex: "#764ba2").opacity(0.05),
                Color.white
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Main Content
    
    private func mainContent(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // 顶部统计信息
            if !viewModel.boxItems.isEmpty {
                BlindBoxStatsView(viewModel: viewModel)
                    .padding(.top, 20)
                    .padding(.bottom, 16)
            }
            
            // 盲盒网格或空状态
            contentArea(geometry: geometry)
        }
    }
    
    // MARK: - Content Area
    
    @ViewBuilder
    private func contentArea(geometry: GeometryProxy) -> some View {
        if viewModel.isLoading {
            loadingView
        } else if viewModel.boxItems.isEmpty {
            emptyStateView
        } else {
            blindBoxGridView(geometry: geometry)
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .progressViewStyle(CircularProgressViewStyle(tint: Color(hex: "#a9d051")))
            
            Text("blind_box.loading_config".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white.opacity(0.8))
    }
    
    // MARK: - Empty State View
    
    private var emptyStateView: some View {
        BlindBoxEmptyStateView {
            dismiss()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                onNavigateToSettings?()
            }
        }
    }
    
    // MARK: - Blind Box Grid View
    
    private func blindBoxGridView(geometry: GeometryProxy) -> some View {
        BlindBoxGridView(
            viewModel: viewModel,
            geometry: geometry,
            onBoxTapped: { index in
                handleBoxTapped(at: index)
            }
        )
    }
    
    // MARK: - Result Overlay
    
    private var resultOverlay: some View {
        BlindBoxResultView(
            prizeName: viewModel.resultPrize,
            costPoints: viewModel.costPerOpen,
            onConfirm: {
                viewModel.confirmResult()
            },
            onCancel: {
                viewModel.showResult = false
                viewModel.resultPrize = ""
            }
        )
        .zIndex(1000)
    }
    
    // MARK: - Insufficient Points Alert
    
    private var insufficientPointsAlert: some View {
        BlindBoxAlertView(
            title: "blind_box.alert.insufficient_points_title_text".localized,
            message: String(format: "blind_box.alert.insufficient_points_message_text".localized, "\(viewModel.costPerOpen)", "\(student.point)"),
            primaryButton: .init(
                title: "blind_box.alert.confirm_button_text".localized,
                action: {
                    viewModel.showInsufficientPoints = false
                }
            ),
            secondaryButton: nil
        )
        .zIndex(999)
    }
    

    
    // MARK: - Particle Effect Layer
    
    private var particleEffectLayer: some View {
        ParticleSystemView(
            particles: viewModel.particles,
            isActive: !viewModel.particles.isEmpty
        )
        .zIndex(998)
    }
    
    // MARK: - Toolbar Items
    
    private var backButton: some View {
        Button(action: {
            dismiss()
        }) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .semibold))
                
                Text("blind_box.back_button".localized)
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(DesignSystem.Colors.textPrimary)
        }
    }
    

    
    // MARK: - Event Handlers
    
    /**
     * 处理盲盒点击事件
     */
    private func handleBoxTapped(at index: Int) {
        let success = viewModel.openBlindBox(at: index)
        if success {
            print("✅ 盲盒开启成功，索引: \(index)")
        } else {
            print("❌ 盲盒开启失败，索引: \(index)")
        }
    }
}

// MARK: - Custom Alert View

/**
 * 盲盒自定义弹窗组件
 */
struct BlindBoxAlertView: View {
    
    let title: String
    let message: String
    let primaryButton: AlertButton
    let secondaryButton: AlertButton?
    
    @State private var showAlert = false
    
    struct AlertButton {
        let title: String
        let action: () -> Void
    }
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.4)
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                // 标题
                Text(title)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                // 消息
                Text(message)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
                
                // 按钮
                HStack(spacing: 12) {
                    if let secondaryButton = secondaryButton {
                        Button(secondaryButton.title) {
                            secondaryButton.action()
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color(hex: "#f8f9fa"))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 10)
                                        .stroke(Color(hex: "#e9ecef"), lineWidth: 1)
                                )
                        )
                    }
                    
                    Button(primaryButton.title) {
                        primaryButton.action()
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient(
                            colors: [Color(hex: "#a9d051"), Color(hex: "#8bb83f")],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(10)
                }
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 40)
            .scaleEffect(showAlert ? 1.0 : 0.8)
            .opacity(showAlert ? 1.0 : 0.0)
        }
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                showAlert = true
            }
        }
    }
}

// MARK: - Blind Box Settings View

/**
 * 盲盒配置视图（临时实现）
 */
struct BlindBoxSettingsView: View {
    
    let schoolClass: SchoolClass
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "gear")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(Color(hex: "#a9d051").opacity(0.6))
            
            Text("盲盒配置")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Text("此功能正在开发中，敬请期待...")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(hex: "#f8f9fa"))
    }
}

// MARK: - Preview

#Preview {
    NavigationView {
        BlindBoxView(
            student: {
                let student = Student()
                student.name = "测试学生"
                student.point = 100
                return student
            }(),
            schoolClass: SchoolClass()
        )
    }
} 