//
//  BlindBoxResultView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 盲盒结果展示组件
 * 显示开箱结果，包含奖品信息、庆祝动画和确认操作
 */
struct BlindBoxResultView: View {
    
    // MARK: - Properties
    let prizeName: String
    let costPoints: Int
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    // MARK: - Animation States
    @State private var showResult = false
    @State private var celebrationTriggered = false
    @State private var scaleEffect: CGFloat = 0.3
    @State private var rotationAngle: Double = 0
    @State private var bounceOffset: CGFloat = 0
    
    var body: some View {
        ZStack {
            // 背景遮罩
            backgroundOverlay
            
            // 主要内容
            if showResult {
                resultContentView
                    .scaleEffect(scaleEffect)
                    .rotation3DEffect(
                        .degrees(rotationAngle),
                        axis: (x: 0, y: 1, z: 0)
                    )
                    .offset(y: bounceOffset)
            }
            
            // 庆祝粒子效果
            if celebrationTriggered {
                celebrationParticles
            }
        }
        .onAppear {
            startPresentationAnimation()
        }
    }
    
    // MARK: - Background Overlay
    
    private var backgroundOverlay: some View {
        Color.black.opacity(0.6)
            .ignoresSafeArea()
    }
    
    // MARK: - Result Content View
    
    private var resultContentView: some View {
        VStack(spacing: 24) {
            // 奖品卡片
            prizeCardView
            
            // 消耗信息
            costInfoView
            
            // 操作按钮
            actionButtonsView
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(Color.white)
                .shadow(
                    color: Color.black.opacity(0.2),
                    radius: 20,
                    x: 0,
                    y: 10
                )
        )
        .padding(.horizontal, 32)
    }
    
    // MARK: - Prize Card View
    
    private var prizeCardView: some View {
        VStack(spacing: 16) {
            // 庆祝图标
            Image(systemName: "gift.fill")
                .font(.system(size: 48, weight: .bold))
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color(hex: "#ff6b6b"), Color(hex: "#feca57")],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .scaleEffect(celebrationTriggered ? 1.2 : 1.0)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: celebrationTriggered)
            
            // 恭喜文本
            Text("blind_box.result.congratulations_text".localized)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            // 奖品名称
            Text(prizeName)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .multilineTextAlignment(.center)
                .lineLimit(3)
                .fixedSize(horizontal: false, vertical: true)
            
            // 装饰线条
            decorationLine
        }
        .padding(.vertical, 20)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color(hex: "#ffeef0"),
                            Color(hex: "#fff4e6")
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
    }
    
    // MARK: - Decoration Line
    
    private var decorationLine: some View {
        HStack(spacing: 8) {
            ForEach(0..<5, id: \.self) { _ in
                Circle()
                    .fill(Color(hex: "#ff6b6b").opacity(0.6))
                    .frame(width: 6, height: 6)
            }
        }
    }
    
    // MARK: - Cost Info View
    
    private var costInfoView: some View {
        HStack(spacing: 12) {
            Image(systemName: "star.circle.fill")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(Color(hex: "#a9d051"))
            
            VStack(alignment: .leading, spacing: 2) {
                Text("blind_box.result.cost_label_text".localized)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Text("\(costPoints) 积分")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
            
            Spacer()
            
            // 获得标签
            HStack(spacing: 4) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.green)
                
                Text("blind_box.result.obtained_label_text".localized)
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(.green)
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 4)
            .background(
                Capsule()
                    .fill(Color.green.opacity(0.1))
            )
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(hex: "#f8f9fa"))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(hex: "#e9ecef"), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Action Buttons View
    
    private var actionButtonsView: some View {
        Button(action: {
            confirmResult()
        }) {
            Text("blind_box.result.confirm_button_text".localized)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(
                    LinearGradient(
                        colors: [Color(hex: "#a9d051"), Color(hex: "#8bb83f")],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(12)
                .shadow(color: Color(hex: "#a9d051").opacity(0.3), radius: 8, x: 0, y: 4)
        }
    }
    
    // MARK: - Celebration Particles
    
    private var celebrationParticles: some View {
        ZStack {
            // 左侧粒子
            AdvancedParticleSystemView(
                particleSystem: ParticleSystem(
                    emitter: .celebration(at: CGPoint(x: 100, y: 300))
                )
            )
            
            // 右侧粒子
            AdvancedParticleSystemView(
                particleSystem: ParticleSystem(
                    emitter: .celebration(at: CGPoint(x: 300, y: 350))
                )
            )
            
            // 顶部粒子
            AdvancedParticleSystemView(
                particleSystem: ParticleSystem(
                    emitter: .sparkle(at: CGPoint(x: 200, y: 200))
                )
            )
        }
        .allowsHitTesting(false)
    }
    
    // MARK: - Animation Methods
    
    /**
     * 开始展示动画
     */
    private func startPresentationAnimation() {
        // 延迟显示内容
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                showResult = true
                scaleEffect = 1.0
            }
        }
        
        // 旋转入场
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.easeOut(duration: 0.6)) {
                rotationAngle = 360
            }
        }
        
        // 弹跳效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.6)) {
                bounceEffect()
            }
        }
        
        // 启动庆祝动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            startCelebration()
        }
    }
    
    /**
     * 弹跳效果
     */
    private func bounceEffect() {
        withAnimation(.easeOut(duration: 0.2)) {
            bounceOffset = -20
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.easeIn(duration: 0.2)) {
                bounceOffset = 0
            }
        }
    }
    
    /**
     * 开始庆祝动画
     */
    private func startCelebration() {
        celebrationTriggered = true
        
        // 触觉反馈
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }
    
    /**
     * 确认结果
     */
    private func confirmResult() {
        // 确认动画
        withAnimation(.easeIn(duration: 0.3)) {
            scaleEffect = 0.8
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            dismiss()
            onConfirm()
        }
    }
    
    /**
     * 关闭视图
     */
    private func dismiss() {
        withAnimation(.easeIn(duration: 0.3)) {
            showResult = false
            scaleEffect = 0.3
        }
    }
}

/**
 * 简化版盲盒结果组件
 */
struct SimpleBlindBoxResultView: View {
    
    let prizeName: String
    let onDismiss: () -> Void
    
    @State private var showContent = false
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.5)
                .ignoresSafeArea()
                .onTapGesture {
                    dismiss()
                }
            
            VStack(spacing: 20) {
                Image(systemName: "gift.fill")
                    .font(.system(size: 40, weight: .bold))
                    .foregroundColor(.orange)
                
                Text("获得奖品")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text(prizeName)
                    .font(.title)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Button("确定") {
                    dismiss()
                }
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .padding(.horizontal, 30)
                .padding(.vertical, 12)
                .background(Color.blue)
                .cornerRadius(25)
            }
            .padding(30)
            .background(Color.white)
            .cornerRadius(20)
            .shadow(radius: 20)
            .scaleEffect(showContent ? 1 : 0.5)
            .opacity(showContent ? 1 : 0)
        }
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                showContent = true
            }
        }
    }
    
    private func dismiss() {
        withAnimation(.easeIn(duration: 0.3)) {
            showContent = false
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            onDismiss()
        }
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.gray.opacity(0.3).ignoresSafeArea()
        
        BlindBoxResultView(
            prizeName: "超级大奖",
            costPoints: 50,
            onConfirm: {
                print("确认领取")
            },
            onCancel: {
                print("取消领取")
            }
        )
    }
} 