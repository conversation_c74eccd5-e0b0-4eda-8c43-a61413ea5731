//
//  StudentDetailView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 学生详情页面主视图
 * 包含学生信息卡片、操作按钮和历史记录等完整功能
 */
struct StudentDetailView: View {
    
    // MARK: - Properties
    @StateObject private var viewModel: StudentDetailViewModel
    @Environment(\.presentationMode) var presentationMode
    
    // MARK: - Animation States
    @State private var pageAppeared = false
    
    // MARK: - Points Operations States
    @State private var showAddPointsOptions = false
    @State private var showDeductPointsOptions = false
    @State private var showAddPointsForm = false
    @State private var showDeductPointsForm = false
    @State private var addPointsFormData = StudentPointsFormData(operationType: .add)
    @State private var deductPointsFormData = StudentPointsFormData(operationType: .deduct)
    
    // MARK: - Redemption Operations States
    @State private var showRedemptionOptions = false
    @State private var showRedemptionForm = false
    
    // MARK: - Lottery Operations States
    @State private var showLotteryOptions = false
    @State private var showLotteryWheel = false
    @State private var showBlindBox = false
    @State private var showScratchCard = false
    
    // MARK: - AI Analysis Report States
    @State private var showAIAnalysisReport = false
    
    // MARK: - Navigation Callback
    let onClose: () -> Void
    let onNavigateToSettings: (() -> Void)?
    let onNavigateToSubscription: (() -> Void)?
    
    // MARK: - Initialization
    init(student: Student) {
        self._viewModel = StateObject(wrappedValue: StudentDetailViewModel(student: student))
        self.onClose = { }
        self.onNavigateToSettings = nil
        self.onNavigateToSubscription = nil
    }
    
    init(studentId: String?, onClose: @escaping () -> Void, onNavigateToSettings: (() -> Void)? = nil, onNavigateToSubscription: (() -> Void)? = nil) {
        // 根据studentId从CoreData中查找真实的学生数据
        let student: Student
        
        if let studentId = studentId,
           let uuid = UUID(uuidString: studentId),
           let foundStudent = CoreDataManager.shared.getStudent(by: uuid) {
            // 找到了对应的学生数据
            student = foundStudent
            print("✅ 成功加载学生数据: \(student.name ?? "未知"), ID: \(studentId)")
        } else {
            // 如果找不到学生数据，创建一个临时空对象（避免崩溃）
            let context = CoreDataManager.shared.viewContext
            student = Student(context: context)
            student.id = UUID()
            student.name = "未找到学生"
            student.studentNumber = "N/A"
            student.gender = "male"
            student.point = 0
            student.createdAt = Date()
            print("⚠️ 未找到学生数据，studentId: \(studentId ?? "nil")，创建临时对象")
        }
        
        self._viewModel = StateObject(wrappedValue: StudentDetailViewModel(student: student))
        self.onClose = onClose
        self.onNavigateToSettings = onNavigateToSettings
        self.onNavigateToSubscription = onNavigateToSubscription
    }
    
    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 美化背景渐变
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color(hex: "#fcfff4"), location: 0.0),
                        .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                        .init(color: Color.white, location: 0.7),
                        .init(color: Color(hex: "#fafffe"), location: 1.0)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea(.all)
                
                // 装饰性背景元素
                VStack {
                    HStack {
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.04))
                            .frame(width: 100, height: 100)
                            .offset(x: 30, y: 20)
                    }
                    Spacer()
                    HStack {
                        Circle()
                            .fill(Color(hex: "#FFE49E").opacity(0.05))
                            .frame(width: 80, height: 80)
                            .offset(x: -40, y: -30)
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.03))
                            .frame(width: 60, height: 60)
                            .offset(x: 20, y: 40)
                    }
                }
                
                VStack(spacing: 0) {
                    // 顶部关闭按钮
                    StudentDetailHeader {
                        handleClose()
                    }
                    .offset(y: pageAppeared ? 0 : -80)
                    .animation(.easeInOut(duration: 0.6).delay(0.1), value: pageAppeared)
                    
                    // 主要内容区域
                    VStack(spacing: DesignSystem.Spacing.lg) {
                        // 学生信息卡片
                        StudentInfoCard(
                            student: viewModel.student,
                            onAddPointsTapped: {
                                handleAddPoints()
                            },
                            onDeductPointsTapped: {
                                handleDeductPoints()
                            },
                            onExchangeTapped: {
                                handleExchange()
                            },
                            onLotteryTapped: {
                                handleLottery()
                            },
                            onAnalysisReportTapped: {
                                handleAnalysisReport()
                            }
                        )
                        .padding(.horizontal, 25)
                        .opacity(pageAppeared ? 1.0 : 0.0)
                        .offset(y: pageAppeared ? 0 : 50)
                        .animation(.easeInOut(duration: 0.8).delay(0.1), value: pageAppeared)
                        
                        // 历史记录组件
                        HistoryRecordsView(viewModel: viewModel)
                            .padding(.horizontal, 25)
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? 0 : 30)
                            .animation(.easeInOut(duration: 0.6).delay(0.5), value: pageAppeared)
                        
                        Spacer()  // 填充剩余空间
                    }
                    .padding(.top, DesignSystem.Spacing.sm)
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            withAnimation(.easeInOut(duration: 0.8)) {
                pageAppeared = true
            }
            
            // 页面出现时刷新学生数据
            refreshStudentData()
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            // 应用进入前台时刷新数据
            refreshStudentData()
        }
        // 删除确认对话框
        .overlay {
            if viewModel.showDeleteConfirmation, let record = viewModel.recordToDelete {
                ZStack {
                    Color.black.opacity(0.4)
                        .ignoresSafeArea()
                        .onTapGesture {
                            viewModel.cancelDelete()
                        }
                    
                    DeleteRecordConfirmationDialog(
                        record: record,
                        onConfirm: {
                            viewModel.confirmDeleteRecord()
                        },
                        onCancel: {
                            viewModel.cancelDelete()
                        }
                    )
                    .transition(.scale.combined(with: .opacity))
                }
                .animation(.spring(response: 0.3, dampingFraction: 0.8), value: viewModel.showDeleteConfirmation)
            }
        }
        // 加分选项弹窗
        .overlay {
            StudentPointsOptionsView(
                isPresented: $showAddPointsOptions,
                operationType: .add,
                frequentRules: viewModel.getFrequentAddRules(),
                onRuleSelected: handleAddPointsRuleSelected,
                onCustomSelected: handleAddPointsCustomSelected,
                onCancel: cancelPointsOperations
            )
        }
        // 扣分选项弹窗
        .overlay {
            StudentPointsOptionsView(
                isPresented: $showDeductPointsOptions,
                operationType: .deduct,
                frequentRules: viewModel.getFrequentDeductRules(),
                onRuleSelected: handleDeductPointsRuleSelected,
                onCustomSelected: handleDeductPointsCustomSelected,
                onCancel: cancelPointsOperations
            )
        }
        // 加分自定义表单弹窗
        .overlay {
            StudentPointsFormView(
                isPresented: $showAddPointsForm,
                formData: addPointsFormData,
                onSubmit: handleAddPointsFormSubmit,
                onCancel: cancelPointsOperations
            )
        }
        // 扣分自定义表单弹窗
        .overlay {
            StudentPointsFormView(
                isPresented: $showDeductPointsForm,
                formData: deductPointsFormData,
                onSubmit: handleDeductPointsFormSubmit,
                onCancel: cancelPointsOperations
            )
        }
        // 兑换选项弹窗
        .overlay {
            StudentRedemptionOptionsView(
                isPresented: $showRedemptionOptions,
                student: viewModel.student,
                prizes: viewModel.getClassPrizes(),
                onPrizeSelected: handlePrizeRedemption,
                onCustomRedemption: handleCustomRedemption,
                onDismiss: cancelRedemptionOperations
            )
        }
        // 兑换自定义表单弹窗
        .overlay {
            StudentRedemptionFormView(
                isPresented: $showRedemptionForm,
                student: viewModel.student,
                onConfirm: handleCustomRedemptionFormSubmit,
                onDismiss: cancelRedemptionOperations
            )
        }
        // 抽奖选项弹窗
        .overlay {
            LotteryOptionsView(
                isPresented: $showLotteryOptions,
                onWheelSelected: handleWheelSelected,
                onBlindBoxSelected: handleBlindBoxSelected,
                onScratchCardSelected: handleScratchCardSelected,
                onNavigateToSubscription: onNavigateToSubscription
            )
        }
        // 大转盘抽奖页面
        .fullScreenCover(isPresented: $showLotteryWheel, onDismiss: {
            // 大转盘关闭时也刷新数据
            refreshStudentData()
        }) {
            NavigationView {
                LotteryWheelView(
                    isPresented: $showLotteryWheel,
                    student: viewModel.student,
                    schoolClass: viewModel.getStudentSchoolClass(),
                    onLotteryComplete: handleLotteryWheelComplete,
                    onDismiss: {
                        showLotteryWheel = false
                        // 大转盘关闭时也刷新数据
                        refreshStudentData()
                    },
                    onNavigateToSettings: onNavigateToSettings
                )
            }
        }
        // 盲盒开箱页面
        .fullScreenCover(isPresented: $showBlindBox, onDismiss: {
            // 盲盒开箱页面关闭时刷新学生数据
            refreshStudentData()
        }) {
            NavigationView {
                BlindBoxView(
                    student: viewModel.student,
                    schoolClass: viewModel.getStudentSchoolClass(),
                    onNavigateToSettings: onNavigateToSettings
                )
            }
        }
        // 刮刮卡页面
        .fullScreenCover(isPresented: $showScratchCard, onDismiss: {
            // 刮刮卡页面关闭时刷新学生数据
            refreshStudentData()
        }) {
            NavigationView {
                ScratchCardView(
                    student: viewModel.student,
                    schoolClass: viewModel.getStudentSchoolClass(),
                    onNavigateToSettings: onNavigateToSettings
                )
            }
        }
        // AI分析报告页面
        .fullScreenCover(isPresented: $showAIAnalysisReport, onDismiss: {
            // AI分析报告页面关闭时刷新学生数据
            refreshStudentData()
        }) {
            NavigationView {
                AIAnalysisReportView(
                    student: viewModel.student,
                    onDismiss: {
                        showAIAnalysisReport = false
                    },
                    onNavigateToSubscription: onNavigateToSubscription
                )
            }
        }
    }
    
    // MARK: - Action Handlers
    
    /**
     * 处理关闭按钮点击
     */
    private func handleClose() {
        onClose()
    }
    
    /**
     * 处理加分按钮点击
     */
    private func handleAddPoints() {
        print("显示加分选项弹窗 - 学生: \(viewModel.student.name ?? "未知")")
        showAddPointsOptions = true
    }
    
    /**
     * 处理扣分按钮点击
     */
    private func handleDeductPoints() {
        print("显示扣分选项弹窗 - 学生: \(viewModel.student.name ?? "未知")")
        showDeductPointsOptions = true
    }
    
    // MARK: - Points Operations Handlers
    
    /**
     * 处理加分规则选择
     */
    private func handleAddPointsRuleSelected(_ rule: Rule) {
        showAddPointsOptions = false
        
        let success = viewModel.executeRuleOperation(rule)
        if success {
            // 触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.success)
            
            print("✅ 加分操作成功: \(rule.name ?? "") +\(rule.value)分")
        } else {
            // 错误触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            
            print("❌ 加分操作失败: \(rule.name ?? "")")
        }
    }
    
    /**
     * 处理扣分规则选择
     */
    private func handleDeductPointsRuleSelected(_ rule: Rule) {
        showDeductPointsOptions = false
        
        let success = viewModel.executeRuleOperation(rule)
        if success {
            // 触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.success)
            
            print("✅ 扣分操作成功: \(rule.name ?? "") -\(rule.value)分")
        } else {
            // 错误触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            
            print("❌ 扣分操作失败: \(rule.name ?? "")")
        }
    }
    
    /**
     * 处理加分自定义选择
     */
    private func handleAddPointsCustomSelected() {
        showAddPointsOptions = false
        addPointsFormData = StudentPointsFormData(operationType: .add)
        showAddPointsForm = true
    }
    
    /**
     * 处理扣分自定义选择
     */
    private func handleDeductPointsCustomSelected() {
        showDeductPointsOptions = false
        deductPointsFormData = StudentPointsFormData(operationType: .deduct)
        showDeductPointsForm = true
    }
    
    /**
     * 处理加分表单提交
     */
    private func handleAddPointsFormSubmit(_ operations: [StudentPointsOperation]) {
        showAddPointsForm = false
        
        let successCount = viewModel.executeBatchPointsOperations(operations)
        
        if successCount > 0 {
            // 成功触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.success)
            
            print("✅ 批量加分操作成功: \(successCount)/\(operations.count)")
        } else {
            // 错误触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            
            print("❌ 批量加分操作失败")
        }
    }
    
    /**
     * 处理扣分表单提交
     */
    private func handleDeductPointsFormSubmit(_ operations: [StudentPointsOperation]) {
        showDeductPointsForm = false
        
        let successCount = viewModel.executeBatchPointsOperations(operations)
        
        if successCount > 0 {
            // 成功触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.success)
            
            print("✅ 批量扣分操作成功: \(successCount)/\(operations.count)")
        } else {
            // 错误触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            
            print("❌ 批量扣分操作失败")
        }
    }
    
    /**
     * 取消积分操作弹窗
     */
    private func cancelPointsOperations() {
        showAddPointsOptions = false
        showDeductPointsOptions = false
        showAddPointsForm = false
        showDeductPointsForm = false
    }
    
    // MARK: - Redemption Operations Handlers
    
    /**
     * 处理奖品兑换 (不关闭弹窗)
     */
    private func handlePrizeRedemption(_ prize: Prize) {
        print("处理奖品兑换: \(prize.name ?? "未知奖品")")
        
        let success = viewModel.redeemPrize(prize)
        if success {
            // 成功触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.success)
            
            print("✅ 奖品兑换成功: \(prize.name ?? "")")
            // 注意：这里不关闭弹窗，让用户可以继续兑换其他奖品
        } else {
            // 错误触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            
            print("❌ 奖品兑换失败: \(prize.name ?? "")")
        }
    }
    
    /**
     * 处理自定义兑换选择
     */
    private func handleCustomRedemption() {
        print("处理自定义兑换")
        showRedemptionOptions = false
        showRedemptionForm = true
    }
    
    /**
     * 处理自定义兑换表单提交
     */
    private func handleCustomRedemptionFormSubmit(_ operation: StudentRedemptionOperation) {
        print("处理自定义兑换表单提交: \(operation.items.count) 个项目")
        showRedemptionForm = false
        
        let success = viewModel.executeRedemptionOperation(operation)
        if success {
            // 成功触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.success)
            
            print("✅ 自定义兑换成功: \(operation.items.count) 个项目")
        } else {
            // 错误触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            
            print("❌ 自定义兑换失败")
        }
    }
    
    /**
     * 处理兑换按钮点击
     */
    private func handleExchange() {
        print("显示兑换选项弹窗 - 学生: \(viewModel.student.name ?? "未知")")
        showRedemptionOptions = true
    }
    
    /**
     * 取消兑换操作弹窗
     */
    private func cancelRedemptionOperations() {
        showRedemptionOptions = false
        showRedemptionForm = false
    }
    
    /**
     * 处理抽奖按钮点击
     */
    private func handleLottery() {
        print("显示抽奖选项弹窗 - 学生: \(viewModel.student.name ?? "未知")")
        showLotteryOptions = true
    }
    
    // MARK: - Lottery Operations Handlers
    
    /**
     * 处理大转盘选择
     */
    private func handleWheelSelected() {
        print("选择大转盘 - 学生: \(viewModel.student.name ?? "未知")")
        showLotteryOptions = false
        
        // 延迟显示大转盘，确保选项弹窗完全关闭
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            showLotteryWheel = true
        }
    }
    
    /**
     * 处理盲盒选择
     */
    private func handleBlindBoxSelected() {
        print("选择盲盒 - 学生: \(viewModel.student.name ?? "未知")")
        showLotteryOptions = false
        
        // 延迟显示盲盒页面，确保选项弹窗完全关闭
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            showBlindBox = true
        }
    }
    
    /**
     * 处理刮刮卡选择
     */
    private func handleScratchCardSelected() {
        print("选择刮刮卡 - 学生: \(viewModel.student.name ?? "未知")")
        showLotteryOptions = false
        
        // 延迟显示刮刮卡页面，确保选项弹窗完全关闭
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            showScratchCard = true
        }
    }
    
    /**
     * 处理大转盘抽奖完成
     */
    private func handleLotteryWheelComplete(prize: String, cost: Int) {
        print("大转盘抽奖完成 - 学生: \(viewModel.student.name ?? "未知"), 中奖: \(prize), 消耗: \(cost)积分")
        
        // 直接创建抽奖记录（内部会自动扣除积分）
        let success = viewModel.createLotteryRecord(type: .wheelLottery, prize: prize, cost: cost)
        
        if success {
            // 成功触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.success)
            
            print("✅ 大转盘抽奖成功: 获得 \(prize)，消耗 \(cost) 积分")
        } else {
            // 错误触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            
            print("❌ 大转盘抽奖失败: 积分不足或记录创建失败")
        }
    }
    
    /**
     * 处理分析报告按钮点击
     */
    private func handleAnalysisReport() {
        print("打开AI分析报告 - 学生: \(viewModel.student.name ?? "未知")")
        showAIAnalysisReport = true
    }
    
    // MARK: - Data Loading Methods (CoreData+CloudKit预留接口)
    
    /**
     * 根据需要加载学生数据
     * TODO: 实现CoreData+CloudKit数据同步逻辑
     */
    private func loadStudentDataIfNeeded() {
        // 预留接口：从CoreData本地数据库加载学生信息
        // 预留接口：如果本地数据不存在或过期，从CloudKit同步最新数据
        // 预留接口：处理数据加载状态（加载中、成功、失败）
        
        print("预留接口：加载学生数据 - StudentID: \(viewModel.student.id?.uuidString ?? "未知ID")")
    }
    
    /**
     * 刷新学生数据
     */
    private func refreshStudentData() {
        // 刷新ViewModel中的学生数据
        viewModel.refreshStudentFromDatabase()
        
        print("✅ 已刷新学生数据 - 当前积分: \(viewModel.student.point)")
    }
}

// MARK: - Preview
#Preview {
    let context = PersistenceController.preview.container.viewContext
    let student = Student(context: context)
    student.id = UUID()
    student.name = "张小明"
    student.studentNumber = "001"
    student.gender = "male"
    student.point = 85
    student.createdAt = Date()
    
    return StudentDetailView(student: student)
} 