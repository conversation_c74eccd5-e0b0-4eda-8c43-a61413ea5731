//
//  StudentPointsOptionsView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 学生积分操作选项弹窗组件
 * 显示常用规则列表和自定义积分操作入口
 */
struct StudentPointsOptionsView: View {
    
    // MARK: - Properties
    @Binding var isPresented: Bool
    let operationType: StudentPointsOperation.OperationType
    let frequentRules: [Rule]
    let onRuleSelected: (Rule) -> Void
    let onCustomSelected: () -> Void
    let onCancel: () -> Void
    
    // MARK: - State
    @State private var animationTrigger = false
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            onCancel()
                        }
                    }
                    .transition(.opacity)
                
                // 选项菜单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        headerView
                        
                        // 分隔线
                        dividerView
                        
                        // 常用规则列表区域
                        if !frequentRules.isEmpty {
                            frequentRulesSection
                        } else {
                            emptyRulesSection
                        }
                        
                        // 分隔线
                        if !frequentRules.isEmpty {
                            dividerView
                        }
                        
                        // 自定义选项
                        customOptionSection
                        
                        // 底部间距
                        Spacer()
                            .frame(height: 10)
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 350))
                    .frame(maxHeight: min(geometry.size.height * 0.7, 500))
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color(hex: operationType.colorHex).opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
    }
    
    // MARK: - 子视图组件
    
    /**
     * 标题栏
     */
    private var headerView: some View {
        HStack {
            Text(operationType == .add ? "student_points.options.add_title".localized : "student_points.options.deduct_title".localized)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Spacer()
            
            // 关闭按钮
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
        .padding(.bottom, 16)
    }
    
    /**
     * 分隔线
     */
    private var dividerView: some View {
        Rectangle()
            .fill(Color(hex: "#edf5d9"))
            .frame(height: 1)
            .padding(.horizontal, 20)
    }
    
    /**
     * 常用规则列表区域
     */
    private var frequentRulesSection: some View {
        VStack(spacing: 0) {
            // 区域标题
            HStack {
                Text("student_points.options.frequent_rules_title".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            .padding(.bottom, 12)
            
            // 规则列表
            ScrollView(.vertical, showsIndicators: false) {
                VStack(spacing: 8) {
                    ForEach(frequentRules, id: \.id) { rule in
                        FrequentRuleOptionButton(
                            rule: rule,
                            operationType: operationType,
                            action: {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                    onRuleSelected(rule)
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal, 20)
            }
            .frame(maxHeight: 200)
        }
    }
    
    /**
     * 空规则状态
     */
    private var emptyRulesSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "list.bullet")
                .font(.system(size: 32))
                .foregroundColor(Color.gray.opacity(0.5))
            
            Text("student_points.options.no_frequent_rules".localized)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.gray)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 24)
    }
    
    /**
     * 自定义选项区域
     */
    private var customOptionSection: some View {
        CustomOptionButton(
            operationType: operationType,
            action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                    onCustomSelected()
                }
            }
        )
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
}

/**
 * 常用规则选项按钮组件
 */
struct FrequentRuleOptionButton: View {
    
    let rule: Rule
    let operationType: StudentPointsOperation.OperationType
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 左侧图标
                ZStack {
                    Circle()
                        .fill(Color(hex: operationType.colorHex).opacity(0.15))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: operationType.iconName)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(Color(hex: operationType.colorHex))
                }
                
                // 规则信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(rule.name ?? "")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    Text("\(operationType == .add ? "+" : "-")\(rule.value) 分")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: operationType.colorHex))
                }
                
                Spacer()
                
                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        isPressed 
                        ? Color(hex: operationType.colorHex).opacity(0.1)
                        : Color.clear
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(hex: operationType.colorHex).opacity(0.2), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: isPressed)
        .onTapGesture {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

/**
 * 自定义选项按钮组件
 */
struct CustomOptionButton: View {
    
    let operationType: StudentPointsOperation.OperationType
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 左侧图标
                ZStack {
                    Circle()
                        .fill(Color(hex: "#f39c12").opacity(0.15))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: "square.and.pencil")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(Color(hex: "#f39c12"))
                }
                
                // 自定义信息
                VStack(alignment: .leading, spacing: 4) {
                    Text("student_points.options.custom_option".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text(operationType == .add ? "student_points.options.custom_description_add".localized : "student_points.options.custom_description_deduct".localized)
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        isPressed 
                        ? Color(hex: "#f39c12").opacity(0.1)
                        : Color.clear
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(hex: "#f39c12").opacity(0.3), lineWidth: 1.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: isPressed)
        .onTapGesture {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        StudentPointsOptionsView(
            isPresented: .constant(true),
            operationType: .add,
            frequentRules: [
                // 模拟规则数据
            ],
            onRuleSelected: { rule in
                print("选择规则: \(rule.name ?? "")")
            },
            onCustomSelected: {
                print("选择自定义")
            },
            onCancel: {
                print("取消操作")
            }
        )
    }
} 