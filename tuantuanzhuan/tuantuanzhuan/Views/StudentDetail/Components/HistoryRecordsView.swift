//
//  HistoryRecordsView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 历史记录组件
 * 包含积分记录和兑换记录的选项卡切换及列表显示
 */
struct HistoryRecordsView: View {
    
    // MARK: - Properties
    @ObservedObject var viewModel: StudentDetailViewModel
    @State private var recordsAppeared = false
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // 选项卡切换
            TabSelector(
                selectedType: $viewModel.selectedRecordType,
                onSelectionChanged: { type in
                    viewModel.selectRecordType(type)
                }
            )
            .opacity(recordsAppeared ? 1.0 : 0.0)
            .offset(y: recordsAppeared ? 0 : -20)
            .animation(.easeInOut(duration: 0.5).delay(0.1), value: recordsAppeared)
            
            // 记录列表
            RecordsList(
                records: viewModel.filteredRecordsWithProtocol, 
                recordsAppeared: $recordsAppeared,
                viewModel: viewModel
            )
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#f8f8f8"),
                    Color(hex: "#ededed")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .cornerRadius(25)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        .onAppear {
            withAnimation {
                recordsAppeared = true
            }
        }
    }
}

/**
 * 选项卡选择器组件
 */
private struct TabSelector: View {
    @Binding var selectedType: StudentDetailViewModel.RecordType
    let onSelectionChanged: (StudentDetailViewModel.RecordType) -> Void
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(StudentDetailViewModel.RecordType.allCases, id: \.self) { type in
                Button(action: {
                    selectedType = type
                    onSelectionChanged(type)
                }) {
                    VStack(spacing: 0) {
                        Text(type.displayName)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(selectedType == type ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(selectedType == type ? Color.white : Color.clear)
                            )
                        
                        // 选中状态下显示绿色底线
                        if selectedType == type {
                            Rectangle()
                                .fill(Color(hex: "a9d051"))
                                .frame(width: 70, height: 5)
                                .cornerRadius(2)
                        }
                    }
                }
            }
        }
        .padding(.horizontal, DesignSystem.Spacing.md)
        .padding(.top, DesignSystem.Spacing.md)
        .padding(.bottom, DesignSystem.Spacing.sm)
    }
}

/**
 * 记录列表组件
 */
private struct RecordsList: View {
    let records: [HistoryRecordProtocol]
    @Binding var recordsAppeared: Bool
    @ObservedObject var viewModel: StudentDetailViewModel
    
    var body: some View {
        if records.isEmpty {
            // 空状态显示
            EmptyRecordsView()
        } else {
            // 记录列表 - 使用List支持swipeActions
            List {
                ForEach(Array(records.enumerated()), id: \.element.recordId) { index, record in
                    RecordItemWithProtocol(record: record, viewModel: viewModel)
                        .opacity(recordsAppeared ? 1.0 : 0.0)
                        .offset(y: recordsAppeared ? 0 : -20)
                        .animation(.easeInOut(duration: 0.4).delay(0.05 * Double(index)), value: recordsAppeared)
                        .listRowSeparator(.hidden)
                        .listRowBackground(Color.clear)
                        .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))
                }
            }
            .listStyle(PlainListStyle())
            .background(Color.clear)
            .padding(.vertical, DesignSystem.Spacing.sm)
        }
    }
}

/**
 * 单个记录项组件
 */
private struct RecordItem: View {
    let record: HistoryRecord
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // 历史图标容器
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "#f8ffe5"),
                                Color(hex: "#edf6d9")
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 40, height: 40)
                    .shadow(color: Color(hex: "#a9d051").opacity(0.1), radius: 2, x: 0, y: 1)
                
                Image("lishi")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)
                    .foregroundColor(Color(hex: "#a9d051"))
            }
            
            // 记录信息
            VStack(alignment: .leading, spacing: 4) {
                Text(record.action)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
                
                Text(record.formattedTime)
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            // 分值显示 - 使用圆角背景
            Text(record.formattedPoints)
                .font(.system(size: 15, weight: .semibold))
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    record.points > 0 ? Color(hex: "#4CAF50") : Color(hex: "#F44336"),
                                    record.points > 0 ? Color(hex: "#45A049") : Color(hex: "#E53935")
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: (record.points > 0 ? Color(hex: "#4CAF50") : Color(hex: "#F44336")).opacity(0.3), radius: 2, x: 0, y: 1)
                )
        }
        .padding(.horizontal, DesignSystem.Spacing.md)
        .padding(.vertical, DesignSystem.Spacing.md)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.white,
                    Color(hex: "#fafafa")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.08), radius: 4, x: 0, y: 2)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#a9d051").opacity(0.1), lineWidth: 1)
        )
    }
}

/**
 * 空状态视图
 */
private struct EmptyRecordsView: View {
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // 图标容器
            ZStack {
                Circle()
                    .fill(DesignSystem.Colors.historyBackground.opacity(0.6))
                    .frame(width: 80, height: 80)
                
                Image("lishi")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 40, height: 40)
                    .foregroundColor(DesignSystem.Colors.textSecondary.opacity(0.7))
            }
            
            VStack(spacing: 8) {
                                    Text("student_detail.history.empty.title".localized)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("student_detail.history.empty.description".localized)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(height: 240)
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 扩展：为特定角落添加圆角
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

/**
 * 使用协议接口的记录项组件（支持左滑删除）
 */
private struct RecordItemWithProtocol: View {
    let record: HistoryRecordProtocol
    @ObservedObject var viewModel: StudentDetailViewModel
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // 类型图标容器
            ZStack {
                Circle()
                    .fill(record.recordType.typeColor.opacity(0.1))
                    .frame(width: 40, height: 40)
                    .shadow(color: record.recordType.typeColor.opacity(0.2), radius: 2, x: 0, y: 1)
                
                Image(record.recordType.iconName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)
                    .foregroundColor(record.recordType.typeColor)
            }
            
            // 记录信息
            VStack(alignment: .leading, spacing: 4) {
                Text(record.displayName)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
                
                HStack(spacing: 8) {
                    Text(record.formattedTime)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    // 类型标识
                    Text(record.recordType.displayName)
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(record.recordType.typeColor)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(record.recordType.typeColor.opacity(0.1))
                        )
                }
            }
            
            Spacer()
            
            // 分值显示
            if record.pointsValue != 0 {
                Text(record.formattedPoints)
                    .font(.system(size: 15, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        record.pointsValue > 0 ? Color(hex: "#4CAF50") : Color(hex: "#F44336"),
                                        record.pointsValue > 0 ? Color(hex: "#45A049") : Color(hex: "#E53935")
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .shadow(color: (record.pointsValue > 0 ? Color(hex: "#4CAF50") : Color(hex: "#F44336")).opacity(0.3), radius: 2, x: 0, y: 1)
                    )
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 12)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.white,
                    Color(hex: "#fafafa")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.08), radius: 4, x: 0, y: 2)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(record.recordType.typeColor.opacity(0.1), lineWidth: 1)
        )
        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
            if record.canDelete {
                Button(action: {
                    // 添加触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.impactOccurred()
                    
                    // 显示删除确认对话框
                    viewModel.prepareDeleteRecord(record)
                }) {
                    Label("删除", systemImage: "trash")
                }
                .tint(Color(hex: "#FF6B6B"))
            }
        }
    }
}

// MARK: - Preview
#Preview {
    let context = PersistenceController.preview.container.viewContext
    let student = Student(context: context)
    student.id = UUID()
    student.name = "张小明"
    student.studentNumber = "001"
    student.gender = "male"
    student.point = 85
    student.createdAt = Date()
    
    let viewModel = StudentDetailViewModel(student: student)
    
    return HistoryRecordsView(viewModel: viewModel)
        .padding()
} 