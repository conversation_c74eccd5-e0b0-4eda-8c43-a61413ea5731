//
//  StudentRedemptionOptionsView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 学生兑换选项弹窗视图
 */
struct StudentRedemptionOptionsView: View {
    
    // MARK: - Properties
    
    @Binding var isPresented: Bool
    let student: Student
    let prizes: [Prize]
    let onPrizeSelected: (Prize) -> Void
    let onCustomRedemption: () -> Void
    let onDismiss: () -> Void
    
    // MARK: - Private Properties
    
    @State private var selectedPrize: Prize?
    @State private var animationTrigger = false
    @State private var showSuccessMessage = false
    @State private var successMessageText = ""
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            onDismiss()
                        }
                    }
                    .transition(.opacity)
                
                // 兑换选项对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 兑换成功消息
                        if showSuccessMessage {
                            successMessageView
                        }
                        
                        // 头部标题
                        headerView
                        
                        // 内容区域
                        contentView
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 350))
                    .frame(maxHeight: min(geometry.size.height * 0.8, 600))
                    .background(Color(.systemBackground))
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.blue.opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
    }
    
    // MARK: - Views
    
    /**
     * 兑换成功消息视图
     */
    private var successMessageView: some View {
        HStack(spacing: 8) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 16))
                .foregroundColor(.white)
            
            Text(successMessageText)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
            LinearGradient(
                colors: [Color.green, Color.green.opacity(0.8)],
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        .cornerRadius(8)
        .padding(.horizontal, 20)
        .padding(.top, 16)
        .transition(.move(edge: .top).combined(with: .opacity))
    }
    
    /**
     * 头部标题视图
     */
    private var headerView: some View {
        HStack {
            Text("redemption.options.title".localized)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Spacer()
            
            // 关闭按钮
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onDismiss()
                }
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
        .padding(.bottom, 16)
    }
    
    /**
     * 内容视图
     */
    private var contentView: some View {
        VStack(spacing: 0) {
            // 分隔线
            dividerView
            
            // 学生信息区域
            studentInfoSection
            
            // 分隔线
            dividerView
            
            // 奖品列表区域
            if !prizes.isEmpty {
                prizesSection
            } else {
                emptyPrizesSection
            }
            
            // 分隔线
            if !prizes.isEmpty {
                dividerView
            }
            
            // 自定义兑换区域
            customRedemptionSection
            
            // 底部间距
            Spacer()
                .frame(height: 10)
        }
    }
    
    // MARK: - 子视图组件
    
    /**
     * 分隔线
     */
    private var dividerView: some View {
        Rectangle()
            .fill(Color(hex: "#edf5d9"))
            .frame(height: 1)
            .padding(.horizontal, 20)
    }
    
    /**
     * 学生信息区域
     */
    private var studentInfoSection: some View {
        HStack(spacing: 12) {
            // 学生头像
            Image(student.gender == "gender.female".localized ? "gender.avatar.female".localized : "gender.avatar.male".localized)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 32, height: 32)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(student.name ?? "")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("redemption.options.current_points".localized(with: "\(Int(student.point))"))
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    /**
     * 奖品列表区域
     */
    private var prizesSection: some View {
        VStack(spacing: 0) {
            // 区域标题
            HStack {
                Text("redemption.options.prizes_title".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            .padding(.bottom, 12)
            
            // 奖品列表
            ScrollView(.vertical, showsIndicators: false) {
                VStack(spacing: 8) {
                    ForEach(prizes, id: \.objectID) { prize in
                        prizeCardView(prize: prize)
                    }
                }
                .padding(.horizontal, 20)
            }
            .frame(maxHeight: 300)
        }
    }
    
    /**
     * 空奖品状态
     */
    private var emptyPrizesSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "gift")
                .font(.system(size: 32))
                .foregroundColor(Color.gray.opacity(0.5))
            
            Text("redemption.options.no_prizes".localized)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.gray)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 24)
    }
    
    /**
     * 自定义兑换区域
     */
    private var customRedemptionSection: some View {
        VStack(spacing: 12) {
            // 区域标题
            HStack {
                Text("redemption.options.custom_title".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            
            // 自定义兑换按钮
            customRedemptionButton
                .padding(.horizontal, 20)
                .padding(.bottom, 16)
        }
    }
    
    /**
     * 奖品卡片视图
     */
    private func prizeCardView(prize: Prize) -> some View {
        let canAfford = Int(student.point) >= Int(prize.cost)
        let prizeName = prize.name ?? ""
        let prizeCost = Int(prize.cost)
        
        return Button(action: {
            if canAfford {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                    // 触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.impactOccurred()
                    
                    // 调用兑换回调
                    onPrizeSelected(prize)
                    
                    // 显示兑换成功消息
                    showRedemptionSuccessMessage(for: prize)
                }
            }
        }) {
            HStack(spacing: 16) {
                // 奖品图标
                RoundedRectangle(cornerRadius: 12)
                    .fill(canAfford ? 
                          LinearGradient(colors: [Color.blue.opacity(0.8), Color.purple.opacity(0.8)], 
                                       startPoint: .topLeading, endPoint: .bottomTrailing) :
                          LinearGradient(colors: [Color.gray.opacity(0.3), Color.gray.opacity(0.2)], 
                                       startPoint: .topLeading, endPoint: .bottomTrailing))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Image(systemName: getPrizeIcon(for: prize.type))
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(canAfford ? .white : .secondary)
                    )
                
                // 奖品信息
                VStack(alignment: .leading, spacing: 2) {
                    Text(prizeName)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(canAfford ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary)
                        .lineLimit(1)
                        .multilineTextAlignment(.leading)
                    
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .font(.system(size: 10))
                            .foregroundColor(canAfford ? .orange : .secondary)
                        
                        Text("\(prizeCost)")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(canAfford ? .orange : .secondary)
                        
                        Text("redemption.options.points_unit".localized)
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 状态指示器
                if canAfford {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                } else {
                    VStack(spacing: 2) {
                        Image(systemName: "lock.fill")
                            .font(.system(size: 10))
                            .foregroundColor(.secondary)
                        
                        Text("redemption.options.insufficient".localized)
                            .font(.system(size: 8))
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(hex: canAfford ? "#ffffff" : "#f8f8f8"))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(canAfford ? Color.blue.opacity(0.3) : Color.gray.opacity(0.2), lineWidth: 1)
            )
            .opacity(canAfford ? 1.0 : 0.6)
        }
        .disabled(!canAfford)
        .buttonStyle(PlainButtonStyle())
    }
    
    /**
     * 自定义兑换按钮
     */
    private var customRedemptionButton: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
                
                onCustomRedemption()
            }
        }) {
            HStack(spacing: 16) {
                // 图标
                RoundedRectangle(cornerRadius: 12)
                    .fill(LinearGradient(colors: [Color.green.opacity(0.8), Color.teal.opacity(0.8)], 
                                       startPoint: .topLeading, endPoint: .bottomTrailing))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white)
                    )
                
                // 文本信息
                VStack(alignment: .leading, spacing: 2) {
                    Text("redemption.options.custom_button_title".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .lineLimit(1)
                    
                    Text("redemption.options.custom_button_description".localized)
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 12))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(hex: "#f8f8f8"))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(hex: "#e0e0e0"), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Helper Methods
    
    /**
     * 获取奖品图标
     */
    private func getPrizeIcon(for type: String?) -> String {
        switch type {
        case "虚拟":
            return "gift.circle.fill"
        case "实物":
            return "cube.box.fill"
        default:
            return "star.circle.fill"
        }
    }
    
    /**
     * 显示兑换成功消息
     */
    private func showRedemptionSuccessMessage(for prize: Prize) {
        let _ = prize.name ?? ""
        successMessageText = "redemption.options.success_message".localized
        
        withAnimation(.easeInOut(duration: 0.3)) {
            showSuccessMessage = true
        }
        
        // 2秒后自动隐藏消息
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.easeInOut(duration: 0.3)) {
                showSuccessMessage = false
            }
        }
    }
}

 