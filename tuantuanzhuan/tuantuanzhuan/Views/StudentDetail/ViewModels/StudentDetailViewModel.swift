//
//  StudentDetailViewModel.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import SwiftUI
import CoreData

// MARK: - 明确使用CoreData实体类型
typealias CoreDataStudent = Student
typealias CoreDataRedemptionRecord = RedemptionRecord
typealias CoreDataLotteryRecord = LotteryRecord
typealias CoreDataPointRecord = PointRecord

/**
 * 学生详情页面视图模型
 * 负责管理学生数据、历史记录数据和页面状态
 * 使用CoreData+CloudKit进行数据管理
 */
class StudentDetailViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var student: CoreDataStudent
    @Published var selectedRecordType: RecordType = .points
    @Published var showDeleteConfirmation = false
    @Published var recordToDelete: HistoryRecordProtocol?
    @Published var showingRedemptionOptions = false
    @Published var showingRedemptionForm = false
    
    // MARK: - CoreData Manager
    private let coreDataManager = CoreDataManager.shared
    
    // MARK: - Enums
    
    /**
     * 历史记录类型枚举
     */
    enum RecordType: String, CaseIterable {
        case points
        case exchange
        
        var displayName: String {
            switch self {
            case .points:
                return "student_detail.history.points".localized
            case .exchange:
                return "student_detail.history.exchange".localized
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取当前选中类型的记录
     */
    var filteredRecords: [Any] {
        switch selectedRecordType {
        case .points:
            return student.sortedPointRecords
        case .exchange:
            // 合并兑换记录和抽奖记录
            let redemptionRecords = student.sortedRedemptionRecords
            let lotteryRecords = student.sortedLotteryRecords
            
            // 将两种记录合并并按时间排序
            var allRecords: [Any] = []
            allRecords.append(contentsOf: redemptionRecords)
            allRecords.append(contentsOf: lotteryRecords)
            
            // 按时间戳排序
            return allRecords.sorted { record1, record2 in
                let timestamp1: Date
                let timestamp2: Date
                
                if let redemption = record1 as? CoreDataRedemptionRecord {
                    timestamp1 = redemption.timestamp ?? Date.distantPast
                } else if let lottery = record1 as? CoreDataLotteryRecord {
                    timestamp1 = lottery.timestamp ?? Date.distantPast
                } else {
                    timestamp1 = Date.distantPast
                }
                
                if let redemption = record2 as? CoreDataRedemptionRecord {
                    timestamp2 = redemption.timestamp ?? Date.distantPast
                } else if let lottery = record2 as? CoreDataLotteryRecord {
                    timestamp2 = lottery.timestamp ?? Date.distantPast
                } else {
                    timestamp2 = Date.distantPast
                }
                
                return timestamp1 > timestamp2
            }
        }
    }
    
    /**
     * 获取当前选中类型的记录数量
     */
    var currentRecordCount: Int {
        return filteredRecords.count
    }
    
    /**
     * 检查是否有历史记录
     */
    var hasRecords: Bool {
        switch selectedRecordType {
        case .points:
            return student.totalPointRecordsCount > 0
        case .exchange:
            let redemptionCount = Int(student.redemptionRecords?.count ?? 0)
            let lotteryCount = Int(student.lotteryRecords?.count ?? 0)
            return (redemptionCount + lotteryCount) > 0
        }
    }
    
    /**
     * 获取积分记录（兼容旧的HistoryRecord接口）
     */
    var historyRecords: [HistoryRecord] {
        return student.sortedPointRecords.map { pointRecord in
            HistoryRecord(
                studentId: student.id?.uuidString ?? "",
                type: .points,
                action: pointRecord.reason ?? "",
                points: Int(pointRecord.value),
                reason: pointRecord.reason
            )
        }
    }
    
    // MARK: - Initialization
    
    /**
     * 初始化视图模型
     * @param student CoreData学生实体
     */
    init(student: CoreDataStudent) {
        self.student = student
    }
    
    // MARK: - Public Methods
    
    /**
     * 选择记录类型
     * @param type 记录类型
     */
    func selectRecordType(_ type: RecordType) {
        selectedRecordType = type
    }
    
    /**
     * 添加积分记录
     * @param points 积分数值
     * @param reason 原因
     */
    func addPoints(_ points: Int, reason: String) {
        _ = coreDataManager.addPointRecord(
            to: student,
            reason: reason,
            value: points
        )
        
        // 刷新学生对象
        refreshStudent()
        
        print("添加积分记录: +\(points) 分，原因: \(reason)")
    }
    
    /**
     * 扣除积分记录
     * @param points 积分数值
     * @param reason 原因
     */
    func deductPoints(_ points: Int, reason: String) {
        _ = coreDataManager.addPointRecord(
            to: student,
            reason: reason,
            value: -points
        )
        
        // 刷新学生对象
        refreshStudent()
        
        print("扣除积分记录: -\(points) 分，原因: \(reason)")
    }
    
    /**
     * 添加兑换记录
     * @param itemName 兑换物品名称
     * @param points 消耗积分
     */
    func addExchangeRecord(itemName: String, points: Int) {
        guard coreDataManager.addRedemptionRecord(
            to: student,
            prizeName: itemName,
            cost: points
        ) != nil else {
            print("兑换失败：积分不足")
            return
        }
        
        // 刷新学生对象
        refreshStudent()
        
        print("添加兑换记录: \(itemName)，消耗积分: \(points)")
    }
    
    /**
     * 添加抽奖记录
     * @param toolType 抽奖工具类型
     * @param prizeResult 抽奖结果
     * @param cost 消耗积分
     */
    func addLotteryRecord(toolType: String, prizeResult: String, cost: Int) {
        guard coreDataManager.addLotteryRecord(
            to: student,
            toolType: toolType,
            prizeResult: prizeResult,
            cost: cost
        ) != nil else {
            print("抽奖失败：积分不足")
            return
        }
        
        // 刷新学生对象
        refreshStudent()
        
        print("添加抽奖记录: \(toolType) - \(prizeResult)，消耗积分: \(cost)")
    }
    
    /**
     * 撤销最近的积分记录
     */
    func reverseLastPointRecord() -> Bool {
        let success = student.reverseLastPointRecord(in: coreDataManager.viewContext)
        
        if success {
            coreDataManager.save()
            refreshStudent()
            print("撤销最近的积分记录成功")
        } else {
            print("撤销积分记录失败：没有可撤销的记录")
        }
        
        return success
    }
    
    // MARK: - 新增积分操作方法
    
    /**
     * 获取学生所在班级的常用加分规则
     * 根据用户订阅级别限制返回的规则数量
     */
    func getFrequentAddRules() -> [Rule] {
        guard let schoolClass = student.schoolClass else { return [] }
        let allRules = coreDataManager.getFrequentRules(for: schoolClass, type: "add")
        
        // 获取当前用户订阅级别的规则数量限制
        let currentUser = coreDataManager.getOrCreateDefaultUser()
        let maxRulesCount = currentUser.subscription?.maxRulesCount ?? 5
        
        // 返回限制数量内的规则
        return Array(allRules.prefix(maxRulesCount))
    }
    
    /**
     * 获取学生所在班级的常用扣分规则
     * 根据用户订阅级别限制返回的规则数量
     */
    func getFrequentDeductRules() -> [Rule] {
        guard let schoolClass = student.schoolClass else { return [] }
        let allRules = coreDataManager.getFrequentRules(for: schoolClass, type: "deduct")
        
        // 获取当前用户订阅级别的规则数量限制
        let currentUser = coreDataManager.getOrCreateDefaultUser()
        let maxRulesCount = currentUser.subscription?.maxRulesCount ?? 5
        
        // 返回限制数量内的规则
        return Array(allRules.prefix(maxRulesCount))
    }
    
    /**
     * 执行单个积分操作
     * @param operation 积分操作对象
     * @return 操作是否成功
     */
    func executePointsOperation(_ operation: StudentPointsOperation) -> Bool {
        let pointsChange = operation.pointsChange
        
        // 如果是扣分操作，检查积分是否足够
        if pointsChange < 0 && Int(student.point) + pointsChange < 0 {
            print("❌ 扣分操作失败：积分不足")
            return false
        }
        
        // 执行积分变更
        let _ = coreDataManager.addPointRecord(
            to: student,
            reason: operation.name,
            value: pointsChange
        )
        
        // 积分记录创建成功
        refreshStudent()
        
        // 发送学生积分变更通知
        sendPointsChangeNotification(pointsChange: pointsChange, reason: operation.name)
        
        print("✅ 积分操作成功: \(operation.description)")
        return true
    }
    
    /**
     * 批量执行积分操作
     * @param operations 积分操作数组
     * @return 成功执行的操作数量
     */
    func executeBatchPointsOperations(_ operations: [StudentPointsOperation]) -> Int {
        guard !operations.isEmpty else { return 0 }
        
        let context = coreDataManager.viewContext
        var successCount = 0
        
        context.performAndWait {
            do {
                // 验证批量操作
                let validationResult = operations.validateBatch()
                if case .invalid(let message) = validationResult {
                    print("❌ 批量操作验证失败: \(message)")
                    return
                }
                
                // 计算总积分变化
                let totalChange = operations.totalPointsChange
                
                // 如果是扣分操作，检查积分是否足够
                if totalChange < 0 && Int(student.point) + totalChange < 0 {
                    print("❌ 批量扣分操作失败：积分不足")
                    return
                }
                
                // 执行所有操作
                for operation in operations {
                    let _ = coreDataManager.addPointRecord(
                        to: student,
                        reason: operation.name,
                        value: operation.pointsChange
                    )
                    
                    // 积分记录创建成功
                    successCount += 1
                    print("✅ 积分操作成功: \(operation.description)")
                }
                
                // 保存所有更改
                try context.save()
                
            } catch {
                print("❌ 批量积分操作保存失败: \(error)")
                successCount = 0
            }
        }
        
        if successCount > 0 {
            refreshStudent()
            
            // 发送批量积分变更通知
            let totalPointsChange = operations.totalPointsChange
            sendPointsChangeNotification(pointsChange: totalPointsChange, reason: "批量操作")
        }
        
        return successCount
    }
    
    /**
     * 从规则执行积分操作
     * @param rule 规则对象
     * @return 操作是否成功
     */
    func executeRuleOperation(_ rule: Rule) -> Bool {
        guard let operation = StudentPointsOperation.fromRule(rule) else {
            print("❌ 无法从规则创建积分操作")
            return false
        }
        
        return executePointsOperation(operation)
    }
    
    /**
     * 检查是否可以执行扣分操作
     * @param deductPoints 要扣除的积分
     * @return 是否可以执行
     */
    func canDeductPoints(_ deductPoints: Int) -> Bool {
        return Int(student.point) >= deductPoints
    }
    
    /**
     * 获取积分操作的预览信息
     * @param operations 积分操作数组
     * @return 预览信息字符串
     */
    func getOperationsPreview(_ operations: [StudentPointsOperation]) -> String {
        guard !operations.isEmpty else { return "" }
        
        let totalChange = operations.totalPointsChange
        let operationCount = operations.count
        let operationType = operations.first?.type.displayName ?? ""
        
        let changeText = totalChange > 0 ? "+\(totalChange)" : "\(totalChange)"
        return "将执行 \(operationCount) 个\(operationType)操作，总计 \(changeText) 分"
    }
    
    /**
     * 获取学生统计信息
     */
    func getStudentStatistics() -> CoreDataStudent.StudentStatistics {
        return student.getStatistics()
    }
    
    // MARK: - 兑换功能相关方法
    
    /**
     * 获取学生所在班级的奖品列表
     */
    func getClassPrizes() -> [Prize] {
        guard let schoolClass = student.schoolClass else { return [] }
        return schoolClass.sortedPrizes
    }
    
    /**
     * 执行奖品兑换
     * @param prize 奖品对象
     * @return 兑换是否成功
     */
    func redeemPrize(_ prize: Prize) -> Bool {
        let cost = Int(prize.cost)
        
        // 检查积分是否足够
        guard Int(student.point) >= cost else {
            print("❌ 兑换失败：积分不足，需要 \(cost) 分，当前 \(Int(student.point)) 分")
            return false
        }
        
        // 执行兑换
        guard coreDataManager.addRedemptionRecord(
            to: student,
            prizeName: prize.name ?? "",
            cost: cost
        ) != nil else {
            print("❌ 兑换失败：无法创建兑换记录")
            return false
        }
        
        // 刷新学生对象
        refreshStudent()
        
        print("✅ 兑换成功: \(prize.name ?? "")，消耗积分: \(cost)")
        return true
    }
    
    /**
     * 执行自定义兑换操作
     * @param operation 兑换操作对象
     * @return 兑换是否成功
     */
    func executeRedemptionOperation(_ operation: StudentRedemptionOperation) -> Bool {
        let totalCost = operation.totalCost
        
        // 检查积分是否足够
        guard Int(student.point) >= totalCost else {
            print("❌ 兑换失败：积分不足，需要 \(totalCost) 分，当前 \(Int(student.point)) 分")
            return false
        }
        
        // 检查操作是否有效
        guard operation.isValid else {
            print("❌ 兑换失败：兑换操作无效")
            return false
        }
        
        let context = coreDataManager.viewContext
        var successCount = 0
        
        context.performAndWait {
            do {
                // 执行所有兑换操作
                for item in operation.validItems {
                    let redemptionRecord = coreDataManager.addRedemptionRecord(
                        to: student,
                        prizeName: item.formattedName,
                        cost: item.cost
                    )
                    
                    if redemptionRecord != nil {
                        successCount += 1
                        print("✅ 兑换成功: \(item.formattedName)，消耗积分: \(item.cost)")
                    } else {
                        print("❌ 兑换失败: \(item.formattedName)")
                    }
                }
                
                // 保存所有更改
                try context.save()
                
            } catch {
                print("❌ 兑换操作保存失败: \(error)")
                successCount = 0
            }
        }
        
        if successCount > 0 {
            refreshStudent()
        }
        
        return successCount == operation.validItems.count
    }
    
    /**
     * 检查是否可以兑换指定奖品
     * @param prize 奖品对象
     * @return 是否可以兑换
     */
    func canRedeemPrize(_ prize: Prize) -> Bool {
        return Int(student.point) >= Int(prize.cost)
    }
    
    /**
     * 检查是否可以执行兑换操作
     * @param operation 兑换操作
     * @return 是否可以兑换
     */
    func canExecuteRedemptionOperation(_ operation: StudentRedemptionOperation) -> Bool {
        return operation.isValid && Int(student.point) >= operation.totalCost
    }
    
    /**
     * 获取兑换操作的预览信息
     * @param operation 兑换操作
     * @return 预览信息字符串
     */
    func getRedemptionOperationPreview(_ operation: StudentRedemptionOperation) -> String {
        let itemCount = operation.validItems.count
        let totalCost = operation.totalCost
        
        return "将兑换 \(itemCount) 个物品，总计消耗 \(totalCost) 分"
    }
    
    /**
     * 显示兑换选项弹窗
     */
    func showRedemptionOptions() {
        showingRedemptionOptions = true
    }
    
    /**
     * 隐藏兑换选项弹窗
     */
    func hideRedemptionOptions() {
        showingRedemptionOptions = false
    }
    
    /**
     * 显示兑换表单
     */
    func showRedemptionForm() {
        showingRedemptionForm = true
    }
    
    /**
     * 隐藏兑换表单
     */
    func hideRedemptionForm() {
        showingRedemptionForm = false
    }
    
    /**
     * 处理兑换按钮点击
     */
    func handleRedemptionButtonTapped() {
        showRedemptionOptions()
    }
    
    /**
     * 处理奖品兑换
     * @param prize 选中的奖品
     */
    func handlePrizeRedemption(_ prize: Prize) {
        let success = redeemPrize(prize)
        
        if success {
            // 隐藏弹窗
            hideRedemptionOptions()
            
            // 可以在这里添加成功提示
            print("🎉 兑换成功！")
        } else {
            // 可以在这里添加失败提示
            print("❌ 兑换失败！")
        }
    }
    
    /**
     * 处理自定义兑换
     * @param operation 兑换操作
     */
    func handleCustomRedemption(_ operation: StudentRedemptionOperation) {
        let success = executeRedemptionOperation(operation)
        
        if success {
            // 隐藏弹窗
            hideRedemptionForm()
            hideRedemptionOptions()
            
            // 可以在这里添加成功提示
            print("🎉 自定义兑换成功！")
        } else {
            // 可以在这里添加失败提示
            print("❌ 自定义兑换失败！")
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 刷新学生对象
     */
    private func refreshStudent() {
        // 刷新CoreData对象
        coreDataManager.viewContext.refresh(student, mergeChanges: true)
        
        // 触发UI更新
        objectWillChange.send()
    }
    
    /**
     * 从数据库重新加载学生数据
     */
    func refreshStudentFromDatabase() {
        guard let studentId = student.id else {
            print("⚠️ 无法刷新学生数据：学生ID为空")
            return
        }
        
        // 从数据库重新获取学生数据
        if let freshStudent = coreDataManager.getStudent(by: studentId) {
            // 更新当前学生对象的数据
            student.point = freshStudent.point
            
            // 刷新CoreData对象
            coreDataManager.viewContext.refresh(student, mergeChanges: true)
            
            // 触发UI更新
            objectWillChange.send()
            
            print("✅ 学生数据已从数据库刷新 - 积分: \(student.point)")
        } else {
            print("⚠️ 无法从数据库找到学生数据")
        }
    }
    
    // MARK: - Delete Record Methods
    
    /**
     * 准备删除记录（显示确认对话框）
     * @param record 要删除的记录
     */
    func prepareDeleteRecord(_ record: HistoryRecordProtocol) {
        recordToDelete = record
        showDeleteConfirmation = true
    }
    
    /**
     * 取消删除操作
     */
    func cancelDelete() {
        recordToDelete = nil
        showDeleteConfirmation = false
    }
    
    /**
     * 确认删除记录
     */
    func confirmDeleteRecord() {
        guard let record = recordToDelete else { return }
        
        let success = deleteRecord(record)
        
        // 重置状态
        recordToDelete = nil
        showDeleteConfirmation = false
        
        if success {
            print("✅ 记录删除成功: \(record.displayName)")
        } else {
            print("❌ 记录删除失败: \(record.displayName)")
        }
    }
    
    /**
     * 删除记录并回滚积分
     * @param record 要删除的记录
     * @return 是否删除成功
     */
    private func deleteRecord(_ record: HistoryRecordProtocol) -> Bool {
        guard record.canDelete else {
            print("⚠️ 记录不允许删除: \(record.displayName)")
            return false
        }
        
        let context = coreDataManager.viewContext
        
        // 开始CoreData事务
        let result = context.performAndWait { () -> Bool in
            // 根据记录类型执行不同的删除逻辑
            switch record.recordType {
            case .points:
                return deletePointRecord(record, in: context)
            case .redemption:
                return deleteRedemptionRecord(record, in: context)
            case .wheelLottery, .boxLottery, .scratchLottery:
                return deleteLotteryRecord(record, in: context)
            }
        }
        
        return result
    }
    
    /**
     * 删除积分记录
     */
    private func deletePointRecord(_ record: HistoryRecordProtocol, in context: NSManagedObjectContext) -> Bool {
        guard let pointRecord = student.sortedPointRecords.first(where: { $0.id == record.recordId }) else {
            return false
        }
        
        // 回滚积分：如果是加分记录，扣除积分；如果是扣分记录，返还积分
        let rollbackValue = -record.pointsValue
        
        // 检查回滚后积分是否为负
        let newPoints = Int(student.point) + rollbackValue
        if newPoints < 0 {
            print("⚠️ 删除积分记录失败：回滚后积分将为负数 (\(newPoints))")
            return false
        }
        
        // 执行积分回滚
        student.point = Int32(newPoints)
        
        // 删除记录
        context.delete(pointRecord)
        
        // 保存更改
        do {
            try context.save()
            refreshStudent()
            
            // 发送积分变更通知（回滚操作）
            sendPointsChangeNotification(pointsChange: rollbackValue, reason: "删除积分记录")
            
            return true
        } catch {
            print("❌ 删除积分记录失败: \(error)")
            return false
        }
    }
    
    /**
     * 删除兑换记录
     */
    private func deleteRedemptionRecord(_ record: HistoryRecordProtocol, in context: NSManagedObjectContext) -> Bool {
        guard let redemptionRecord = student.sortedRedemptionRecords.first(where: { $0.id == record.recordId }) else {
            return false
        }
        
        // 返还消耗的积分
        let returnPoints = Int(redemptionRecord.cost)
        student.point += Int32(returnPoints)
        
        // 删除记录
        context.delete(redemptionRecord)
        
        // 保存更改
        do {
            try context.save()
            refreshStudent()
            
            // 发送积分变更通知（返还积分）
            sendPointsChangeNotification(pointsChange: returnPoints, reason: "删除兑换记录")
            
            return true
        } catch {
            print("❌ 删除兑换记录失败: \(error)")
            return false
        }
    }
    
    /**
     * 删除抽奖记录
     */
    private func deleteLotteryRecord(_ record: HistoryRecordProtocol, in context: NSManagedObjectContext) -> Bool {
        guard let lotteryRecord = student.sortedLotteryRecords.first(where: { $0.id == record.recordId }) else {
            return false
        }
        
        // 返还消耗的积分
        let returnPoints = Int(lotteryRecord.cost)
        student.point += Int32(returnPoints)
        
        // 删除记录
        context.delete(lotteryRecord)
        
        // 保存更改
        do {
            try context.save()
            refreshStudent()
            
            // 发送积分变更通知（返还积分）
            sendPointsChangeNotification(pointsChange: returnPoints, reason: "删除抽奖记录")
            
            return true
        } catch {
            print("❌ 删除抽奖记录失败: \(error)")
            return false
        }
    }
    
    /**
     * 获取所有记录（使用协议接口）
     */
    var allRecordsWithProtocol: [HistoryRecordProtocol] {
        var allRecords: [HistoryRecordProtocol] = []
        
        // 添加积分记录
        allRecords.append(contentsOf: student.sortedPointRecords.filter { !$0.isReversed })
        
        // 添加兑换记录
        allRecords.append(contentsOf: student.sortedRedemptionRecords)
        
        // 添加抽奖记录
        allRecords.append(contentsOf: student.sortedLotteryRecords)
        
        // 按时间戳排序
        return allRecords.sorted { record1, record2 in
            let timestamp1 = record1.recordTimestamp ?? Date.distantPast
            let timestamp2 = record2.recordTimestamp ?? Date.distantPast
            return timestamp1 > timestamp2
        }
    }
    
    /**
     * 获取当前选中类型的记录（使用协议接口）
     */
    var filteredRecordsWithProtocol: [HistoryRecordProtocol] {
        switch selectedRecordType {
        case .points:
            return student.sortedPointRecords.filter { !$0.isReversed }
        case .exchange:
            var exchangeRecords: [HistoryRecordProtocol] = []
            exchangeRecords.append(contentsOf: student.sortedRedemptionRecords)
            exchangeRecords.append(contentsOf: student.sortedLotteryRecords)
            
            // 按时间戳排序
            return exchangeRecords.sorted { record1, record2 in
                let timestamp1 = record1.recordTimestamp ?? Date.distantPast
                let timestamp2 = record2.recordTimestamp ?? Date.distantPast
                return timestamp1 > timestamp2
            }
        }
    }
    
    // MARK: - 通知发送方法

    /**
     * 发送学生积分变更通知
     * @param pointsChange 积分变化量
     * @param reason 变更原因
     */
    private func sendPointsChangeNotification(pointsChange: Int, reason: String) {
        guard let studentId = student.id?.uuidString,
              let classId = student.schoolClass?.id?.uuidString else {
            print("⚠️ 无法发送通知：学生或班级ID为空")
            return
        }
        
        let userInfo: [String: Any] = [
            NotificationUserInfoKey.studentId: studentId,
            NotificationUserInfoKey.pointsChange: pointsChange,
            NotificationUserInfoKey.reason: reason,
            NotificationUserInfoKey.classId: classId,
            NotificationUserInfoKey.triggerSource: "student_detail"
        ]
        
        // 发送学生积分变更通知
        NotificationCenter.default.post(
            name: .studentPointsDidChange,
            object: nil,
            userInfo: userInfo
        )
        
        // 发送班级统计刷新通知
        NotificationCenter.default.post(
            name: .classStatisticsNeedsRefresh,
            object: nil,
            userInfo: [
                NotificationUserInfoKey.classId: classId,
                NotificationUserInfoKey.triggerSource: "student_detail"
            ]
        )
        
        print("📤 已发送积分变更通知: 学生\(studentId) 积分变化\(pointsChange)")
    }
    
    // MARK: - Lottery Methods
    
    /**
     * 获取学生所在的班级
     * @return 学生所在的班级，如果没有则返回nil
     */
    func getStudentSchoolClass() -> SchoolClass {
        guard let schoolClass = student.schoolClass else {
            // 如果学生没有关联班级，创建一个临时的空班级对象避免崩溃
            let context = coreDataManager.viewContext
            let tempClass = SchoolClass(context: context)
            tempClass.id = UUID()
            tempClass.name = "未知班级"
            return tempClass
        }
        return schoolClass
    }
    
    /**
     * 扣除抽奖积分
     * @param cost 消耗的积分
     * @return 是否扣除成功
     */
    func deductPointsForLottery(_ cost: Int) -> Bool {
        guard Int(student.point) >= cost else {
            print("❌ 积分不足：当前积分 \(Int(student.point))，需要 \(cost)")
            return false
        }
        
        // 扣除积分
        student.point -= Int32(cost)
        
        // 保存更改
        do {
            try coreDataManager.viewContext.save()
            refreshStudent()
            
            // 发送积分变更通知
            sendPointsChangeNotification(pointsChange: -cost, reason: "抽奖消耗")
            
            return true
        } catch {
            print("❌ 扣除抽奖积分失败: \(error)")
            return false
        }
    }
    
    /**
     * 创建抽奖记录
     * @param type 抽奖类型
     * @param prize 中奖奖品
     * @param cost 消耗积分
     * @return 是否创建成功
     */
    func createLotteryRecord(type: HistoryRecordType, prize: String, cost: Int) -> Bool {
        let lotteryRecord = coreDataManager.addLotteryRecord(
            to: student,
            toolType: type.rawValue,
            prizeResult: prize,
            cost: cost
        )
        
        if lotteryRecord != nil {
            refreshStudent()
            print("✅ 创建抽奖记录成功: 类型 \(type.rawValue)，奖品 \(prize)，消耗 \(cost) 积分")
            return true
        } else {
            print("❌ 创建抽奖记录失败")
            return false
        }
    }
}

// MARK: - Legacy Support

/**
 * 历史记录数据模型（兼容旧代码）
 */
struct HistoryRecord: Identifiable {
    var id = UUID()
    let studentId: String
    let type: StudentDetailViewModel.RecordType
    let action: String
    let points: Int
    let timestamp: Date
    let reason: String?
    
    /**
     * 格式化显示时间
     */
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: timestamp)
    }
    
    /**
     * 格式化显示分值
     */
    var formattedPoints: String {
        if points > 0 {
            return "+\(points)"
        } else {
            return "\(points)"
        }
    }
    
    /**
     * 分值颜色
     */
    var pointsColor: String {
        return points > 0 ? "#26C34B" : "#FF5B5B"
    }
    
    /**
     * 初始化历史记录
     */
    init(studentId: String, type: StudentDetailViewModel.RecordType, action: String, points: Int, reason: String? = nil) {
        self.studentId = studentId
        self.type = type
        self.action = action
        self.points = points
        self.timestamp = Date()
        self.reason = reason
    }
} 