//
//  BackgroundImageLayer.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 背景图层管理组件
 * 负责管理初级会员和高级会员背景图的切换显示
 * 横向填满整个屏幕，位于分段选项卡下方
 * 支持用户手动调整背景图的大小、位置和特效
 */
struct BackgroundImageLayer: View {
    
    // MARK: - Properties
    @Binding var selectedMembershipType: Int // 0: 初级会员, 1: 高级会员
    
    // MARK: - State
    @State private var backgroundOpacity: [Double] = [0.0, 1.0] // [初级, 高级]
    @State private var layerAppeared = false
    @State private var backgroundScale: CGFloat = 1.1
    @State private var gradientOpacity: Double = 0.0
    
    var body: some View {
        ZStack {
            // 初级会员背景图
            createBackgroundImage(
                imageName: DesignSystem.SubscriptionPage.BackgroundImage.primaryImageName,
                opacity: backgroundOpacity[0],
                isSelected: selectedMembershipType == 0
            )
            
            // 高级会员背景图
            createBackgroundImage(
                imageName: DesignSystem.SubscriptionPage.BackgroundImage.advancedImageName,
                opacity: backgroundOpacity[1],
                isSelected: selectedMembershipType == 1
            )
            
            // 渐变叠加层 - 增强视觉深度
            createGradientOverlay()
        }
        .ignoresSafeArea(.all) // 横向填满整个屏幕
        .allowsHitTesting(false) // 不拦截触摸事件，让底层组件可交互
        .opacity(layerAppeared ? 1.0 : 0.0)
        .animation(.easeIn(duration: 0.6).delay(0.2), value: layerAppeared)
        .onChange(of: selectedMembershipType) { newValue in
            switchBackgroundImage(to: newValue)
        }
        .onAppear {
            layerAppeared = true
            // 初始化背景图状态
            switchBackgroundImage(to: selectedMembershipType)
            
            // 启动渐变叠加动画
            withAnimation(.easeIn(duration: 1.0).delay(0.5)) {
                gradientOpacity = 0.3
            }
            
            // 启动缩放动画
            withAnimation(.easeOut(duration: 1.2).delay(0.3)) {
                backgroundScale = 1.0
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 创建可调整的背景图组件
     * - Parameters:
     *   - imageName: 图片名称
     *   - opacity: 透明度
     *   - isSelected: 是否为当前选中状态
     */
    private func createBackgroundImage(imageName: String, opacity: Double, isSelected: Bool) -> some View {
        let config = DesignSystem.AdaptiveLayout.SubscriptionPage.BackgroundImage.self
        
        return GeometryReader { geometry in
            Image(imageName)
                .resizable()
                .aspectRatio(contentMode: getContentMode())
                // 应用尺寸缩放 - 结合动态缩放效果
                .scaleEffect(
                    x: config.scaleX * config.uniformScale * (isSelected ? backgroundScale : backgroundScale * 0.95),
                    y: config.scaleY * config.uniformScale * (isSelected ? backgroundScale : backgroundScale * 0.95)
                )
                // 应用位置偏移
                .offset(
                    x: config.offsetX,
                    y: config.offsetY
                )
                // 应用裁剪 - 使用frame来实现裁剪效果
                .frame(
                    width: geometry.size.width * (1 - config.cropLeft - config.cropRight),
                    height: geometry.size.height * (1 - config.cropTop - config.cropBottom)
                )
                .clipped()
                // 应用特效
                .blur(radius: config.blur)
                .brightness(config.brightness)
                .contrast(config.contrast)
                // 应用透明度
                .opacity(opacity * config.opacity)
                // 应用动画
                .animation(
                    .easeInOut(duration: config.animationDuration),
                    value: opacity
                )
        }
        // 应用层级
        .zIndex(config.zIndex)
    }
    
    /**
     * 根据对齐模式获取内容模式
     */
    private func getContentMode() -> ContentMode {
        switch DesignSystem.AdaptiveLayout.SubscriptionPage.BackgroundImage.alignmentMode {
        case .fill:
            return .fill
        case .fit:
            return .fit
        case .stretch:
            return .fill // SwiftUI中没有直接的stretch模式，使用fill代替
        }
    }
    
    /**
     * 切换背景图
     * - Parameter type: 会员类型 (0: 初级, 1: 高级)
     */
    private func switchBackgroundImage(to type: Int) {
        withAnimation(.easeInOut(duration: DesignSystem.AdaptiveLayout.SubscriptionPage.BackgroundImage.animationDuration)) {
            if type == 0 {
                // 显示初级会员背景
                backgroundOpacity = [1.0, 0.0]
            } else {
                // 显示高级会员背景
                backgroundOpacity = [0.0, 1.0]
            }
        }
        
        // 切换时重置缩放动画
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            backgroundScale = 1.05
        }
        
        // 恢复缩放
        withAnimation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.2)) {
            backgroundScale = 1.0
        }
    }
    
    /**
     * 创建渐变叠加层
     * 增强视觉深度和整体美感
     */
    private func createGradientOverlay() -> some View {
        ZStack {
            // 顶部渐变 - 从透明到半透明
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color.white.opacity(0.1), location: 0.0),
                    .init(color: Color.clear, location: 0.3)
                ]),
                startPoint: .top,
                endPoint: .center
            )
            .opacity(gradientOpacity)
            
            // 底部渐变 - 增强层次感
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color.clear, location: 0.6),
                    .init(color: Color.black.opacity(0.05), location: 1.0)
                ]),
                startPoint: .center,
                endPoint: .bottom
            )
            .opacity(gradientOpacity)
            
            // 径向渐变 - 中心高光效果
            RadialGradient(
                gradient: Gradient(stops: [
                    .init(color: Color.white.opacity(0.08), location: 0.0),
                    .init(color: Color.clear, location: 0.8)
                ]),
                center: UnitPoint(x: 0.3, y: 0.4),
                startRadius: 0,
                endRadius: 300
            )
            .opacity(gradientOpacity * 0.7)
        }
        .ignoresSafeArea(.all)
        .allowsHitTesting(false)
        .animation(.easeInOut(duration: 0.8), value: gradientOpacity)
    }
}

// MARK: - Preview
#Preview {
    struct PreviewWrapper: View {
        @State var selectedType = 1
        
        var body: some View {
            ZStack {
                // 模拟底层组件
                VStack {
                    Rectangle()
                        .fill(Color.blue.opacity(0.3))
                        .frame(height: 100)
                        .overlay(Text("模拟底层组件"))
                    Spacer()
                }
                
                // 背景图层
                BackgroundImageLayer(selectedMembershipType: $selectedType)
                
                // 模拟分段选项卡
                VStack {
                    Spacer()
                    HStack {
                        Button("初级会员") {
                            selectedType = 0
                        }
                        .padding()
                        .background(Color.white.opacity(0.8))
                        .cornerRadius(10)
                        
                        Button("高级会员") {
                            selectedType = 1
                        }
                        .padding()
                        .background(Color.white.opacity(0.8))
                        .cornerRadius(10)
                    }
                    Spacer()
                }
            }
        }
    }
    
    return PreviewWrapper()
}