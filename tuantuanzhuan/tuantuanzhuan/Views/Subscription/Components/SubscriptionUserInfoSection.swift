//
//  SubscriptionUserInfoSection.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 订阅页面个人信息组件
 * 重构版本：所有元素使用绝对定位，互不影响
 * 每个元素都有独立的坐标系统
 */
struct SubscriptionUserInfoSection: View {
    
    // MARK: - Properties
    let userName: String
    let userID: String
    let membershipLevel: String
    let expirationDate: String
    let onBackPressed: () -> Void
    
    // MARK: - State
    @State private var sectionAppeared = false
    @State private var crownRotation: Double = 0
    
    var body: some View {
        GeometryReader { geometry in
            setupAbsoluteLayout(geometry: geometry)
        }
        .onAppear {
            withAnimation {
                sectionAppeared = true
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置绝对定位布局
     * 每个元素都有独立的坐标系统，互不影响
     */
    private func setupAbsoluteLayout(geometry: GeometryProxy) -> some View {
        let screenWidth = geometry.size.width
        let screenHeight = geometry.size.height
        
        return ZStack {
            // 背景容器 - 占满整个区域
            createBackgroundContainer()
                .frame(width: screenWidth, height: screenHeight)
                .position(x: screenWidth / 2, y: screenHeight / 2)
            
            // 装饰性背景元素 - 绝对定位
            createDecorationCircles(screenWidth: screenWidth, screenHeight: screenHeight)
            
            // 返回按钮 - 绝对定位，独立坐标系
            createBackButton()
                .position(
                    x: DesignSystem.SubscriptionPage.UserInfoSection.backButtonPositionX,
                    y: DesignSystem.SubscriptionPage.UserInfoSection.backButtonPositionY
                )
            
            // 用户头像 - 绝对定位，独立坐标系
            createUserAvatar()
                .position(
                    x: DesignSystem.SubscriptionPage.UserInfoSection.avatarPositionX,
                    y: DesignSystem.SubscriptionPage.UserInfoSection.avatarPositionY
                )
            
            // 用户信息 - 绝对定位，独立坐标系
            createUserInfo()
                .position(
                    x: DesignSystem.SubscriptionPage.UserInfoSection.userInfoPositionX,
                    y: screenHeight / 2 + DesignSystem.SubscriptionPage.UserInfoSection.userInfoPositionY
                )
            
            // 皇冠图标 - 绝对定位，独立坐标系
            createCrownIcon()
                .position(
                    x: screenWidth - DesignSystem.SubscriptionPage.UserInfoSection.crownPositionOffsetX,
                    y: DesignSystem.SubscriptionPage.UserInfoSection.crownPositionY
                )
        }
    }
    
    /**
     * 创建背景容器
     */
    private func createBackgroundContainer() -> some View {
        RoundedRectangle(cornerRadius: DesignSystem.SubscriptionPage.UserInfoSection.cornerRadius)
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [
                        DesignSystem.Colors.profileUserInfoBackground,
                        DesignSystem.Colors.profileUserInfoBackground.opacity(0.9)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .ignoresSafeArea(.all, edges: [.top, .leading, .trailing])
            .shadow(color: Color(hex: "#a9d051").opacity(0.15), radius: 8, x: 0, y: 4)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.SubscriptionPage.UserInfoSection.cornerRadius)
                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
            )
    }
    
    /**
     * 创建装饰性背景圆圈
     * 增强版本 - 参考个人中心页面设计，添加动画效果  
     */
    private func createDecorationCircles(screenWidth: CGFloat, screenHeight: CGFloat) -> some View {
        Group {
            // 主要装饰圆圈 - 绿色大圆
            Circle()
                .fill(Color(hex: "#B5E36B").opacity(0.08))
                .frame(width: 100, height: 100)
                .position(x: screenWidth / 2 + 120, y: screenHeight / 2 - 80)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .scaleEffect(sectionAppeared ? 1.0 : 0.6)
                .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.5), value: sectionAppeared)
            
            // 次要装饰圆圈 - 黄色中圆
            Circle()
                .fill(Color(hex: "#FFE49E").opacity(0.06))
                .frame(width: 70, height: 70)
                .position(x: screenWidth / 2 - 100, y: screenHeight / 2 + 60)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .scaleEffect(sectionAppeared ? 1.0 : 0.6)
                .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.6), value: sectionAppeared)
            
            // 辅助装饰圆圈 - 白色小圆
            Circle()
                .fill(Color.white.opacity(0.4))
                .frame(width: 50, height: 50)
                .position(x: screenWidth / 2 + 140, y: screenHeight / 2 + 40)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .scaleEffect(sectionAppeared ? 1.0 : 0.6)
                .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.7), value: sectionAppeared)
            
            // 新增装饰圆圈 - 绿色小圆（左上角）
            Circle()
                .fill(Color(hex: "#B5E36B").opacity(0.04))
                .frame(width: 60, height: 60)
                .position(x: screenWidth / 2 - 130, y: screenHeight / 2 - 50)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .scaleEffect(sectionAppeared ? 1.0 : 0.6)
                .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.8), value: sectionAppeared)
            
            // 新增装饰圆圈 - 黄色微圆（右下角远处）
            Circle()
                .fill(Color(hex: "#FFE49E").opacity(0.03))
                .frame(width: 40, height: 40)
                .position(x: screenWidth / 2 + 160, y: screenHeight / 2 + 90)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .scaleEffect(sectionAppeared ? 1.0 : 0.6)
                .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.9), value: sectionAppeared)
        }
    }
    
    /**
     * 创建返回按钮
     */
    private func createBackButton() -> some View {
        Button(action: onBackPressed) {
            ZStack {
                Circle()
                    .fill(DesignSystem.SubscriptionPage.BackButton.backgroundColor)
                    .frame(
                        width: DesignSystem.SubscriptionPage.BackButton.size,
                        height: DesignSystem.SubscriptionPage.BackButton.size
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                
                Image("fanhui")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(
                        width: DesignSystem.SubscriptionPage.BackButton.iconSize,
                        height: DesignSystem.SubscriptionPage.BackButton.iconSize
                    )
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(sectionAppeared ? 1.0 : 0.8)
        .opacity(sectionAppeared ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: sectionAppeared)
    }
    
    /**
     * 创建用户头像
     */
    private func createUserAvatar() -> some View {
        ZStack {
            Circle()
                .fill(Color.white)
                .frame(width: 70, height: 70)
                .shadow(color: Color.black.opacity(0.08), radius: 4, x: 0, y: 2)
            
            // 头像背景光圈
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.3),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: 35
                    )
                )
                .frame(width: 70, height: 70)
            
            Image("laoshi") // 使用统一的用户头像
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(
                    width: DesignSystem.SubscriptionPage.UserInfoSection.avatarSize,
                    height: DesignSystem.SubscriptionPage.UserInfoSection.avatarSize
                )
                .background(
                    Circle()
                        .fill(Color.white.opacity(0.5))
                )
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color.white.opacity(0.3), lineWidth: 2)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
        }
        .scaleEffect(sectionAppeared ? 1.0 : 0.8)
        .opacity(sectionAppeared ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: sectionAppeared)
    }
    
    /**
     * 创建用户信息
     */
    private func createUserInfo() -> some View {
        VStack(alignment: .leading, spacing: 6) {
            // 姓名和会员标签
            HStack(spacing: 8) {
                Text(userName)
                    .font(.system(size: DesignSystem.SubscriptionPage.UserInfoSection.nameFont, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.profileSettingsTextColor)
                    .shadow(color: Color.white.opacity(0.5), radius: 1, x: 0, y: 1)
                
                // 会员等级标签
                Text("(\(membershipLevel))")
                    .font(.system(size: DesignSystem.SubscriptionPage.UserInfoSection.membershipFont, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 3)
                    .background(Color.white.opacity(0.7))
                    .cornerRadius(8)
                    .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
            }
            
            // 用户ID - 与个人中心保持一致的显示格式
            Text("profile.user_info.id_label".localized(with: userID))
                .font(.system(size: DesignSystem.SubscriptionPage.UserInfoSection.idFont, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            // 会员到期时间 - 与个人中心保持一致的显示格式
            Text("profile.user_info.membership_expires".localized(with: expirationDate))
                .font(.system(size: DesignSystem.SubscriptionPage.UserInfoSection.dateFont, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .opacity(sectionAppeared ? 1.0 : 0.0)
        .offset(x: sectionAppeared ? 0 : -20, y: 0)
        .animation(.easeOut(duration: 0.8).delay(0.3), value: sectionAppeared)
    }
    
    /**
     * 创建皇冠图标
     * 增强版本 - 添加特殊的闪烁和光芒效果
     */
    private func createCrownIcon() -> some View {
        ZStack {
            // 背景光环效果
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#FFE49E").opacity(0.3),
                            Color(hex: "#FFE49E").opacity(0.1),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: 60
                    )
                )
                .frame(width: 120, height: 120)
                .opacity(sectionAppeared ? 0.8 : 0.0)
                .scaleEffect(sectionAppeared ? 1.0 : 0.5)
                .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: sectionAppeared)
            
            // 主皇冠图标
            Image("皇冠(订阅）")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(
                    width: DesignSystem.SubscriptionPage.UserInfoSection.crownSize,
                    height: DesignSystem.SubscriptionPage.UserInfoSection.crownSize
                )
                .rotationEffect(.degrees(DesignSystem.SubscriptionPage.UserInfoSection.crownRotation + crownRotation))
                .shadow(color: Color(hex: "#FFE49E").opacity(0.4), radius: 8, x: 0, y: 0)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .offset(x: sectionAppeared ? 0 : 30)
                .scaleEffect(sectionAppeared ? 1.0 : 0.6)
                .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.4), value: sectionAppeared)
            
            // 闪烁星星效果
            ForEach(0..<6, id: \.self) { index in
                createSparkleEffect(index: index)
            }
        }
        .onAppear {
            // 持续的轻微摆动动画
            withAnimation(.easeInOut(duration: 4.0).repeatForever(autoreverses: true)) {
                crownRotation = 8
            }
        }
    }
    
    /**
     * 创建闪烁星星效果
     */
    private func createSparkleEffect(index: Int) -> some View {
        let angle = Double(index) * 60.0 // 每个星星间隔60度
        let distance: CGFloat = 50 + CGFloat(index % 2) * 15 // 不同距离
        
        return Circle()
            .fill(Color.white)
            .frame(width: 4, height: 4)
            .offset(
                x: cos(angle * .pi / 180) * distance,
                y: sin(angle * .pi / 180) * distance
            )
            .opacity(sectionAppeared ? 1.0 : 0.0)
            .scaleEffect(sectionAppeared ? 1.0 : 0.3)
            .animation(
                .easeInOut(duration: 1.5 + Double(index) * 0.2)
                .repeatForever(autoreverses: true)
                .delay(Double(index) * 0.3),
                value: sectionAppeared
            )
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color(hex: "#f8fdf0")
            .ignoresSafeArea()
        
        SubscriptionUserInfoSection(
            userName: "user_info.teacher_nickname".localized,
            userID: "123456",
            membershipLevel: "subscription.user_level_regular".localized,
            expirationDate: "未开通"
        ) {
            print("返回按钮被点击")
        }
        .frame(height: 300)
    }
} 