//
//  LoginView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI
import AuthenticationServices

/**
 * 登录页面视图
 * 包含登录页面logo、Apple登录按钮和用户协议复选框
 */
struct LoginView: View {
    
    // MARK: - Environment
    @Environment(\.colorScheme) private var colorScheme
    
    // MARK: - Dependencies
    @ObservedObject var authManager: AuthenticationManager
    
    // MARK: - State
    @StateObject private var viewModel: LoginViewModel
    @State private var agreementAccepted: Bool = false
    @State private var showUserAgreement: Bool = false
    @State private var showPrivacyPolicy: Bool = false
    @State private var showChildrenPrivacyPolicy: Bool = false
    
    // MARK: - Initialization
    init(authManager: AuthenticationManager) {
        self.authManager = authManager
        self._viewModel = StateObject(wrappedValue: LoginViewModel(authManager: authManager))
    }
    
    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                createBackgroundGradient()
                
                // 主要内容
                VStack(spacing: 0) {
                    Spacer()
                    
                    // Logo区域
                    createLogoSection()
                    
                    Spacer()
                    
                    // 登录按钮区域
                    createLoginSection()
                    
                    // 用户协议区域
                    createAgreementSection()
                    
                    Spacer()
                        .frame(height: geometry.safeAreaInsets.bottom + 40)
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
            }
        }
        .ignoresSafeArea()
        .sheet(isPresented: $showUserAgreement) {
            UserAgreementView()
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            PrivacyPolicyView()
        }
        .sheet(isPresented: $showChildrenPrivacyPolicy) {
            ChildrenPrivacyPolicyView()
        }
        .alert("login.error.title".localized, isPresented: $viewModel.showError) {
            Button("common.button.ok".localized) {
                viewModel.showError = false
            }
        } message: {
            Text(viewModel.errorMessage)
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 创建背景渐变
     */
    private func createBackgroundGradient() -> some View {
        LinearGradient(
            gradient: Gradient(colors: [
                DesignSystem.Colors.background,
                DesignSystem.Colors.primaryLight.opacity(0.3),
                DesignSystem.Colors.background
            ]),
            startPoint: .topTrailing,
            endPoint: .bottomLeading
        )
    }
    
    /**
     * 创建Logo区域
     */
    private func createLogoSection() -> some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // Logo图片
            Image("登录页面logo")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 160, height: 160)
                .background(
                    Circle()
                        .fill(Color.white.opacity(0.1))
                        .frame(width: 180, height: 180)
                )
                .shadow(color: DesignSystem.Colors.primary.opacity(0.2), radius: 20, x: 0, y: 10)
        }
    }
    
    /**
     * 创建登录按钮区域
     */
    private func createLoginSection() -> some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // Apple登录按钮
            SignInWithAppleButton(
                onRequest: { request in
                    viewModel.handleSignInWithAppleRequest(request)
                },
                onCompletion: { result in
                    viewModel.handleSignInWithAppleCompletion(result)
                }
            )
            .signInWithAppleButtonStyle(colorScheme == .dark ? .white : .black)
            .frame(height: 50)
            .cornerRadius(25)
            .disabled(!agreementAccepted)
            .opacity(agreementAccepted ? 1.0 : 0.6)
            .overlay(
                RoundedRectangle(cornerRadius: 25)
                    .stroke(DesignSystem.Colors.primary.opacity(agreementAccepted ? 0.3 : 0.1), lineWidth: 1)
            )
        }
    }
    
    /**
     * 创建用户协议区域
     */
    private func createAgreementSection() -> some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            // 协议勾选
            Button(action: {
                agreementAccepted.toggle()
                
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }) {
                HStack(alignment: .top, spacing: 14) {
                    // 勾选框 - 固定在顶部
                    ZStack {
                        RoundedRectangle(cornerRadius: 5)
                            .stroke(DesignSystem.Colors.primary, lineWidth: 1.5)
                            .frame(width: 22, height: 22)
                            .background(
                                RoundedRectangle(cornerRadius: 5)
                                    .fill(Color.white.opacity(0.1))
                            )
                        
                        if agreementAccepted {
                            RoundedRectangle(cornerRadius: 5)
                                .fill(DesignSystem.Colors.primary)
                                .frame(width: 22, height: 22)
                            
                            Image(systemName: "checkmark")
                                .font(.system(size: 13, weight: .bold))
                                .foregroundColor(.white)
                        }
                    }
                    .padding(.top, 1) // 微调对齐
                    .animation(.easeInOut(duration: 0.2), value: agreementAccepted)
                    
                    // 协议文本 - 允许多行显示
                    createAgreementText()
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding(.horizontal, 4)
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.top, DesignSystem.Spacing.lg)
            .padding(.horizontal, DesignSystem.Spacing.sm)
        }
    }
    
    /**
     * 创建协议文本
     */
    private func createAgreementText() -> some View {
        VStack(alignment: .leading, spacing: 6) {
            // 第一行：前缀文本 + 用户协议
            HStack(spacing: 2) {
                Text("login.agreement.prefix".localized)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .lineLimit(nil)
                
                Button(action: {
                    showUserAgreement = true
                    // 添加触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }) {
                    Text("login.agreement.user_agreement".localized)
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.primary)
                        .underline(true, color: DesignSystem.Colors.primary)
                }
                
                Text("login.agreement.and".localized)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Spacer(minLength: 0)
            }
            
            // 第二行：隐私政策相关
            HStack(spacing: 2) {
                Button(action: {
                    showPrivacyPolicy = true
                    // 添加触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }) {
                    Text("login.agreement.privacy_policy".localized)
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.primary)
                        .underline(true, color: DesignSystem.Colors.primary)
                }
                
                Text("login.agreement.and".localized)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Button(action: {
                    showChildrenPrivacyPolicy = true
                    // 添加触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }) {
                    Text("login.agreement.children_privacy_policy".localized)
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.primary)
                        .underline(true, color: DesignSystem.Colors.primary)
                        .lineLimit(nil)
                }
                
                Spacer(minLength: 0)
            }
        }
        .fixedSize(horizontal: false, vertical: true) // 允许垂直扩展
    }
}

// MARK: - Login ViewModel

/**
 * 登录页面视图模型
 */
class LoginViewModel: ObservableObject {
    
    @Published var showError: Bool = false
    @Published var errorMessage: String = ""
    @Published var isLoading: Bool = false
    
    // MARK: - Dependencies
    private let authManager: AuthenticationManager
    
    // MARK: - Initialization
    init(authManager: AuthenticationManager) {
        self.authManager = authManager
    }
    
    /**
     * 处理Apple登录请求
     */
    func handleSignInWithAppleRequest(_ request: ASAuthorizationAppleIDRequest) {
        request.requestedScopes = [.fullName, .email]
        isLoading = true
    }
    
    /**
     * 处理Apple登录完成回调
     */
    func handleSignInWithAppleCompletion(_ result: Result<ASAuthorization, Error>) {
        isLoading = false
        
        switch result {
        case .success(let authorization):
            handleSuccessfulAuthorization(authorization)
        case .failure(let error):
            handleAuthorizationError(error)
        }
    }
    
    /**
     * 处理登录成功
     */
    private func handleSuccessfulAuthorization(_ authorization: ASAuthorization) {
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential else {
            showErrorMessage("login.error.invalid_credential".localized)
            return
        }
        
        // 获取用户信息
        let userID = appleIDCredential.user
        let fullName = appleIDCredential.fullName
        let email = appleIDCredential.email
        
        print("Apple登录成功:")
        print("用户ID: \(userID)")
        print("姓名: \(fullName?.formatted() ?? "未提供")")
        print("邮箱: \(email ?? "未提供")")
        
        // 通过AuthenticationManager处理登录成功
        authManager.handleSuccessfulLogin(userID: userID, fullName: fullName, email: email)
    }
    
    /**
     * 处理登录错误
     */
    private func handleAuthorizationError(_ error: Error) {
        let authError = error as? ASAuthorizationError
        
        switch authError?.code {
        case .canceled:
            // 用户取消登录，不显示错误
            break
        case .failed:
            showErrorMessage("login.error.failed".localized)
        case .invalidResponse:
            showErrorMessage("login.error.invalid_response".localized)
        case .notHandled:
            showErrorMessage("login.error.not_handled".localized)
        case .unknown:
            showErrorMessage("login.error.unknown".localized)
        default:
            showErrorMessage("login.error.general".localized)
        }
        
        // 通过AuthenticationManager处理登录失败
        authManager.handleLoginFailure(error)
    }
    
    /**
     * 显示错误消息
     */
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
}



// MARK: - Preview

#Preview {
    LoginView(authManager: AuthenticationManager())
}