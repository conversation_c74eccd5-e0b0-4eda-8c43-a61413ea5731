//
//  SystemSettingsSection.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 设置项类型枚举
 */
enum SettingType: CaseIterable {
    case language
    case feedback
    case about
    case deleteAccount
    case logout
    
    var displayName: String {
        switch self {
        case .language:
            return "settings.item.language".localized
        case .feedback:
            return "settings.item.feedback".localized
        case .about:
            return "settings.item.about".localized
        case .deleteAccount:
            return "settings.item.delete_account".localized
        case .logout:
            return "settings.item.logout".localized
        }
    }
    
    var iconName: String {
        switch self {
        case .language:
            return "yuyan"
        case .feedback:
            return "guzhangfankui"
        case .about:
            return "guanyu"
        case .deleteAccount:
            return "shanchu"
        case .logout:
            return "tuichu"
        }
    }
    
    var isDestructive: Bool {
        switch self {
        case .deleteAccount, .logout:
            return true
        default:
            return false
        }
    }
}

/**
 * 系统设置组件
 * 包含6个设置项的列表布局
 */
struct SystemSettingsSection: View {
    
    // MARK: - Properties
    let onSettingItemPressed: (SettingType) -> Void
    
    // MARK: - State
    @State private var sectionAppeared = false
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // 标题
            HStack {
                Text("settings.title".localized)
                    .font(.system(
                        size: DesignSystem.Typography.HeadingMedium.fontSize,
                        weight: DesignSystem.Typography.HeadingMedium.fontWeight
                    ))
                    .foregroundColor(DesignSystem.Colors.profileSettingsTextColor)
                
                Spacer()
            }
            .opacity(sectionAppeared ? 1.0 : 0.0)
            .offset(y: sectionAppeared ? 0 : -10)
            .animation(.easeOut(duration: 0.6).delay(0.1), value: sectionAppeared)
            
            // 设置项列表
            VStack(spacing: DesignSystem.ProfilePage.SettingsItem.spacing) {
                ForEach(SettingType.allCases.indices, id: \.self) { index in
                    let settingType = SettingType.allCases[index]
                    
                    SettingsItemView(
                        settingType: settingType,
                        onTapped: {
                            onSettingItemPressed(settingType)
                        }
                    )
                    .opacity(sectionAppeared ? 1.0 : 0.0)
                    .offset(y: sectionAppeared ? 0 : 20)
                    .animation(.easeOut(duration: 0.6).delay(0.2 + Double(index) * 0.1), value: sectionAppeared)
                }
            }
        }
        .onAppear {
            withAnimation {
                sectionAppeared = true
            }
        }
    }
}

/**
 * 设置项视图
 */
private struct SettingsItemView: View {
    
    let settingType: SettingType
    let onTapped: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                isPressed = true
                onTapped()
            }
            
            // 恢复按钮状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                isPressed = false
            }
        }) {
            ZStack {
                // 背景容器
                RoundedRectangle(cornerRadius: DesignSystem.ProfilePage.SettingsItem.cornerRadius)
                    .fill(Color(hex: "#f6fbe9"))
                    .frame(height: DesignSystem.ProfilePage.SettingsItem.height)
                    .shadow(color: Color.black.opacity(0.04), radius: 4, x: 0, y: 2)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.ProfilePage.SettingsItem.cornerRadius)
                            .stroke(Color.white.opacity(0.8), lineWidth: 1)
                    )
                
                // 内容布局
                HStack(spacing: DesignSystem.ProfilePage.SettingsItem.spacing) {
                    // 左侧图标
                    Image(settingType.iconName)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(
                            width: DesignSystem.ProfilePage.SettingsItem.iconSize,
                            height: DesignSystem.ProfilePage.SettingsItem.iconSize
                        )
                        .foregroundColor(DesignSystem.Colors.profileSettingsIconColor)
                    
                    // 设置项文字
                    Text(settingType.displayName)
                        .font(.system(
                            size: DesignSystem.ProfilePage.SettingsItem.textFont,
                            weight: .medium
                        ))
                        .foregroundColor(Color(hex: "#808080"))
                    
                    Spacer()
                    
                    // 右侧箭头
                    Image(systemName: "chevron.right")
                        .font(.system(
                            size: DesignSystem.ProfilePage.SettingsItem.arrowSize,
                            weight: .medium
                        ))
                        .foregroundColor(DesignSystem.Colors.profileSettingsArrowColor)
                }
                .padding(.horizontal, DesignSystem.ProfilePage.SettingsItem.padding)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
    }
}

// MARK: - Preview
#Preview {
    SystemSettingsSection { settingType in
        print("设置项被点击: \(settingType.displayName)")
    }
    .padding()
    .background(DesignSystem.Colors.background)
}