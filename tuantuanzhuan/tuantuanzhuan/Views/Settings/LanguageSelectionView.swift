//
//  LanguageSelectionView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 语言选择页面
 * 提供中英文切换功能界面
 */
struct LanguageSelectionView: View {
    
    // MARK: - Environment
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var localizationManager = LocalizationManager.shared
    
    // MARK: - State
    @State private var selectedLanguage: SupportedLanguage
    @State private var pageAppeared = false
    
    // MARK: - Initialization
    init() {
        _selectedLanguage = State(initialValue: LocalizationManager.shared.currentLanguage)
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // 美化背景渐变
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color(hex: "#fcfff4"), location: 0.0),
                        .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                        .init(color: Color.white, location: 0.7),
                        .init(color: Color(hex: "#fafffe"), location: 1.0)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea(.all)
                
                // 装饰性背景元素
                VStack {
                    HStack {
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.03))
                            .frame(width: 100, height: 100)
                            .offset(x: -30, y: 20)
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#FFE49E").opacity(0.04))
                            .frame(width: 120, height: 120)
                            .offset(x: 40, y: -10)
                    }
                    Spacer()
                }
                
                VStack(spacing: DesignSystem.Spacing.lg) {
                    // 说明文本
                    VStack(spacing: DesignSystem.Spacing.sm) {
                        Text("".localized)
                            .font(.system(
                                size: DesignSystem.Typography.HeadingMedium.fontSize,
                                weight: DesignSystem.Typography.HeadingMedium.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? 0 : -20)
                            .animation(.easeOut(duration: 0.6).delay(0.1), value: pageAppeared)
                        
                        Text("language.selection.description".localized)
                            .font(.system(
                                size: DesignSystem.Typography.Body.fontSize,
                                weight: DesignSystem.Typography.Body.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? 0 : -10)
                            .animation(.easeOut(duration: 0.6).delay(0.3), value: pageAppeared)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                    
                    // 语言选项列表
                    VStack(spacing: DesignSystem.Spacing.md) {
                        ForEach(SupportedLanguage.allCases.indices, id: \.self) { index in
                            let language = SupportedLanguage.allCases[index]
                            
                            LanguageOptionView(
                                language: language,
                                isSelected: selectedLanguage == language,
                                onTapped: {
                                    selectedLanguage = language
                                }
                            )
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? 0 : 30)
                            .animation(.easeOut(duration: 0.6).delay(0.5 + Double(index) * 0.1), value: pageAppeared)
                        }
                    }
                    .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                    
                    Spacer()
                    
                    // 确认按钮
                    Button(action: {
                        handleLanguageConfirm()
                    }) {
                        ZStack {
                            RoundedRectangle(cornerRadius: 16)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(hex: "#a9d051"),
                                            Color(hex: "#8bb83f")
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(height: 50)
                                .shadow(color: Color(hex: "#a9d051").opacity(0.3), radius: 8, x: 0, y: 4)
                            
                            Text("common.button.confirm".localized)
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                        }
                    }
                    .disabled(selectedLanguage == localizationManager.currentLanguage)
                    .opacity(selectedLanguage == localizationManager.currentLanguage ? 0.5 : 1.0)
                    .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                    .padding(.bottom, DesignSystem.Spacing.lg)
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : 20)
                    .animation(.easeOut(duration: 0.6).delay(0.8), value: pageAppeared)
                }
                .padding(.top, DesignSystem.Spacing.lg)
            }
            .navigationTitle("settings.item.language".localized)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                    }
                }
            }
        }
        .onAppear {
            withAnimation {
                pageAppeared = true
            }
        }
    }
    
    // MARK: - Action Handlers
    
    /**
     * 处理语言确认选择
     */
    private func handleLanguageConfirm() {
        guard selectedLanguage != localizationManager.currentLanguage else { return }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // 切换语言
        localizationManager.changeLanguage(to: selectedLanguage)
    }
}

/**
 * 语言选项视图
 */
private struct LanguageOptionView: View {
    
    let language: SupportedLanguage
    let isSelected: Bool
    let onTapped: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                isPressed = true
                onTapped()
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isPressed = false
            }
        }) {
            ZStack {
                // 背景容器
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? Color(hex: "#f0f8e0") : Color(hex: "#f6fbe9"))
                    .frame(height: 60)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected ? Color(hex: "#a9d051") : Color.clear,
                                lineWidth: 2
                            )
                    )
                    .shadow(color: Color.black.opacity(0.04), radius: 4, x: 0, y: 2)
                
                HStack(spacing: DesignSystem.Spacing.md) {
                    // 语言标识图标
                    ZStack {
                        Circle()
                            .fill(isSelected ? Color(hex: "#a9d051") : Color(hex: "#e0e0e0"))
                            .frame(width: 24, height: 24)
                        
                        Text(language == .chinese ? "中" : "EN")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(isSelected ? .white : Color(hex: "#808080"))
                    }
                    
                    // 语言名称
                    VStack(alignment: .leading, spacing: 2) {
                        Text(language.nativeDisplayName)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Text(language.displayName)
                            .font(.system(size: 14, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    // 选中指示器
                    if isSelected {
                        Image(systemName: "checkmark")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(Color(hex: "#a9d051"))
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.md)
                
                // 按压效果
                if isPressed {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.white.opacity(0.3))
                        .frame(height: 60)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
    }
}

// MARK: - Preview
#Preview {
    LanguageSelectionView()
} 