//
//  HomeView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/6/23.
//

import SwiftUI

/**
 * 首页主视图 - 团团转应用的核心页面
 */
struct HomeView: View {
    
    @EnvironmentObject var viewModel: HomeViewModel
    @State private var pageAppeared = false
    
    // MARK: - Navigation Callback
    let onStudentSelected: (String) -> Void
    
    var body: some View {
        ZStack {
            // 美化背景渐变
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color(hex: "#fcfff4"), location: 0.0),
                    .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                    .init(color: Color.white, location: 0.7),
                    .init(color: Color(hex: "#fafffe"), location: 1.0)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)
            
            // 装饰性背景元素
            VStack {
                HStack {
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.03))
                        .frame(width: 120, height: 120)
                        .offset(x: 40, y: -20)
                }
                Spacer()
                HStack {
                    Circle()
                        .fill(Color(hex: "#FFE49E").opacity(0.04))
                        .frame(width: 80, height: 80)
                        .offset(x: -30, y: 50)
                    Spacer()
                }
            }
            
            VStack(spacing: 0) {
                // 顶部区域 - Logo和搜索框
                HeaderView(
                    searchText: $viewModel.searchText,
                    isSearching: $viewModel.isSearching
                )
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : -50)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: pageAppeared)
                
                Spacer()
                    .frame(height: 5)
                
                // 操作按钮区域
                ActionButtonsView(
                    totalScore: viewModel.currentRangeTotalScore,
                    dateRangeText: viewModel.currentDateRangeDisplayText,
                    onAddStudentTapped: {
                        handleAddStudent()
                    },
                    onClassOperationTapped: {
                        handleClassOperation()
                    },
                    onTotalScoreTapped: {
                        handleTotalScoreTapped()
                    }
                )
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 30)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: pageAppeared)
                
                Spacer()
                    .frame(height: 20)
                
                // 班级选择器
                ClassTabView(
                    classes: viewModel.classes,
                    selectedIndex: viewModel.selectedClassIndex,
                    onClassSelected: { index in
                        // 班级切换动画由selectClass方法内部管理
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                            viewModel.selectClass(at: index)
                        }
                    },
                    onSortOptionsRequested: {
                        // 显示排序选项弹窗
                        viewModel.handleSortOptions()
                    }
                )
                .zIndex(1)
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 20)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.5), value: pageAppeared)
                .padding(.bottom, DesignSystem.Spacing.sm)
                
                // 学生网格视图
                StudentGridView(
                    students: viewModel.filteredStudents,
                    isDeleteMode: viewModel.isDeleteMode,
                    hasClasses: !viewModel.classes.isEmpty,
                    onStudentTapped: { student in
                        handleStudentTapped(student)
                    },
                    onEnterDeleteMode: {
                        viewModel.enterDeleteMode()
                    },
                    onExitDeleteMode: {
                        viewModel.exitDeleteMode()
                    },
                    onDeleteRequested: { student in
                        viewModel.requestDeleteStudent(student)
                    },
                    onCreateClassTapped: {
                        handleCreateClass()
                    },
                    onRefresh: {
                        await viewModel.pullToRefreshData()
                    }
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .opacity(viewModel.isInitialLoad ? (pageAppeared ? 1.0 : 0.0) : 1.0)
                .offset(y: viewModel.isInitialLoad ? (pageAppeared ? 0 : 30) : 0)
                .animation(
                    viewModel.isInitialLoad ? 
                    Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.7) : 
                    nil, 
                    value: pageAppeared
                )
                .animation(nil, value: viewModel.isClassSwitching)
            }
        }
        .overlay(
            // 删除确认对话框
            DeleteConfirmationDialog(
                isPresented: $viewModel.showDeleteConfirmation,
                studentName: viewModel.studentToDelete?.name ?? "",
                onConfirm: {
                    viewModel.confirmDeleteStudent()
                },
                onCancel: {
                    viewModel.cancelDeleteStudent()
                }
            )
        )
        .overlay(
            // 日期范围选择器
            Group {
                if viewModel.showDateRangePicker {
                    DateRangePickerView(
                        selectedDateRange: $viewModel.selectedDateRange,
                        isPresented: $viewModel.showDateRangePicker
                    )
                    .transition(.opacity)
                }
            }
        )
        .overlay(
            // 无班级提示弹窗
            NoClassAlertView(
                isPresented: $viewModel.showNoClassAlert,
                onCreateClass: {
                    viewModel.handleCreateClassFromAlert()
                },
                onCancel: {
                    viewModel.closeNoClassAlert()
                }
            )
        )
        .overlay(
            // 添加学生选项菜单
            AddStudentOptionsView(
                isPresented: $viewModel.showAddStudentOptions,
                onManualAdd: {
                    viewModel.showManualAddStudentForm()
                },
                onExcelImport: {
                    viewModel.showExcelImportView()
                }
            )
        )
        .overlay(
            // 手动添加学生表单
            ManualAddStudentView(
                isPresented: $viewModel.showManualAddStudent,
                onSubmit: { studentForms in
                    viewModel.addStudents(studentForms)
                },
                onCancel: {
                    viewModel.closeManualAddStudent()
                }
            )
        )
        .overlay(
            // Excel导入界面
            ExcelImportView(
                isPresented: $viewModel.showExcelImport,
                onImport: { data in
                    viewModel.importStudentsFromExcel(data)
                },
                onCancel: {
                    viewModel.closeExcelImport()
                }
            )
        )
        .overlay(
            // 全班操作选项弹窗
            ClassOperationOptionsView(
                isPresented: $viewModel.showClassOperationOptions,
                onAddPoints: {
                    viewModel.showAddPointsForm()
                },
                onDeductPoints: {
                    viewModel.showDeductPointsForm()
                }
            )
        )
        .overlay(
            // 全班操作表单弹窗
            ClassOperationFormView(
                isPresented: $viewModel.showClassOperationForm,
                operationType: viewModel.classOperationType,
                onSubmit: { name, value in
                    viewModel.submitClassOperation(name: name, value: value)
                },
                onCancel: {
                    viewModel.closeClassOperationDialogs()
                }
            )
        )
        .overlay(
            // 班级排序选项弹窗
            ClassSortOptionsView(
                isPresented: $viewModel.showSortOptions,
                onSortByNumber: {
                    viewModel.sortByStudentNumber()
                },
                onSortByScore: {
                    viewModel.sortByScore()
                }
            )
        )
        // MARK: - Create Class Alert Modifiers
        .alert("create_class.permission_denied.title".localized, isPresented: $viewModel.showPermissionDeniedAlert) {
            Button("create_class.permission_denied.upgrade_button".localized) {
                viewModel.showSubscriptionView = true
            }
            Button("common.button.cancel".localized, role: .cancel) {}
        } message: {
            Text(viewModel.permissionDeniedMessage)
        }
        .alert("create_class.dialog.title".localized, isPresented: $viewModel.showCreateClassDialog, actions: {
            TextField("create_class.dialog.placeholder".localized, text: $viewModel.className)
                .textInputAutocapitalization(.words)
            Button("create_class.dialog.create_button".localized) {
                viewModel.createClass()
            }
            .disabled(viewModel.isCreatingClass)
            Button("common.button.cancel".localized, role: .cancel) {
                viewModel.resetCreateClassState()
            }
        }, message: {
            Text("create_class.dialog.message".localized)
        })
        .alert("create_class.success.message".localized, isPresented: $viewModel.showCreateClassSuccessAlert) {
            Button("common.button.confirm".localized, role: .cancel) {}
        }
        .alert("Error", isPresented: .constant(viewModel.createClassErrorMessage != nil)) {
            Button("common.button.confirm".localized, role: .cancel) {
                viewModel.createClassErrorMessage = nil
            }
        } message: {
            if let errorMessage = viewModel.createClassErrorMessage {
                Text(errorMessage)
            }
        }
        .fullScreenCover(isPresented: $viewModel.showSubscriptionView) {
            SubscriptionView()
        }
        .onTapGesture {
            // 点击空白区域关闭键盘或退出删除模式
            if viewModel.isDeleteMode {
                viewModel.exitDeleteMode()
            } else {
                hideKeyboard()
            }
        }
        .onAppear {
            // 确保初始状态正确
            print("HomeView onAppear: isInitialLoad: \(viewModel.isInitialLoad), isClassSwitching: \(viewModel.isClassSwitching)")
            
            // 重新加载班级数据，确保从其他页面返回时数据是最新的
            viewModel.reloadClassData()
            
            // 页面入场动画
            withAnimation {
                pageAppeared = true
            }
        }
        
        // 添加班级冻结选择弹窗
        .fullScreenCover(isPresented: $viewModel.showClassFreezeSelection) {
            ClassFreezeSelectionView(
                activeClasses: viewModel.activeClasses,
                allowedActiveClassCount: viewModel.allowedActiveClassCount,
                onConfirm: { selectedClassIds in
                    viewModel.handleClassFreezeSelection(selectedClassIds)
                },
                onSubscribe: {
                    viewModel.openSubscriptionPage()
                },
                subscriptionLevelName: viewModel.currentSubscriptionLevelName == "free" ? "免费版" :
                                      viewModel.currentSubscriptionLevelName == "basic" ? "初级会员" : "高级会员",
                previousSubscriptionLevelName: viewModel.previousSubscriptionLevelName == "free" ? "免费版" :
                                             viewModel.previousSubscriptionLevelName == "basic" ? "初级会员" : "高级会员"
            )
            .environmentObject(CoreDataManager.shared)
        }
        
        // 添加班级解冻选择弹窗
        .fullScreenCover(isPresented: $viewModel.showClassUnfreezeSelection) {
            ClassUnfreezeSelectionView(
                frozenClasses: viewModel.frozenClasses,
                availableUnfreezeCount: viewModel.availableUnfreezeCount,
                onConfirm: { selectedClassIds in
                    viewModel.handleClassUnfreezeSelection(selectedClassIds)
                },
                onCancel: {
                    viewModel.cancelClassUnfreeze()
                },
                subscriptionLevelName: viewModel.currentSubscriptionLevelName == "free" ? "免费版" :
                                      viewModel.currentSubscriptionLevelName == "basic" ? "初级会员" : "高级会员"
            )
            .environmentObject(CoreDataManager.shared)
        }
    }
    
    // MARK: - Action Handlers
    
    /**
     * 处理添加学生按钮点击
     */
    private func handleAddStudent() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            viewModel.handleAddStudent()
        }
    }
    
    /**
     * 处理全班操作按钮点击
     */
    private func handleClassOperation() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            viewModel.handleClassOperation()
        }
    }
    
    /**
     * 处理全班总分按钮点击
     */
    private func handleTotalScoreTapped() {
        print("显示日期范围选择器")
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            viewModel.showDateRangeSelector()
        }
    }
    
    /**
     * 处理学生卡片点击
     */
    private func handleStudentTapped(_ student: Student) {
        print("点击了学生: \(student.name ?? "未知")")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // 添加点击反馈动画
        }
        
        // 调用导航回调，传递学生ID（为CoreData+CloudKit预留接口）
        let studentId = student.id?.uuidString ?? ""
        onStudentSelected(studentId)
    }
    
    /**
     * 处理创建班级按钮点击
     */
    private func handleCreateClass() {
        print("创建班级按钮点击")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // 添加点击反馈动画
        }
        
        // 触发HomeViewModel的创建班级流程
        viewModel.handleCreateClassFromAlert()
    }
    
    /**
     * 隐藏键盘
     */
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        withAnimation(.easeInOut(duration: 0.3)) {
            viewModel.isSearching = false
        }
    }
}

// MARK: - Preview
#Preview {
    HomeView(onStudentSelected: { _ in })
        .environmentObject(HomeViewModel())
} 