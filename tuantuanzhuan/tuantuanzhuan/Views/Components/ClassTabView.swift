//
//  ClassTabView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/6/23.
//

import SwiftUI

/**
 * 班级选择器组件
 */
struct ClassTabView: View {
    
    let classes: [SchoolClass]
    let selectedIndex: Int
    let onClassSelected: (Int) -> Void
    let onSortOptionsRequested: () -> Void
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            ScrollViewReader { proxy in
                HStack(spacing: DesignSystem.Spacing.md) {
                    ForEach(Array(classes.enumerated()), id: \.element.id) { index, schoolClass in
                        ClassTabButton(
                            className: schoolClass.name ?? "未知班级",
                            isSelected: index == selectedIndex,
                            showCrown: index == selectedIndex,
                            onTapped: {
                                if index == selectedIndex {
                                    // 点击已选中的班级按钮时，显示排序选项
                                    onSortOptionsRequested()
                                } else {
                                    // 点击其他班级按钮时，切换班级
                                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                        onClassSelected(index)
                                    }
                                }
                            }
                        )
                        .id(index)
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.top, 25)
                .padding(.bottom, 10)
                .onReceive([selectedIndex].publisher.first()) { newIndex in
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        proxy.scrollTo(newIndex, anchor: .center)
                    }
                }
            }
        }
        .frame(height: 60)
        .clipped()
    }
}

/**
 * 班级标签按钮（优化版 - 皇冠在按钮外部右上角）
 */
private struct ClassTabButton: View {
    
    let className: String
    let isSelected: Bool
    let showCrown: Bool
    let onTapped: () -> Void
    
    @State private var isPressed = false
    @State private var crownAnimationTrigger = false
    
    var body: some View {
        ZStack {
            // 主按钮
            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    isPressed = true
                    crownAnimationTrigger.toggle()
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isPressed = false
                    onTapped()
                }
            }) {
                ZStack {
                    // 美化背景
                    RoundedRectangle(cornerRadius: 25)
                        .fill(
                            isSelected ? 
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "#FFE49E"),
                                    Color(hex: "#FFD700").opacity(0.8)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ) :
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.white,
                                    Color(hex: "#f8fdf0").opacity(0.5)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(
                            color: isSelected ? 
                            Color(hex: "#FFE49E").opacity(0.4) : 
                            Color.black.opacity(0.08),
                            radius: isSelected ? 8 : 4,
                            x: 0,
                            y: isSelected ? 4 : 2
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 25)
                                .stroke(
                                    isSelected ?
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(hex: "#FFE49E").opacity(0.8),
                                            Color(hex: "#FFD700").opacity(0.6)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ) :
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.gray.opacity(0.2),
                                            Color.clear
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: isSelected ? 2 : 1
                                )
                        )
                    
                    // 装饰性背景点
                    if isSelected {
                        Circle()
                            .fill(Color(hex: "#FFD700").opacity(0.2))
                            .frame(width: 20, height: 20)
                            .offset(x: 25, y: -8)
                        
                        Circle()
                            .fill(Color(hex: "#FFE49E").opacity(0.15))
                            .frame(width: 15, height: 15)
                            .offset(x: -20, y: 10)
                    }
                    
                    // 班级名称文字
                    Text(className)
                        .font(.system(size: DesignSystem.Typography.Caption.fontSize, weight: .semibold))
                        .foregroundColor(isSelected ? Color(hex: "#ac892e") : DesignSystem.Colors.textSecondary)
                        .shadow(color: isSelected ? Color.white.opacity(0.5) : Color.clear, radius: 1, x: 0, y: 1)
                        .padding(.horizontal, DesignSystem.Spacing.md)
                        .padding(.vertical, DesignSystem.Spacing.sm)
                    
                    // 按压闪烁效果
                    if isPressed {
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color.white.opacity(0.4))
                            .transition(.opacity)
                    }
                }
            }
            .buttonStyle(PlainButtonStyle())
            .frame(minWidth: 110)
            .scaleEffect(isSelected ? 1.08 : (isPressed ? 0.95 : 1.0))
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isSelected)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
            
            // 皇冠图标（位于按钮外部右上角）
            if showCrown {
                ZStack {
                    // 皇冠背景光环
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "#FFD700").opacity(0.3),
                                    Color.clear
                                ]),
                                center: .center,
                                startRadius: 0,
                                endRadius: 15
                            )
                        )
                        .frame(width: 30, height: 30)
                        .scaleEffect(crownAnimationTrigger ? 1.2 : 1.0)
                        .opacity(crownAnimationTrigger ? 0.7 : 0.4)
                        .animation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true), value: crownAnimationTrigger)
                    
                    // 皇冠图标
                    Image("皇冠")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 30, height: 30)
                        .shadow(color: Color(hex: "#FFD700").opacity(0.4), radius: 3, x: 0, y: 1)
                        .scaleEffect(crownAnimationTrigger ? 1.1 : 1.0)
                        .rotationEffect(.degrees(crownAnimationTrigger ? 5 : -5))
                        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: crownAnimationTrigger)
                }
                .offset(x: 40, y: -32)
                .zIndex(1)
            }
        }
        .onAppear {
            if isSelected {
                // 选中状态的皇冠动画
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    crownAnimationTrigger = true
                }
            }
        }
        .onReceive([isSelected].publisher.first()) { selected in
            if selected {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8).delay(0.2)) {
                    crownAnimationTrigger = true
                }
            } else {
                crownAnimationTrigger = false
            }
        }
    }
}

// MARK: - Preview
#Preview {
    return VStack {
        Text("班级选择器预览")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.gray)
        Spacer()
    }
    .background(
        LinearGradient(
            gradient: Gradient(colors: [DesignSystem.Colors.background, Color.white]),
            startPoint: .top,
            endPoint: .bottom
        )
    )
} 