//
//  DeleteAccountConfirmationView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025-01-20.
//

import SwiftUI

/**
 * 删除账号确认弹窗组件
 * 
 * 提供最终确认界面，要求用户输入确认文本
 * 这是删除账号流程的最后一道安全检查
 */
struct DeleteAccountConfirmationView: View {
    
    // MARK: - Properties
    @Binding var confirmationText: String
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    // MARK: - Environment
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authManager: AuthenticationManager
    
    // MARK: - State
    @State private var isConfirmButtonEnabled = false
    @State private var showDataStats = false
    @State private var userDataStats: [String: Int] = [:]
    
    // MARK: - Constants
    private let requiredConfirmationText = "account_deletion.second_confirmation.confirm_text".localized
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 标题区域
                headerSection
                
                // 数据统计区域
                if showDataStats {
                    dataStatsSection
                }
                
                // 确认输入区域
                confirmationInputSection
                
                // 多设备提醒
                multiDeviceNoticeSection
                
                Spacer()
                
                // 操作按钮
                actionButtonsSection
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .navigationTitle("account_deletion.second_confirmation.title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("account_deletion.cancel_button".localized) {
                        onCancel()
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
        .onAppear {
            loadUserDataStats()
        }
        .onChange(of: confirmationText) { newValue in
            isConfirmButtonEnabled = newValue.lowercased() == requiredConfirmationText.lowercased()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // 警告图标
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 60))
                .foregroundColor(.red)
            
            // 标题
            Text("account_deletion.second_confirmation.title".localized)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
            
            // 说明文字
            Text("account_deletion.second_confirmation.message".localized)
                .font(.system(size: 16))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Data Stats Section
    private var dataStatsSection: some View {
        VStack(spacing: 12) {
            // 标题
            HStack {
                Text("即将删除的数据")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showDataStats.toggle()
                    }
                }) {
                    Image(systemName: showDataStats ? "chevron.up" : "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.blue)
                }
            }
            
            // 数据统计卡片
            if showDataStats {
                VStack(spacing: 8) {
                    ForEach(Array(userDataStats.keys.sorted()), id: \.self) { key in
                        HStack {
                            Text(localizedStatsKey(key))
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text("\(userDataStats[key] ?? 0)")
                                .font(.system(size: 14, weight: .bold))
                                .foregroundColor(.primary)
                        }
                        .padding(.vertical, 4)
                    }
                }
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.red.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.red.opacity(0.3), lineWidth: 1)
                        )
                )
                .transition(.opacity.combined(with: .slide))
            }
        }
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.3)) {
                showDataStats.toggle()
            }
        }
    }
    
    // MARK: - Confirmation Input Section
    private var confirmationInputSection: some View {
        VStack(spacing: 16) {
            // 说明文字
            Text("为了防止误删，请在下方输入确认文本：")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
            
            // 确认文本显示
            Text("\"\(requiredConfirmationText)\"")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(.red)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                )
            
            // 输入框
            TextField("account_deletion.second_confirmation.placeholder".localized, text: $confirmationText)
                .font(.system(size: 16))
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .autocapitalization(.none)
                .disableAutocorrection(true)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(
                            isConfirmButtonEnabled ? Color.green : Color.gray.opacity(0.5),
                            lineWidth: 2
                        )
                )
        }
        .padding(.horizontal, 8)
    }
    
    // MARK: - Multi-device Notice Section
    private var multiDeviceNoticeSection: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "iphone.and.ipad")
                    .font(.system(size: 16))
                    .foregroundColor(.orange)
                
                Text("account_deletion.multi_device.title".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.orange)
                
                Spacer()
            }
            
            Text("account_deletion.multi_device.message".localized)
                .font(.system(size: 14))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.orange.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            // 确认删除按钮
            Button(action: {
                onConfirm()
                presentationMode.wrappedValue.dismiss()
            }) {
                HStack {
                    Image(systemName: "trash.fill")
                    Text("account_deletion.delete_input_button".localized)
                }
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 48)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isConfirmButtonEnabled ? Color.red : Color.gray)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
            }
            .disabled(!isConfirmButtonEnabled)
            .buttonStyle(PlainButtonStyle())
            
            // 取消按钮
            Button(action: {
                onCancel()
                presentationMode.wrappedValue.dismiss()
            }) {
                Text("account_deletion.cancel_button".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .frame(height: 48)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.blue, lineWidth: 2)
                    )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - Private Methods
    
    /**
     * 加载用户数据统计
     */
    private func loadUserDataStats() {
        guard let currentUser = authManager.currentUser else { return }
        
        var stats: [String: Int] = [:]
        
        // 统计班级数量
        stats["classes"] = currentUser.classes?.count ?? 0
        
        // 统计学生数量
        var totalStudents = 0
        if let classes = currentUser.classes as? Set<SchoolClass> {
            for schoolClass in classes {
                totalStudents += schoolClass.students?.count ?? 0
            }
        }
        stats["students"] = totalStudents
        
        // 统计记录数量
        var totalRecords = 0
        if let classes = currentUser.classes as? Set<SchoolClass> {
            for schoolClass in classes {
                if let students = schoolClass.students as? Set<Student> {
                    for student in students {
                        totalRecords += student.pointRecords?.count ?? 0
                        totalRecords += student.redemptionRecords?.count ?? 0
                        totalRecords += student.lotteryRecords?.count ?? 0
                    }
                }
            }
        }
        stats["records"] = totalRecords
        
        self.userDataStats = stats
        
        // 如果有数据，默认显示统计信息
        if totalStudents > 0 || totalRecords > 0 {
            showDataStats = true
        }
    }
    
    /**
     * 获取本地化的统计信息键名
     */
    private func localizedStatsKey(_ key: String) -> String {
        switch key {
        case "classes":
            return "account_deletion.data_stats.classes".localized
        case "students":
            return "account_deletion.data_stats.students".localized
        case "records":
            return "account_deletion.data_stats.records".localized
        default:
            return key
        }
    }
}

// MARK: - Preview
#Preview {
    DeleteAccountConfirmationView(
        confirmationText: .constant(""),
        onConfirm: {
            print("确认删除")
        },
        onCancel: {
            print("取消删除")
        }
    )
    .environmentObject(AuthenticationManager())
} 