//
//  ManualAddStudentView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 手动添加学生表单组件
 * 支持单个和批量学生信息输入，可动态添加表单行
 */
struct ManualAddStudentView: View {
    
    @Binding var isPresented: Bool
    @State private var studentForms: [StudentFormData] = [StudentFormData()]
    @State private var isSubmitting: Bool = false
    @State private var validationErrors: [String] = []
    @State private var animationTrigger = false
    
    let onSubmit: ([StudentFormData]) -> Void
    let onCancel: () -> Void
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    // 注释掉点击外部区域关闭弹窗的手势，防止误操作
                    // .onTapGesture {
                    //     if !isSubmitting {
                    //         onCancel()
                    //     }
                    // }
                    .transition(.opacity)
                
                // 表单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Text("add_student.form.title".localized)
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            Spacer()
                            
                            // 添加学生按钮
                            Button(action: addNewStudentForm) {
                                Image(systemName: "plus.circle.fill")
                                    .font(.system(size: 26))
                                    .foregroundColor(Color(hex: "#a9d051"))
                            }
                            .disabled(isSubmitting)
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 24)
                        .padding(.bottom, 16)
                        .onTapGesture {
                            // 点击标题栏区域关闭键盘
                            dismissKeyboard()
                        }
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                            .padding(.horizontal, 24)
                        
                        // 表单内容区域
                        ScrollView(.vertical, showsIndicators: true) {
                            VStack(spacing: 16) {
                                ForEach(studentForms.indices, id: \.self) { index in
                                    StudentFormRow(
                                        student: $studentForms[index],
                                        index: index,
                                        canDelete: studentForms.count > 1,
                                        onDelete: {
                                            removeStudentForm(at: index)
                                        }
                                    )
                                    .disabled(isSubmitting)
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 20)
                        }
                        .frame(maxHeight: geometry.size.height * 0.5)
                        .contentShape(Rectangle()) // 确保整个滚动区域都能响应手势
                        .onTapGesture {
                            // 点击表单内容区域关闭键盘
                            dismissKeyboard()
                        }
                        
                        // 错误提示区域
                        if !validationErrors.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                ForEach(validationErrors, id: \.self) { error in
                                    Text(error)
                                        .font(.system(size: 13, weight: .regular))
                                        .foregroundColor(.red)
                                        .multilineTextAlignment(.leading)
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(Color.red.opacity(0.05))
                            .onTapGesture {
                                // 点击错误提示区域关闭键盘
                                dismissKeyboard()
                            }
                        }
                        
                        // 底部按钮区域
                        HStack(spacing: 16) {
                            // 取消按钮
                            Button(action: {
                                if !isSubmitting {
                                    dismissKeyboard()
                                    onCancel()
                                }
                            }) {
                                Text("common.button.cancel".localized)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(Color.gray)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 44)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(12)
                            }
                            .disabled(isSubmitting)
                            
                            // 添加学生按钮
                            Button(action: {
                                dismissKeyboard()
                                submitStudents()
                            }) {
                                HStack(spacing: 8) {
                                    if isSubmitting {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    }
                                    
                                    Text(isSubmitting ? "add_student.form.submitting".localized : "add_student.form.submit".localized)
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.white)
                                }
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(hex: "#a9d051"),
                                            Color(hex: "#74c07f")
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .cornerRadius(12)
                                .opacity(isSubmitting ? 0.8 : 1.0)
                            }
                            .disabled(isSubmitting || studentForms.isEmpty)
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 20)
                        .padding(.bottom, 28)
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 400))
                    .frame(maxHeight: geometry.size.height * 0.8)
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                let _ = withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
                // 重置状态
                resetForm()
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 添加新的学生表单行
     */
    private func addNewStudentForm() {
        let _ = withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            studentForms.append(StudentFormData())
        }
    }
    
    /**
     * 移除指定索引的学生表单行
     */
    private func removeStudentForm(at index: Int) {
        if studentForms.count > 1 {
            let _ = withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                studentForms.remove(at: index)
            }
        }
    }
    
    /**
     * 提交学生数据
     */
    private func submitStudents() {
        // 清除之前的错误
        validationErrors.clear()
        
        // 批量验证
        let validationResult = studentForms.validateBatch()
        
        if validationResult.isValid {
            isSubmitting = true
            
            // 延迟提交，模拟网络请求
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                onSubmit(studentForms)
                isSubmitting = false
            }
        } else {
            // 显示验证错误
            let _ = withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                validationErrors = validationResult.errorMessages
            }
        }
    }
    
    /**
     * 重置表单
     */
    private func resetForm() {
        studentForms = [StudentFormData()]
        validationErrors.clear()
        isSubmitting = false
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

/**
 * 学生表单行组件
 */
struct StudentFormRow: View {
    
    @Binding var student: StudentFormData
    let index: Int
    let canDelete: Bool
    let onDelete: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            // 行标题和删除按钮
            HStack {
                Text("add_student.form.student_number_label".localized(with: "\(index + 1)"))
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                if canDelete {
                    Button(action: onDelete) {
                        Image(systemName: "minus.circle.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.red.opacity(0.7))
                    }
                }
            }
            
            // 表单字段
            VStack(spacing: 12) {
                // 姓名输入
                StudentFormField(
                    title: "add_student.form.name".localized,
                    text: $student.name,
                    placeholder: "add_student.form.name_placeholder".localized
                )
                
                // 学号输入
                StudentFormField(
                    title: "add_student.form.student_number".localized,
                    text: $student.studentNumber,
                    placeholder: "add_student.form.number_placeholder".localized
                )
                
                // 性别选择和初始积分
                HStack(spacing: 12) {
                    // 性别选择
                    VStack(alignment: .leading, spacing: 6) {
                        Text("add_student.form.gender".localized)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        SegmentButton(
                            selection: $student.gender,
                            options: ["male", "female"],
                            labels: ["male": "add_student.form.gender_male".localized, "female": "add_student.form.gender_female".localized],
                            backgroundColor: Color(hex: "#f8fff2"),
                            selectedColor: Color(hex: "#a9d051"),
                            textColor: DesignSystem.Colors.textPrimary,
                            fontSize: 14
                        )
                    }
                    .frame(maxWidth: .infinity)
                    
                    // 初始积分输入
                    VStack(alignment: .leading, spacing: 6) {
                        Text("add_student.form.initial_points".localized)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        TextField("0", text: $student.initialPoints)
                            .keyboardType(.numberPad)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                    .frame(width: 80)
                }
            }
        }
        .padding(16)
        .background(Color(hex: "#f8ffe5").opacity(0.5))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
        )
    }
}

/**
 * 学生表单字段组件
 */
struct StudentFormField: View {
    
    let title: String
    @Binding var text: String
    let placeholder: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            TextField(placeholder, text: $text)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
    }
}

// MARK: - Array Extension
extension Array where Element == String {
    mutating func clear() {
        self.removeAll()
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        ManualAddStudentView(
            isPresented: .constant(true),
            onSubmit: { students in
                print("提交学生数据: \(students)")
            },
            onCancel: {
                print("取消添加")
            }
        )
    }
} 