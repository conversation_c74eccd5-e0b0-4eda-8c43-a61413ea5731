//
//  LiquidTabBarShape.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 液态导航栏Shape - 绘制包含融球效果的导航栏轮廓
 * 支持动态波浪边框和上拱圆角效果
 */
struct LiquidTabBarShape: Shape {
    
    // MARK: - Properties
    
    /// 融球中心X坐标
    var bubbleCenter: CGFloat
    
    /// 波浪幅度（0.0-1.0）
    var waveAmplitude: CGFloat = 0.5
    
    /// 过渡进度（0.0-1.0）
    var transitionProgress: CGFloat = 1.0
    
    // MARK: - Constants
    
    /// 融球半径
    private let bubbleRadius: CGFloat = 20.0
    
    /// 上拱高度（延伸到导航栏上方）
    private let archHeight: CGFloat = 20.0
    
    /// 过渡宽度（融球两侧）
    private let transitionWidth: CGFloat = 40.0
    
    /// 边框宽度
    private let borderWidth: CGFloat = 5.0
    
    // MARK: - Animatable
    
    var animatableData: AnimatablePair<CGFloat, AnimatablePair<CGFloat, CGFloat>> {
        get {
            AnimatablePair(bubbleCenter, AnimatablePair(waveAmplitude, transitionProgress))
        }
        set {
            bubbleCenter = newValue.first
            waveAmplitude = newValue.second.first
            transitionProgress = newValue.second.second
        }
    }
    
    // MARK: - Shape Protocol
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        // 计算关键位置
        let bubbleLeft = bubbleCenter - bubbleRadius
        let bubbleRight = bubbleCenter + bubbleRadius
        let transitionLeft = bubbleLeft - transitionWidth / 2
        let transitionRight = bubbleRight + transitionWidth / 2
        
        // 确保边界在有效范围内
        let safeTransitionLeft = max(0, transitionLeft)
        let safeTransitionRight = min(rect.width, transitionRight)
        
        // 开始绘制路径 - 从顶部开始，包含上拱
        path.move(to: CGPoint(x: 0, y: 0))
        
        // 左侧顶边直线段
        if safeTransitionLeft > 0 {
            path.addLine(to: CGPoint(x: safeTransitionLeft, y: 0))
        }
        
        // 左侧过渡曲线 - 向上拱起
        if safeTransitionLeft < bubbleLeft {
            let leftCurveStart = CGPoint(x: safeTransitionLeft, y: 0)
            let leftCurveEnd = CGPoint(x: bubbleLeft, y: -archHeight * transitionProgress)
            
            // 计算控制点
            let leftControl1 = CGPoint(
                x: safeTransitionLeft + (bubbleLeft - safeTransitionLeft) * 0.3,
                y: -archHeight * transitionProgress * 0.1
            )
            let leftControl2 = CGPoint(
                x: bubbleLeft - (bubbleLeft - safeTransitionLeft) * 0.3,
                y: -archHeight * transitionProgress * 0.9
            )
            
            path.addCurve(
                to: leftCurveEnd,
                control1: leftControl1,
                control2: leftControl2
            )
        }
        
        // 融球顶部弧线 - 向上拱起的最高点
        let bubbleTop = CGPoint(x: bubbleCenter, y: -archHeight * transitionProgress)
        let bubbleEndPoint = CGPoint(x: bubbleRight, y: -archHeight * transitionProgress)
        
        // 使用二次贝塞尔曲线绘制融球顶部拱形
        path.addQuadCurve(
            to: bubbleEndPoint,
            control: CGPoint(x: bubbleCenter, y: -archHeight * transitionProgress - bubbleRadius * 0.3)
        )
        
        // 右侧过渡曲线 - 从拱形回到顶边
        if bubbleRight < safeTransitionRight {
            let rightCurveStart = CGPoint(x: bubbleRight, y: -archHeight * transitionProgress)
            let rightCurveEnd = CGPoint(x: safeTransitionRight, y: 0)
            
            // 计算控制点
            let rightControl1 = CGPoint(
                x: bubbleRight + (safeTransitionRight - bubbleRight) * 0.3,
                y: -archHeight * transitionProgress * 0.9
            )
            let rightControl2 = CGPoint(
                x: safeTransitionRight - (safeTransitionRight - bubbleRight) * 0.3,
                y: -archHeight * transitionProgress * 0.1
            )
            
            path.addCurve(
                to: rightCurveEnd,
                control1: rightControl1,
                control2: rightControl2
            )
        }
        
        // 右侧顶边直线段
        if safeTransitionRight < rect.width {
            path.addLine(to: CGPoint(x: rect.width, y: 0))
        }
        
        // 右边界 - 向下到底部
        path.addLine(to: CGPoint(x: rect.width, y: rect.height))
        
        // 底边 - 从右到左
        path.addLine(to: CGPoint(x: 0, y: rect.height))
        
        // 左边界 - 向上回到起点
        path.addLine(to: CGPoint(x: 0, y: 0))
        path.closeSubpath()
        
        return path
    }
    
    // MARK: - Private Methods
    // 波浪效果现在通过上拱曲线实现，不需要单独的波浪方法
}

// MARK: - Preview
#Preview {
    VStack {
        Spacer()
        
        ZStack {
            LiquidTabBarShape(bubbleCenter: 100)
                .fill(Color.white)
            
            LiquidTabBarShape(bubbleCenter: 100)
                .stroke(Color(hex: "#a9d051"), lineWidth: 5)
        }
        .frame(height: 72)
        .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: -4)
    }
    .background(Color.gray.opacity(0.2))
} 