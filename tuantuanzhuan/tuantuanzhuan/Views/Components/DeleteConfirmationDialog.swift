//
//  DeleteConfirmationDialog.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 删除确认对话框组件
 * 与首页UI风格保持一致的自定义模态对话框
 */
struct DeleteConfirmationDialog: View {
    
    @Binding var isPresented: Bool
    let studentName: String
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        onCancel()
                    }
                    .transition(.opacity)
                
                // 确认对话框卡片
                VStack(spacing: 20) {
                    // 警告图标
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.orange)
                        .padding(.top, 10)
                    
                    // 标题
                    Text("student.delete.title".localized)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    // 内容
                    Text("student.delete.message".localized(with: studentName))
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                    
                    // 按钮组
                    HStack(spacing: 15) {
                        // 取消按钮
                        Button(action: onCancel) {
                            Text("common.button.cancel".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.ConfirmationDialog.cancelButtonColor)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(Color.white)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(DesignSystem.ConfirmationDialog.cancelButtonColor, lineWidth: 1.5)
                                )
                                .cornerRadius(12)
                        }
                        
                        // 确定删除按钮
                        Button(action: onConfirm) {
                            Text("student.delete.confirm".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(DesignSystem.ConfirmationDialog.deleteButtonColor)
                                .cornerRadius(12)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 10)
                }
                .frame(maxWidth: DesignSystem.ConfirmationDialog.maxWidth)
                .background(DesignSystem.ConfirmationDialog.backgroundColor)
                .cornerRadius(DesignSystem.ConfirmationDialog.cornerRadius)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.ConfirmationDialog.cornerRadius)
                        .stroke(DesignSystem.ConfirmationDialog.borderColor, lineWidth: 2)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
                .scaleEffect(isPresented ? 1.0 : 0.9)
                .opacity(isPresented ? 1.0 : 0.0)
                .animation(.spring(response: 0.5, dampingFraction: 0.8), value: isPresented)
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        DeleteConfirmationDialog(
            isPresented: .constant(true),
            studentName: "张小明",
            onConfirm: {
                print("确认删除")
            },
            onCancel: {
                print("取消删除")
            }
        )
    }
} 