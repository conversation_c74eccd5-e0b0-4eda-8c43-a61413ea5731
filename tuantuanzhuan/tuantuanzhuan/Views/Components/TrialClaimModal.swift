//
//  TrialClaimModal.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/20.
//

import SwiftUI

/**
 * 试用领取弹窗
 * 信纸样式的弹窗，包含感谢文案和领取按钮
 */
struct TrialClaimModal: View {
    
    // MARK: - Properties
    @Binding var isPresented: Bool
    let onClaim: () -> Void
    
    // MARK: - State
    @State private var modalAppeared = false
    @State private var contentAppeared = false
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // 背景渐变 - 与关于弹窗一致
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#f8fdf0"),
                    Color(hex: "#f0f8e0")
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            .onTapGesture {
                dismissModal()
            }
            
            // 信纸样式的弹窗
            VStack(spacing: 0) {
                // 信纸内容
                letterContent
                    .background(letterBackground)
                    .cornerRadius(16)
                    .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                    .scaleEffect(modalAppeared ? 1.0 : 0.8)
                    .opacity(modalAppeared ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8), value: modalAppeared)
            }
            .padding(.horizontal, 40)
        }
        .onAppear {
            withAnimation {
                modalAppeared = true
            }
            
            // 延迟显示内容动画
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.easeOut(duration: 0.8)) {
                    contentAppeared = true
                }
            }
        }
    }
    
    // MARK: - Letter Content
    private var letterContent: some View {
        VStack(spacing: 24) {
            // 顶部装饰
            topDecoration
            
            // 主要内容
            mainContent
            
            // 底部按钮
            bottomButton
        }
        .padding(.horizontal, 32)
        .padding(.vertical, 40)
    }
    
    // MARK: - Top Decoration
    private var topDecoration: some View {
        VStack(spacing: 12) {
            // 信封图标
            Image(systemName: "envelope.open.fill")
                .font(.system(size: 32, weight: .light))
                .foregroundColor(DesignSystem.Colors.primary)
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(y: contentAppeared ? 0 : -20)
                .animation(.easeOut(duration: 0.8).delay(0.2), value: contentAppeared)
            
            // 标题
            Text("trial.modal.title".localized)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(y: contentAppeared ? 0 : -15)
                .animation(.easeOut(duration: 0.8).delay(0.3), value: contentAppeared)
        }
    }
    
    // MARK: - Main Content
    private var mainContent: some View {
        VStack(spacing: 20) {
            // 感谢文案
            Text("trial.modal.message".localized)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .lineSpacing(4)
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(y: contentAppeared ? 0 : -10)
                .animation(.easeOut(duration: 0.8).delay(0.4), value: contentAppeared)
            
            // 福利说明
            benefitDescription
        }
    }
    
    // MARK: - Benefit Description
    private var benefitDescription: some View {
        VStack(spacing: 12) {
            Text("trial.modal.benefits_title".localized)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(alignment: .leading, spacing: 8) {
                benefitItem(icon: "checkmark.circle.fill", text: "trial.modal.benefit_1".localized)
                benefitItem(icon: "checkmark.circle.fill", text: "trial.modal.benefit_2".localized)
                benefitItem(icon: "checkmark.circle.fill", text: "trial.modal.benefit_3".localized)
                benefitItem(icon: "checkmark.circle.fill", text: "trial.modal.benefit_4".localized)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(DesignSystem.Colors.primary.opacity(0.05))
        )
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : -5)
        .animation(.easeOut(duration: 0.8).delay(0.5), value: contentAppeared)
    }
    
    // MARK: - Benefit Item
    private func benefitItem(icon: String, text: String) -> some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.primary)
            
            Text(text)
                .font(.system(size: 13, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Spacer()
        }
    }
    
    // MARK: - Bottom Button
    private var bottomButton: some View {
        // 主要按钮
        Button(action: {
            onClaim()
            dismissModal()
        }) {
            HStack {
                Image(systemName: "gift.fill")
                    .font(.system(size: 16, weight: .medium))

                Text("trial.modal.claim_button".localized)
                    .font(.system(size: 16, weight: .semibold))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        DesignSystem.Colors.primary,
                        DesignSystem.Colors.primary.opacity(0.8)
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(25)
            .shadow(color: DesignSystem.Colors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .buttonStyle(PlainButtonStyle())
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : 10)
        .animation(.easeOut(duration: 0.8).delay(0.6), value: contentAppeared)
    }
    
    // MARK: - Letter Background
    private var letterBackground: some View {
        ZStack {
            // 主背景
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
            
            // 信纸纹理
            VStack(spacing: 0) {
                ForEach(0..<15, id: \.self) { _ in
                    Rectangle()
                        .fill(Color.gray.opacity(0.1))
                        .frame(height: 1)
                    
                    Spacer()
                        .frame(height: 24)
                }
            }
            .padding(.top, 80)
            .clipped()
            
            // 边框
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        }
    }
    
    // MARK: - Helper Methods
    private func dismissModal() {
        withAnimation(.easeInOut(duration: 0.3)) {
            modalAppeared = false
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            isPresented = false
        }
    }
}

/**
 * 试用期订阅提醒弹窗
 * 当用户在试用期内点击查看订阅方案时显示的温馨提醒
 */
struct TrialSubscriptionReminderModal: View {

    // MARK: - Properties
    @Binding var isPresented: Bool
    let onConfirm: () -> Void

    // MARK: - State
    @State private var modalAppeared = false
    @State private var contentAppeared = false

    // MARK: - Body
    var body: some View {
        ZStack {
            // 背景渐变 - 与其他弹窗一致
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#f8fdf0"),
                    Color(hex: "#f0f8e0")
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            .onTapGesture {
                dismissModal()
            }

            // 弹窗内容
            VStack(spacing: 0) {
                modalContent
                    .background(modalBackground)
                    .cornerRadius(16)
                    .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                    .scaleEffect(modalAppeared ? 1.0 : 0.8)
                    .opacity(modalAppeared ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8), value: modalAppeared)
            }
            .padding(.horizontal, 40)
        }
        .onAppear {
            withAnimation {
                modalAppeared = true
            }

            withAnimation(.easeOut(duration: 0.8).delay(0.2)) {
                contentAppeared = true
            }
        }
    }

    // MARK: - Modal Content
    private var modalContent: some View {
        VStack(spacing: 24) {
            // 顶部装饰
            topDecoration

            // 提醒内容
            reminderContent

            // 确认按钮
            confirmButton
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 32)
    }

    // MARK: - Modal Background
    private var modalBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(Color.white)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(DesignSystem.Colors.primary.opacity(0.2), lineWidth: 1)
            )
    }

    // MARK: - Top Decoration
    private var topDecoration: some View {
        VStack(spacing: 12) {
            // 提醒图标
            Image(systemName: "lightbulb.fill")
                .font(.system(size: 32, weight: .light))
                .foregroundColor(Color.orange)
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(y: contentAppeared ? 0 : -20)
                .animation(.easeOut(duration: 0.8).delay(0.2), value: contentAppeared)

            // 标题
            Text("trial.reminder.title".localized)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(y: contentAppeared ? 0 : -15)
                .animation(.easeOut(duration: 0.8).delay(0.3), value: contentAppeared)
        }
    }

    // MARK: - Reminder Content
    private var reminderContent: some View {
        VStack(spacing: 16) {
            // 提醒文案
            Text("trial.reminder.message".localized)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .lineSpacing(4)
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(y: contentAppeared ? 0 : 20)
                .animation(.easeOut(duration: 0.8).delay(0.4), value: contentAppeared)
        }
    }

    // MARK: - Confirm Button
    private var confirmButton: some View {
        Button(action: {
            onConfirm()
            dismissModal()
        }) {
            HStack(spacing: 8) {
                Text("trial.reminder.confirm".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 48)
            .background(DesignSystem.Colors.primary)
            .cornerRadius(12)
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.5), value: contentAppeared)
    }

    // MARK: - Helper Methods
    private func dismissModal() {
        withAnimation(.easeInOut(duration: 0.3)) {
            modalAppeared = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            isPresented = false
        }
    }
}

// MARK: - Preview
#Preview {
    TrialClaimModal(isPresented: .constant(true)) {
        print("试用领取按钮被点击")
    }
    .background(DesignSystem.Colors.background)
}

#Preview("Trial Reminder") {
    TrialSubscriptionReminderModal(isPresented: .constant(true)) {
        print("谢谢提醒按钮被点击")
    }
    .background(DesignSystem.Colors.background)
}
