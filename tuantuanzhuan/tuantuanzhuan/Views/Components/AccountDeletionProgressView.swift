//
//  AccountDeletionProgressView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025-01-20.
//

import SwiftUI

/**
 * 账号删除进度显示组件
 * 
 * 显示删除账号的实时进度，包括：
 * - 删除步骤进度条
 * - 当前步骤说明
 * - 错误信息显示
 * - 取消操作按钮
 */
struct AccountDeletionProgressView: View {
    
    // MARK: - Properties
    @ObservedObject var deletionManager: AccountDeletionManager
    @Environment(\.presentationMode) var presentationMode
    let onCancel: () -> Void
    let onComplete: () -> Void
    let onError: (Error) -> Void
    
    // MARK: - State
    @State private var showCancelConfirmation = false
    @State private var isAnimating = false
    
    var body: some View {
        ZStack {
            // 背景
            Color.black.opacity(0.4)
                .ignoresSafeArea()
            
            // 主要内容
            VStack(spacing: 24) {
                // 标题
                headerSection
                
                // 进度指示器
                progressSection
                
                // 步骤说明
                statusSection
                
                // 操作按钮
                actionButtonsSection
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 32)
        }
        .onAppear {
            isAnimating = true
        }
        .onChange(of: deletionManager.isDeletingAccount) { isDeletingAccount in
            if !isDeletingAccount {
                if let error = deletionManager.deletionError {
                    onError(error)
                } else if deletionManager.deletionProgress >= 1.0 {
                    onComplete()
                }
            }
        }
        .alert("account_deletion.cancel_button".localized, isPresented: $showCancelConfirmation) {
            Button("account_deletion.cancel_button".localized, role: .cancel) {
                // 取消操作
            }
            Button("确认取消", role: .destructive) {
                deletionManager.cancelDeletion()
                onCancel()
            }
        } message: {
            Text("确定要取消删除操作吗？")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 8) {
            // 删除图标
            Image(systemName: "trash.circle.fill")
                .font(.system(size: 48))
                .foregroundColor(.red)
                .rotationEffect(.degrees(isAnimating ? 360 : 0))
                .animation(
                    .linear(duration: 2.0)
                    .repeatForever(autoreverses: false), 
                    value: isAnimating
                )
            
            Text("account_deletion.title".localized)
                .font(.system(size: 22, weight: .bold))
                .foregroundColor(.primary)
            
            Text("account_deletion.deleting_progress".localized)
                .font(.system(size: 16))
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Progress Section
    private var progressSection: some View {
        VStack(spacing: 16) {
            // 进度条
            ProgressView(value: deletionManager.deletionProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: .red))
                .frame(height: 8)
                .scaleEffect(x: 1, y: 2, anchor: .center)
            
            // 进度百分比
            HStack {
                Text("进度")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(Int(deletionManager.deletionProgress * 100))%")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.primary)
            }
        }
        .padding(.horizontal, 8)
    }
    
    // MARK: - Status Section
    private var statusSection: some View {
        VStack(spacing: 12) {
            // 当前步骤
            if !deletionManager.deletionStatus.isEmpty {
                HStack {
                    // 加载动画
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                        .scaleEffect(0.8)
                    
                    Text(deletionManager.deletionStatus)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                }
                .padding(.horizontal, 8)
            }
            
            // 错误信息
            if let error = deletionManager.deletionError {
                errorMessageView(error: error)
            }
        }
    }
    
    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        HStack(spacing: 16) {
            // 取消按钮（仅在删除进行中显示）
            if deletionManager.isDeletingAccount {
                Button(action: {
                    showCancelConfirmation = true
                }) {
                    HStack {
                        Image(systemName: "xmark.circle")
                        Text("account_deletion.cancel_button".localized)
                    }
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(Color.gray)
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // 完成按钮（仅在删除完成时显示）
            if !deletionManager.isDeletingAccount && deletionManager.deletionProgress >= 1.0 {
                Button(action: {
                    onComplete()
                }) {
                    HStack {
                        Image(systemName: "checkmark.circle")
                        Text("完成")
                    }
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(Color.green)
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - Error Message View
    private func errorMessageView(error: Error) -> some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.red)
                
                Text("删除失败")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.red)
                
                Spacer()
            }
            
            Text("account_deletion.error.unknown".localized)
                .font(.system(size: 14))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.red.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.red.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Preview
#Preview {
    AccountDeletionProgressView(
        deletionManager: AccountDeletionManager.shared,
        onCancel: {
            print("取消删除")
        },
        onComplete: {
            print("删除完成")
        },
        onError: { error in
            print("删除错误: \(error)")
        }
    )
}