//
//  LotteryToolConfigFormView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/16.
//

import SwiftUI

/**
 * 抽奖道具配置表单视图
 * 用于配置抽奖道具的详细参数
 */
struct LotteryToolConfigFormView: View {

    // MARK: - Environment

    @EnvironmentObject private var coreDataManager: CoreDataManager

    // MARK: - Properties

    /// 选择的班级
    let selectedClass: SchoolClass

    /// 道具类型
    let toolType: LotteryToolConfig.ToolType

    /// 现有配置（用于编辑）
    let existingConfig: LotteryToolConfig?

    /// 完成配置的回调
    let onConfigurationComplete: () -> Void

    /// 关闭弹窗的回调
    let onDismiss: () -> Void
    
    // MARK: - State

    @StateObject private var formData = LotteryToolFormData()
    @State private var showErrorAlert = false
    @State private var showSuccessAlert = false
    @State private var alertMessage = ""
    @State private var isEditing = false
    @State private var showPrizeSelection = false
    @State private var selectedItemIndex: Int?
    @State private var animationTrigger = false
    
    // MARK: - Computed Properties
    
    /**
     * 页面标题
     */
    private var navigationTitle: String {
        return "\(toolType.displayName)配置"
    }
    
    /**
     * 是否为编辑模式
     */
    private var isEditMode: Bool {
        return existingConfig != nil
    }
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩 - 与奖品库配置一致
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    // 点击背景不关闭弹窗，防止误操作
                }

            // 表单对话框 - 使用与奖品库配置相同的布局
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 标题栏
                    headerSection

                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 24)

                    // 表单内容区域
                    formContentSection

                    // 底部按钮区域
                    bottomButtonSection
                }
                .frame(maxWidth: min(geometry.size.width - 40, 420))
                .frame(maxHeight: geometry.size.height * 0.8)
                .background(Color.white)
                .cornerRadius(20)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color(hex: "#74c07f").opacity(0.2), lineWidth: 1.5)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                .scaleEffect(animationTrigger ? 1.0 : 0.9)
                .opacity(animationTrigger ? 1.0 : 0.0)
                .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
            }
        }
        .onAppear {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                animationTrigger = true
            }
            setupFormData()
        }
        .alert("lottery_tool_config.validation.error_title".localized, isPresented: $showErrorAlert) {
            Button("common.confirm".localized, role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .alert(isEditMode ? "lottery_tool_config.result.update_success_title".localized : "lottery_tool_config.result.save_success_title".localized, isPresented: $showSuccessAlert) {
            Button("common.confirm".localized, role: .cancel) { }
        } message: {
            Text(isEditMode ? "lottery_tool_config.result.update_success".localized : "lottery_tool_config.result.save_success".localized)
        }
        .overlay(
            // 奖品选择弹窗
            PrizeSelectionView(
                isPresented: $showPrizeSelection,
                onPrizeSelected: handlePrizeSelection
            )
            .allowsHitTesting(showPrizeSelection)
        )
    }
    
    // MARK: - View Components
    
    /**
     * 标题栏 - 与奖品库配置一致
     */
    private var headerSection: some View {
        HStack {
            Text(navigationTitle)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Spacer()

            // 关闭按钮
            Button(action: {
                dismissKeyboard()
                onDismiss()
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
            .disabled(formData.isSaving)
        }
        .padding(.horizontal, 24)
        .padding(.top, 24)
        .padding(.bottom, 16)
    }

    /**
     * 表单内容区域 - 与奖品库配置一致
     */
    private var formContentSection: some View {
        ScrollView(.vertical, showsIndicators: true) {
            VStack(spacing: 24) {
                // 班级信息卡片
                classInfoCard

                // 基本配置
                basicConfigSection

                // 道具项目配置
                itemConfigSection

                // 验证错误显示
                if !formData.validationErrors.isEmpty {
                    validationErrorsSection
                }
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
            .contentShape(Rectangle()) // 确保整个内容区域能响应手势
            .onTapGesture {
                // 点击内容区域关闭键盘
                dismissKeyboard()
            }
        }
        .frame(maxHeight: UIScreen.main.bounds.height * 0.5)
    }

    /**
     * 班级信息卡片
     */
    private var classInfoCard: some View {
        HStack(spacing: 12) {
            // 道具图标
            ZStack {
                Circle()
                    .fill(Color(hex: "#74c07f").opacity(0.1))
                    .frame(width: 40, height: 40)

                Image(systemName: getToolIcon())
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color(hex: "#74c07f"))
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(selectedClass.name ?? "未命名班级")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("\(toolType.displayName) · \(selectedClass.studentCount) 名学生")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(hex: "#edf5d9").opacity(0.3))
        .cornerRadius(12)
    }

    /**
     * 底部按钮区域 - 与奖品库配置一致
     */
    private var bottomButtonSection: some View {
        VStack(spacing: 16) {
            // 分隔线
            Rectangle()
                .fill(Color(hex: "#edf5d9"))
                .frame(height: 1)
                .padding(.horizontal, 24)

            // 按钮组
            HStack(spacing: 12) {
                // 取消按钮
                Button(action: {
                    dismissKeyboard()
                    onDismiss()
                }) {
                    Text("lottery_tool_config.button.cancel".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(Color(hex: "#f8f8f8"))
                        .cornerRadius(12)
                }
                .disabled(formData.isSaving)

                // 保存/完成按钮
                Button(action: {
                    dismissKeyboard()
                    saveConfiguration()
                }) {
                    Text(isEditMode ? "lottery_tool_config.button.save".localized : "lottery_tool_config.button.complete".localized)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "#74c07f"),
                                    Color(hex: "#a9d051")
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(12)
                }
                .disabled(formData.isSaving || formData.items.isEmpty)
            }
            .padding(.horizontal, 24)
            .padding(.bottom, 28)
        }
    }

    /**
     * 基本配置区域
     */
    private var basicConfigSection: some View {
        VStack(spacing: 16) {
            SectionHeader(title: "lottery_tool_config.form.basic_config".localized, icon: "gearshape.fill")
            
            VStack(spacing: 16) {
                // 道具数量配置 - 所有类型都使用可调整组件
                itemCountConfig
                
                // 积分消耗配置
                costPerPlayConfig
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(.systemGray5), lineWidth: 1)
            )
        }
    }
    
    /**
     * 道具数量配置（可调整）
     */
    private var itemCountConfig: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(String(format: "lottery_tool_config.form.item_count_title".localized, formData.localizedItemTitlePrefix))
                    .font(.system(size: 18, weight: .semibold))
                
                Spacer()
                
                Text("\(formData.itemCount)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(getToolColor())
            }
            
            HStack(spacing: 16) {
                Button("-") {
                    formData.updateItemCount(formData.itemCount - 1)
                }
                .buttonStyle(CounterButtonStyle())
                .disabled(formData.itemCount <= formData.minItemCount)
                
                Slider(
                    value: Binding(
                        get: { Double(formData.itemCount) },
                        set: { formData.updateItemCount(Int($0)) }
                    ),
                    in: Double(formData.minItemCount)...Double(formData.maxItemCount),
                    step: 1
                )
                .accentColor(getToolColor())
                
                Button("+") {
                    formData.updateItemCount(formData.itemCount + 1)
                }
                .buttonStyle(CounterButtonStyle())
                .disabled(formData.itemCount >= formData.maxItemCount)
            }
            
            Text(String(format: "lottery_tool_config.form.item_count_range".localized, "\(formData.minItemCount)", "\(formData.maxItemCount)"))
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    

    
    /**
     * 积分消耗配置
     */
    private var costPerPlayConfig: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("lottery_tool_config.form.cost_per_play".localized)
                    .font(.system(size: 18, weight: .semibold))
                
                Spacer()
            }
            
            HStack(spacing: 12) {
                TextField("lottery_tool_config.form.cost_per_play".localized, value: $formData.costPerPlay, format: .number)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.numberPad)
                    .frame(width: 100)
                
                Text("lottery_tool_config.form.cost_per_play".localized)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            
            Text("lottery_tool_config.form.suggested_range".localized)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    /**
     * 道具项目配置区域
     */
    private var itemConfigSection: some View {
        VStack(spacing: 16) {
            SectionHeader(title: String(format: "lottery_tool_config.form.item_config_title".localized, formData.localizedItemTitlePrefix), icon: "list.bullet")
            
            LazyVStack(spacing: 12) {
                ForEach(Array(formData.items.enumerated()), id: \.element.id) { index, item in
                    LotteryToolItemConfigCard(
                        item: item,
                        toolColor: getToolColor(),
                        onPrizeNameChanged: { newName in
                            formData.updateItemPrizeName(at: index, name: newName)
                        },
                        onPrizeSelectionTapped: {
                            openPrizeSelection(for: index)
                        }
                    )
                }
            }
        }
    }
    
    /**
     * 验证错误区域
     */
    private var validationErrorsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.red)
                
                Text("lottery_tool_config.validation.error_title".localized)
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.red)
                
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 8) {
                ForEach(formData.validationErrors, id: \.self) { error in
                    HStack(alignment: .top, spacing: 8) {
                        Text("•")
                            .foregroundColor(.red)
                        
                        Text(error)
                            .font(.subheadline)
                            .foregroundColor(.primary)
                        
                        Spacer()
                    }
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color.red.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.red.opacity(0.3), lineWidth: 1)
        )
    }
    
    // MARK: - Helper Methods
    
    /**
     * 设置表单数据
     */
    private func setupFormData() {
        if let config = existingConfig {
            // 从现有配置加载所有数据，包括基本配置和奖品项目
            formData.loadFromExistingConfig(config)
        } else {
            formData.setToolType(toolType)
        }
    }
    
    /**
     * 获取道具图标
     */
    private func getToolIcon() -> String {
        switch toolType {
        case .wheel:
            return "target"
        case .box:
            return "cube.box.fill"
        case .scratch:
            return "rectangle.and.pencil.and.ellipsis"
        }
    }
    
    /**
     * 获取道具颜色 - 统一使用主色调
     */
    private func getToolColor() -> Color {
        return Color(hex: "#74c07f")
    }
    
    /**
     * 处理奖品选择
     */
    private func handlePrizeSelection(_ prizeName: String) {
        guard let index = selectedItemIndex else { return }
        formData.updateItemPrizeName(at: index, name: prizeName)
        showPrizeSelection = false
        selectedItemIndex = nil
    }
    
    /**
     * 打开奖品选择弹窗
     */
    private func openPrizeSelection(for index: Int) {
        selectedItemIndex = index
        showPrizeSelection = true
    }
    
    /**
     * 保存配置
     */
    private func saveConfiguration() {
        guard formData.validate() else {
            // 显示验证错误
            alertMessage = formData.validationErrors.first ?? "lottery_tool_config.validation.error_title".localized
            showErrorAlert = true
            return
        }
        
        formData.isSaving = true
        
        let configData = formData.toConfigData()
        
        if let existingConfig = existingConfig {
            // 更新现有配置
            coreDataManager.updateLotteryToolConfig(
                existingConfig,
                itemCount: configData.itemCount,
                costPerPlay: configData.costPerPlay
            )
            
            // 重新创建道具项目
            coreDataManager.createLotteryToolItems(
                for: existingConfig,
                prizeNames: configData.prizeNames
            )
        } else {
            // 创建新配置
            let newConfig = coreDataManager.createLotteryToolConfig(
                for: selectedClass,
                toolType: configData.toolType,
                itemCount: configData.itemCount,
                costPerPlay: configData.costPerPlay
            )
            
            // 创建道具项目
            coreDataManager.createLotteryToolItems(
                for: newConfig,
                prizeNames: configData.prizeNames
            )
        }
        
        showSuccessAlert = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            onConfigurationComplete()
            onDismiss()
        }
        
        formData.isSaving = false
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }


}

/**
 * 道具项目配置卡片
 */
struct LotteryToolItemConfigCard: View {
    
    // MARK: - Properties
    
    @ObservedObject var item: LotteryToolItemData
    let toolColor: Color
    let onPrizeNameChanged: (String) -> Void
    let onPrizeSelectionTapped: () -> Void
    
    // MARK: - State
    
    @FocusState private var isTextFieldFocused: Bool
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(item.displayTitle)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(toolColor)
                
                Spacer()
                
                if !item.prizeName.isEmpty {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(Color(hex: "#74c07f"))
                }
            }
            
            HStack(spacing: 12) {
                TextField(item.placeholderText, text: Binding(
                    get: { item.prizeName },
                    set: { newValue in
                        item.prizeName = newValue
                        onPrizeNameChanged(newValue)
                    }
                ))
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .focused($isTextFieldFocused)
                .onSubmit {
                    isTextFieldFocused = false
                }
                
                // 奖品库选择按钮
                Button(action: onPrizeSelectionTapped) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(toolColor)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(isTextFieldFocused ? Color(hex: "#74c07f") : Color(hex: "#74c07f").opacity(0.2), lineWidth: isTextFieldFocused ? 1.5 : 1)
        )
        .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: 2)
    }
}

/**
 * 区域标题组件
 */
struct SectionHeader: View {
    let title: String
    let icon: String
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color(hex: "#74c07f"))
            
            Text(title)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
}

/**
 * 计数器按钮样式
 */
struct CounterButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 22, weight: .semibold))
            .foregroundColor(Color(hex: "#74c07f"))
            .frame(width: 36, height: 36)
            .background(Color(hex: "#74c07f").opacity(0.1))
            .cornerRadius(8)
            .scaleEffect(configuration.isPressed ? 0.9 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Preview

#if DEBUG
struct LotteryToolConfigFormView_Previews: PreviewProvider {
    static var previews: some View {
        let previewContext = PersistenceController.preview.container.viewContext
        
        let user = User(context: previewContext)
        user.id = UUID()
        user.nickname = "预览用户"
        
        let schoolClass = SchoolClass(context: previewContext)
        schoolClass.id = UUID()
        schoolClass.name = "三年级一班"
        schoolClass.owner = user
        schoolClass.createdAt = Date()
        
        return LotteryToolConfigFormView(
            selectedClass: schoolClass,
            toolType: .wheel,
            existingConfig: nil,
            onConfigurationComplete: {},
            onDismiss: {}
        )
        .environmentObject(CoreDataManager(persistenceController: PersistenceController.preview))
    }
}
#endif 