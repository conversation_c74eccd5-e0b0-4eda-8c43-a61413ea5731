//
//  ClassDeleteConfirmationDialog.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/16.
//

import SwiftUI

/**
 * 班级删除确认对话框组件
 * 与现有UI风格保持一致的自定义模态对话框
 */
struct ClassDeleteConfirmationDialog: View {
    
    @Binding var isPresented: Bool
    let className: String
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        onCancel()
                    }
                    .transition(.opacity)
                
                // 确认对话框卡片
                VStack(spacing: 20) {
                    // 警告图标
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.red)
                        .padding(.top, 10)
                    
                    // 标题
                    Text("delete_class.confirmation.title".localized)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    // 内容
                    Text("delete_class.confirmation.message".localized(with: className))
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                    
                    // 按钮组
                    HStack(spacing: 15) {
                        // 取消按钮
                        Button(action: onCancel) {
                            Text("delete_class.confirmation.cancel_button".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.ConfirmationDialog.cancelButtonColor)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(Color.white)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(DesignSystem.ConfirmationDialog.cancelButtonColor, lineWidth: 1.5)
                                )
                                .cornerRadius(12)
                        }
                        
                        // 确定删除按钮
                        Button(action: onConfirm) {
                            Text("delete_class.confirmation.confirm_button".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(Color.red)
                                .cornerRadius(12)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 10)
                }
                .frame(maxWidth: DesignSystem.ConfirmationDialog.maxWidth)
                .background(DesignSystem.ConfirmationDialog.backgroundColor)
                .cornerRadius(DesignSystem.ConfirmationDialog.cornerRadius)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.ConfirmationDialog.cornerRadius)
                        .stroke(DesignSystem.ConfirmationDialog.borderColor, lineWidth: 2)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
                .scaleEffect(isPresented ? 1.0 : 0.9)
                .opacity(isPresented ? 1.0 : 0.0)
                .animation(.spring(response: 0.5, dampingFraction: 0.8), value: isPresented)
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        ClassDeleteConfirmationDialog(
            isPresented: .constant(true),
            className: "一年级1班",
            onConfirm: {
                print("确认删除班级")
            },
            onCancel: {
                print("取消删除班级")
            }
        )
    }
} 