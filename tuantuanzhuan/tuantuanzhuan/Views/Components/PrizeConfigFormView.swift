//
//  PrizeConfigFormView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/16.
//

import SwiftUI

/**
 * 奖品配置表单组件
 * 支持单个和批量奖品信息输入，可动态添加表单行
 */
struct PrizeConfigFormView: View {
    
    @Binding var isPresented: Bool
    @State private var prizeForms: [PrizeFormData] = []
    @State private var isSubmitting: Bool = false
    @State private var validationErrors: [String] = []
    @State private var animationTrigger = false
    @State private var showConfiguredPrizes = false
    
    let onSubmit: ([PrizeFormData]) -> Void
    let onCancel: () -> Void
    
    // MARK: - CoreData Manager
    private let coreDataManager = CoreDataManager.shared
    
    // MARK: - 调试日志系统
    private let componentId = UUID().uuidString.prefix(8)
    
    /**
     * 日志记录工具函数
     */
    private func logPrizeConfig(_ message: String, type: String = "INFO") {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        let timestamp = formatter.string(from: Date())
        let threadName = Thread.isMainThread ? "MAIN" : "BG"
        print("[\(timestamp)] [PrizeConfig-\(componentId)-\(type)-\(threadName)] \(message)")
    }
    
    /**
     * 状态变化追踪函数
     */
    private func logStateChange<T>(_ propertyName: String, oldValue: T, newValue: T) {
        logPrizeConfig("🔄 状态变化: \(propertyName): \(oldValue) → \(newValue)", type: "STATE")
        
        // 记录调用栈信息
        let callStack = Thread.callStackSymbols
        if callStack.count > 2 {
            let caller = extractFunctionName(from: callStack[2])
            logPrizeConfig("📍 调用位置: \(caller)", type: "STATE")
        }
    }
    
    /**
     * 从调用栈中提取函数名
     */
    private func extractFunctionName(from callStackString: String) -> String {
        let components = callStackString.components(separatedBy: " ")
        if let functionPart = components.last {
            return String(functionPart.prefix(50)) // 限制长度
        }
        return "Unknown"
    }
    
    /**
     * 线程安全检查
     */
    private func ensureMainThread(_ operation: String) {
        if !Thread.isMainThread {
            logPrizeConfig("⚠️ 线程警告: \(operation) 不在主线程执行", type: "THREAD")
        }
    }

    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    // 注释掉点击外部区域关闭弹窗的手势，防止误操作
                    // .onTapGesture {
                    //     logPrizeConfig("👆 用户点击背景关闭表单", type: "USER")
                    //     ensureMainThread("背景点击处理")
                    //     
                    //     if !isSubmitting {
                    //         logPrizeConfig("✅ 表单未提交中，执行关闭操作", type: "USER")
                    //         handleFormCancel()
                    //     } else {
                    //         logPrizeConfig("⚠️ 表单提交中，忽略关闭操作", type: "USER")
                    //     }
                    // }
                    .transition(.opacity)
                
                // 表单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Text("prize_config.form.title".localized)
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            Spacer()
                            
                            // 添加奖品按钮
                            Button(action: {
                                logPrizeConfig("👆 用户点击添加奖品按钮", type: "USER")
                                addNewPrizeForm()
                            }) {
                                Image(systemName: "plus.circle.fill")
                                    .font(.system(size: 26))
                                    .foregroundColor(Color(hex: "#74c07f"))
                            }
                            .disabled(isSubmitting)
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 24)
                        .padding(.bottom, 16)
                        .onTapGesture {
                            // 点击标题栏区域关闭键盘
                            logPrizeConfig("👆 用户点击标题栏区域", type: "USER")
                            dismissKeyboard()
                        }
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                            .padding(.horizontal, 24)
                        
                        // 表单内容区域
                        ScrollView(.vertical, showsIndicators: true) {
                            VStack(spacing: 16) {
                                ForEach(prizeForms.indices, id: \.self) { index in
                                    PrizeFormRow(
                                        prize: $prizeForms[index],
                                        index: index,
                                        canDelete: prizeForms.count > 1,
                                        onDelete: {
                                            logPrizeConfig("👆 用户删除奖品表单行: \(index)", type: "USER")
                                            removePrizeForm(at: index)
                                        }
                                    )
                                    .disabled(isSubmitting)
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 20)
                        }
                        .frame(maxHeight: geometry.size.height * 0.5)
                        .contentShape(Rectangle()) // 确保整个滚动区域都能响应手势
                        .onTapGesture {
                            // 点击表单内容区域关闭键盘
                            logPrizeConfig("👆 用户点击表单内容区域", type: "USER")
                            dismissKeyboard()
                        }
                        
                        // 错误提示区域
                        if !validationErrors.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                ForEach(validationErrors, id: \.self) { error in
                                    Text(error)
                                        .font(.system(size: 13, weight: .regular))
                                        .foregroundColor(.red)
                                        .multilineTextAlignment(.leading)
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(Color.red.opacity(0.05))
                            .onTapGesture {
                                // 点击错误提示区域关闭键盘
                                logPrizeConfig("👆 用户点击错误提示区域", type: "USER")
                                dismissKeyboard()
                            }
                        }
                        
                        // 已配置奖品入口
                        HStack {
                            Button(action: {
                                logPrizeConfig("👆 用户点击查看已配置奖品", type: "USER")
                                showConfiguredPrizes = true
                            }) {
                                Text("prize_config.form.configured_prizes".localized)
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(Color(hex: "#74c07f"))
                                    .underline()
                            }
                            .disabled(isSubmitting)
                            
                            Spacer()
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 16)
                        .onTapGesture {
                            // 点击已配置奖品入口区域关闭键盘（Spacer区域）
                            logPrizeConfig("👆 用户点击已配置奖品入口区域", type: "USER")
                            dismissKeyboard()
                        }
                        
                        // 底部按钮区域
                        HStack(spacing: 16) {
                            // 取消按钮
                            Button(action: {
                                logPrizeConfig("👆 用户点击取消按钮", type: "USER")
                                ensureMainThread("取消按钮点击处理")
                                
                                if !isSubmitting {
                                    logPrizeConfig("✅ 表单未提交中，执行取消操作", type: "USER")
                                    dismissKeyboard()
                                    handleFormCancel()
                                } else {
                                    logPrizeConfig("⚠️ 表单提交中，忽略取消操作", type: "USER")
                                }
                            }) {
                                Text("common.button.cancel".localized)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(Color.gray)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 44)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(12)
                            }
                            .disabled(isSubmitting)
                            
                            // 添加奖品按钮
                            Button(action: {
                                logPrizeConfig("👆 用户点击提交奖品按钮", type: "USER")
                                ensureMainThread("提交按钮点击处理")
                                
                                dismissKeyboard()
                                submitPrizes()
                            }) {
                                HStack(spacing: 8) {
                                    if isSubmitting {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    }
                                    
                                    Text(isSubmitting ? "prize_config.form.submitting".localized : "prize_config.form.submit".localized)
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.white)
                                }
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(hex: "#74c07f"),
                                            Color(hex: "#a9d051")
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .cornerRadius(12)
                                .opacity(isSubmitting ? 0.8 : 1.0)
                            }
                            .disabled(isSubmitting || prizeForms.isEmpty)
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 20)
                        .padding(.bottom, 28)
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 420))
                    .frame(maxHeight: geometry.size.height * 0.8)
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color(hex: "#74c07f").opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .overlay(
            // 已配置奖品列表弹窗
            ConfiguredPrizesListView(
                isPresented: $showConfiguredPrizes
            )
        )
        .onAppear {
            logPrizeConfig("🎬 组件出现 (onAppear)", type: "LIFECYCLE")
            setupInitialState()
        }
        .onDisappear {
            logPrizeConfig("🏁 组件消失 (onDisappear)", type: "LIFECYCLE")
        }
        .onChange(of: isPresented) { newValue in
            let oldValue = !newValue // 推断旧值
            logStateChange("isPresented", oldValue: oldValue, newValue: newValue)
            ensureMainThread("isPresented变化处理")
            
            if newValue {
                logPrizeConfig("📱 表单显示开始", type: "LIFECYCLE")
                let _ = withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    logPrizeConfig("🎯 开始显示动画", type: "ANIMATION")
                    animationTrigger = true
                }
                setupInitialState()
                logPrizeConfig("📱 表单显示完成", type: "LIFECYCLE")
            } else {
                logPrizeConfig("📱 表单隐藏开始", type: "LIFECYCLE")
                let oldAnimationTrigger = animationTrigger
                animationTrigger = false
                logStateChange("animationTrigger", oldValue: oldAnimationTrigger, newValue: animationTrigger)
                
                // 延迟重置表单状态，避免动画期间状态冲突
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    if !self.isPresented { // 双重检查，确保表单确实已关闭
                        logPrizeConfig("🧹 执行延迟表单重置", type: "LIFECYCLE")
                        resetForm()
                    } else {
                        logPrizeConfig("⚠️ 表单重新打开，跳过重置操作", type: "LIFECYCLE")
                    }
                }
                logPrizeConfig("📱 表单隐藏完成", type: "LIFECYCLE")
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 安全的表单取消处理
     */
    private func handleFormCancel() {
        logPrizeConfig("🚫 开始处理表单取消", type: "CANCEL")
        ensureMainThread("表单取消处理")
        
        // 立即停止任何正在进行的提交操作
        if isSubmitting {
            logPrizeConfig("⚠️ 强制停止提交操作", type: "CANCEL")
            isSubmitting = false
        }
        
        // 清理验证错误
        if !validationErrors.isEmpty {
            logPrizeConfig("🧹 清理验证错误: \(validationErrors.count)个", type: "CANCEL")
            validationErrors = []
        }
        
        // 调用父组件的取消回调
        logPrizeConfig("📞 调用onCancel回调", type: "CANCEL")
        onCancel()
        
        logPrizeConfig("✅ 表单取消处理完成", type: "CANCEL")
    }
    
    /**
     * 设置初始状态
     */
    private func setupInitialState() {
        logPrizeConfig("⚙️ 开始设置初始状态", type: "INIT")
        ensureMainThread("初始状态设置")
        
        let oldFormsCount = prizeForms.count
        if prizeForms.isEmpty {
            prizeForms = [PrizeFormData()]
            logPrizeConfig("➕ 添加默认奖品表单行", type: "INIT")
        }
        logStateChange("prizeForms.count", oldValue: oldFormsCount, newValue: prizeForms.count)
        
        let oldErrorsCount = validationErrors.count
        validationErrors = []
        logStateChange("validationErrors.count", oldValue: oldErrorsCount, newValue: 0)
        
        logPrizeConfig("✅ 初始状态设置完成", type: "INIT")
    }
    
    /**
     * 添加新的奖品表单行
     */
    private func addNewPrizeForm() {
        logPrizeConfig("➕ 开始添加新奖品表单行", type: "ADD")
        ensureMainThread("添加奖品表单行")
        
        let oldCount = prizeForms.count
        let _ = withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            prizeForms.append(PrizeFormData())
            logPrizeConfig("🎯 新奖品表单行动画添加", type: "ANIMATION")
        }
        logStateChange("prizeForms.count", oldValue: oldCount, newValue: prizeForms.count)
        logPrizeConfig("✅ 新奖品表单行添加完成", type: "ADD")
    }
    
    /**
     * 删除指定位置的奖品表单行
     */
    private func removePrizeForm(at index: Int) {
        logPrizeConfig("🗑️ 开始删除奖品表单行: index=\(index)", type: "DELETE")
        ensureMainThread("删除奖品表单行")
        
        guard index < prizeForms.count else {
            logPrizeConfig("⚠️ 删除索引越界: \(index) >= \(prizeForms.count)", type: "ERROR")
            return
        }
        
        let oldCount = prizeForms.count
        let deletedForm = prizeForms[index]
        
        let _ = withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            prizeForms.remove(at: index)
            logPrizeConfig("🎯 奖品表单行动画删除", type: "ANIMATION")
        }
        
        logStateChange("prizeForms.count", oldValue: oldCount, newValue: prizeForms.count)
        logPrizeConfig("✅ 奖品表单行删除完成: '\(deletedForm.name)'", type: "DELETE")
    }
    
    /**
     * 提交奖品表单
     */
    private func submitPrizes() {
        logPrizeConfig("📤 开始提交奖品表单", type: "SUBMIT")
        ensureMainThread("奖品表单提交")
        
        let oldSubmitting = isSubmitting
        isSubmitting = true
        logStateChange("isSubmitting", oldValue: oldSubmitting, newValue: isSubmitting)
        
        let oldErrorsCount = validationErrors.count
        validationErrors = []
        logStateChange("validationErrors.count", oldValue: oldErrorsCount, newValue: 0)
        
        logPrizeConfig("📊 表单数据统计: \(prizeForms.count)个奖品表单", type: "SUBMIT")
        
        // 安全获取现有奖品模板
        var existingPrizes: [PrizeTemplate] = []
        
        // 在后台线程安全获取CoreData数据
        DispatchQueue.global(qos: .userInitiated).async {
            self.logPrizeConfig("🔄 后台线程获取现有奖品数据", type: "COREDATA")
            
            // 使用CoreData的performAndWait确保线程安全
            self.coreDataManager.viewContext.performAndWait {
                existingPrizes = self.coreDataManager.getAllPrizeTemplates()
                self.logPrizeConfig("📋 获取到 \(existingPrizes.count) 个现有奖品模板", type: "COREDATA")
            }
            
            // 回到主线程执行验证和UI更新
            DispatchQueue.main.async {
                guard self.isSubmitting else {
                    self.logPrizeConfig("⚠️ 表单已取消提交，跳过验证", type: "SUBMIT")
                    return
                }
                
                self.logPrizeConfig("✅ 开始批量验证奖品数据", type: "VALIDATION")
                
                // 批量验证
                let validationResult = self.prizeForms.validateBatch(existingPrizes: existingPrizes)
                self.logPrizeConfig("📊 验证结果: 有效=\(validationResult.isValid), 错误=\(validationResult.errorMessages.count)个, 有效奖品=\(validationResult.validPrizes.count)个", type: "VALIDATION")
                
                if validationResult.isValid {
                    // 验证通过，提交数据
                    self.logPrizeConfig("✅ 验证通过，调用onSubmit回调", type: "SUBMIT")
                    self.onSubmit(validationResult.validPrizes)
                } else {
                    // 验证失败，显示错误信息
                    self.logPrizeConfig("❌ 验证失败，显示错误信息", type: "VALIDATION")
                    self.validationErrors = validationResult.errorMessages
                    self.isSubmitting = false
                    
                    for (index, error) in validationResult.errorMessages.enumerated() {
                        self.logPrizeConfig("❌ 验证错误[\(index+1)]: \(error)", type: "VALIDATION")
                    }
                }
            }
        }
    }
    
    /**
     * 重置表单状态
     */
    private func resetForm() {
        logPrizeConfig("🧹 开始重置表单状态", type: "RESET")
        ensureMainThread("表单状态重置")
        
        let oldFormsCount = prizeForms.count
        let oldErrorsCount = validationErrors.count
        let oldSubmitting = isSubmitting
        
        prizeForms = []
        validationErrors = []
        isSubmitting = false
        
        logStateChange("prizeForms.count", oldValue: oldFormsCount, newValue: 0)
        logStateChange("validationErrors.count", oldValue: oldErrorsCount, newValue: 0)
        logStateChange("isSubmitting", oldValue: oldSubmitting, newValue: false)
        
        logPrizeConfig("✅ 表单状态重置完成", type: "RESET")
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        logPrizeConfig("⌨️ 关闭键盘", type: "UI")
        ensureMainThread("键盘关闭")
        
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

/**
 * 奖品表单行组件
 */
private struct PrizeFormRow: View {
    
    @Binding var prize: PrizeFormData
    let index: Int
    let canDelete: Bool
    let onDelete: () -> Void
    
    // 奖品类型选项
    private let prizeTypes = ["prize_config.type.virtual".localized, "prize_config.type.physical".localized]
    
    var body: some View {
        VStack(spacing: 16) {
            // 表单行标题
            HStack {
                Text("prize_config.form.prize_number_label".localized(with: "\(index + 1)"))
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                // 删除按钮
                if canDelete {
                    Button(action: onDelete) {
                        Image(systemName: "minus.circle.fill")
                            .font(.system(size: 22))
                            .foregroundColor(.red.opacity(0.8))
                    }
                }
            }
            
            // 奖品名称输入
            VStack(alignment: .leading, spacing: 8) {
                Text("Prize name".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                TextField("prize_config.form.name_placeholder".localized, text: $prize.name)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .font(.system(size: 16))
            }
            
            // 奖品类型和积分成本
            HStack(spacing: 16) {
                // 奖品类型选择器
                VStack(alignment: .leading, spacing: 8) {
                    Text("prize_config.form.prize_type".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    SegmentButton(
                        selection: $prize.type,
                        options: prizeTypes,
                        labels: Dictionary(uniqueKeysWithValues: prizeTypes.map { ($0, $0) }),
                        backgroundColor: Color(hex: "#f8fff2"),
                        selectedColor: Color(hex: "#a9d051"),
                        textColor: DesignSystem.Colors.textPrimary,
                        fontSize: 14
                    )
                }
                .frame(maxWidth: .infinity)
                
                // 积分成本输入
                VStack(alignment: .leading, spacing: 8) {
                    Text("Cost".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    TextField("prize_config.form.cost_placeholder".localized, text: $prize.cost)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .font(.system(size: 16))
                        .keyboardType(.numberPad)
                        .frame(width: 80)
                }
            }
        }
        .padding(16)
        .background(Color(hex: "#f8ffe5"))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
        )
    }
}

// MARK: - Preview
#Preview {
    PrizeConfigFormView(
        isPresented: .constant(true),
        onSubmit: { _ in },
        onCancel: { }
    )
} 