//
//  CloudKitStatusView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/20.
//

import SwiftUI
import CoreData

/**
 * CloudKit状态显示组件
 * 用于显示CloudKit同步状态、上次同步时间以及手动触发同步
 */
struct CloudKitStatusView: View {
    
    @EnvironmentObject private var coreDataManager: CoreDataManager
    @State private var showingDetail = false
    
    var body: some View {
        VStack(spacing: 12) {
            // 标题
            HStack {
                Image(systemName: "icloud")
                    .foregroundColor(.blue)
                Text("CloudKit同步状态")
                    .font(.headline)
                    .fontWeight(.medium)
                
                Spacer()
                
                // 展开/收起按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showingDetail.toggle()
                    }
                }) {
                    Image(systemName: showingDetail ? "chevron.up" : "chevron.down")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
            }
            
            // 基本状态显示
            HStack {
                // 状态指示器
                Circle()
                    .fill(statusColor)
                    .frame(width: 8, height: 8)
                
                Text(coreDataManager.getCloudKitStatusText())
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // 同步动画
                if coreDataManager.isSyncing {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            // 详细信息（可展开）
            if showingDetail {
                VStack(spacing: 8) {
                    Divider()
                    
                    // 上次同步时间
                    HStack {
                        Text("上次同步:")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text(coreDataManager.getLastSyncTimeText())
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // 手动同步按钮
                    HStack {
                        Button(action: {
                            coreDataManager.checkCloudKitAvailability()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "arrow.clockwise")
                                Text("检查状态")
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                        .disabled(coreDataManager.isSyncing)
                        
                        Spacer()
                        
                        Button(action: {
                            coreDataManager.triggerCloudKitSync()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "icloud.and.arrow.up")
                                Text("强制同步")
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                        .disabled(coreDataManager.isSyncing || !coreDataManager.cloudKitSyncEnabled)
                    }
                    
                    // 错误信息（如果有）
                    if coreDataManager.syncError != nil {
                        HStack {
                            Image(systemName: "exclamationmark.triangle")
                                .foregroundColor(.orange)
                                .font(.caption)
                            
                            Text("cloudkit.sync.error".localized)
                                .font(.caption)
                                .foregroundColor(.orange)
                                .lineLimit(2)
                            
                            Spacer()
                        }
                        .padding(.top, 4)
                    }
                    
                    // CloudKit配置信息
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("同步已启用:")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text(coreDataManager.cloudKitSyncEnabled ? "是" : "否")
                                .font(.caption2)
                                .foregroundColor(coreDataManager.cloudKitSyncEnabled ? .green : .red)
                        }
                        
                        HStack {
                            Text("容器ID:")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text("iCloud.com.rainkygong.tuantuanzhuan")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.top, 4)
                }
                .transition(.opacity.combined(with: .slide))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .onAppear {
            // 页面出现时检查CloudKit状态
            coreDataManager.checkCloudKitAvailability()
        }
    }
    
    // MARK: - 计算属性
    
    /**
     * 状态指示颜色
     */
    private var statusColor: Color {
        switch coreDataManager.cloudKitStatus {
        case .available, .syncCompleted:
            return .green
        case .syncInProgress:
            return .blue
        case .noAccount, .restricted, .couldNotDetermine, .syncFailed:
            return .red
        case .unknown:
            return .gray
        }
    }
}

// MARK: - 预览

struct CloudKitStatusView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // 正常状态
            CloudKitStatusView()
                .environmentObject({
                    let manager = CoreDataManager.shared
                    manager.cloudKitStatus = .available
                    return manager
                }())
            
            // 同步中状态
            CloudKitStatusView()
                .environmentObject({
                    let manager = CoreDataManager.shared
                    manager.cloudKitStatus = .syncInProgress
                    manager.isSyncing = true
                    return manager
                }())
            
            // 错误状态
            CloudKitStatusView()
                .environmentObject({
                    let manager = CoreDataManager.shared
                    manager.cloudKitStatus = .noAccount
                    return manager
                }())
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .previewLayout(.sizeThatFits)
    }
}