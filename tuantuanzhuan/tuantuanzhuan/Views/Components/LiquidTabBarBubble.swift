//
//  LiquidTabBarBubble.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 液态导航栏融球组件
 * 负责显示黄色圆球背景和选中的图标
 */
struct LiquidTabBarBubble: View {
    
    // MARK: - Properties
    
    /// 融球中心X坐标
    let centerX: CGFloat
    
    /// 当前选中的图标名称
    let selectedIcon: String
    
    // MARK: - Constants
    
    private var bubbleSize: CGFloat { DesignSystem.LiquidTabBar.bubbleSize }
    private var iconSize: CGFloat { DesignSystem.LiquidTabBar.bubbleIconSize }
    
    // MARK: - View
    
    var body: some View {
        GeometryReader { geometry in
            // 融球背景
            Circle()
                .fill(DesignSystem.LiquidTabBar.bubbleColor)
                .frame(width: bubbleSize, height: bubbleSize)
                .overlay(
                    // 图标
                    Image(selectedIcon)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: iconSize, height: iconSize)
                        .foregroundColor(DesignSystem.LiquidTabBar.bubbleIconColor)
                )
                .position(
                    x: centerX,
                    y: geometry.size.height / 2 - 10 // 向上偏移10px以配合上拱效果
                )
        }
    }
}

// MARK: - Preview
#Preview {
    VStack {
        Spacer()
        
        ZStack {
            Rectangle()
                .fill(Color.white)
                .frame(height: 72)
            
                    LiquidTabBarBubble(
            centerX: 150,
            selectedIcon: "shouye1_1"
        )
            .frame(height: 72)
        }
    }
    .background(Color.gray.opacity(0.2))
} 