//
//  ClassSelectionView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/16.
//

import SwiftUI

/**
 * 班级选择视图
 * 用于抽奖道具配置前的班级选择界面
 */
struct ClassSelectionView: View {

    // MARK: - Environment

    @EnvironmentObject private var coreDataManager: CoreDataManager

    // MARK: - Properties

    /// 可选择的班级列表
    let schoolClasses: [SchoolClass]

    /// 选择班级的回调
    let onClassSelected: (SchoolClass) -> Void

    /// 关闭弹窗的回调
    let onDismiss: () -> Void

    // MARK: - State

    @State private var animationTrigger = false

    // MARK: - Body

    var body: some View {
        ZStack {
            // 半透明背景遮罩 - 与奖品库配置一致
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissModal()
                }

            // 弹窗对话框 - 使用与奖品库配置相同的布局
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 标题栏
                    headerSection

                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 24)

                    // 班级列表内容
                    classListContent

                    // 底部空白
                    Spacer(minLength: 20)
                }
                .frame(maxWidth: min(geometry.size.width - 40, 420))
                .frame(maxHeight: geometry.size.height * 0.75)
                .background(Color.white)
                .cornerRadius(20)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color(hex: "#74c07f").opacity(0.2), lineWidth: 1.5)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                .scaleEffect(animationTrigger ? 1.0 : 0.9)
                .opacity(animationTrigger ? 1.0 : 0.0)
                .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
            }
        }
        .onAppear {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                animationTrigger = true
            }
        }
    }

    // MARK: - View Components

    /**
     * 标题栏 - 与奖品库配置一致
     */
    private var headerSection: some View {
        HStack {
            Text("lottery_tool_config.select_class.title".localized)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Spacer()

            // 关闭按钮
            Button(action: {
                dismissModal()
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
        }
        .padding(.horizontal, 24)
        .padding(.top, 24)
        .padding(.bottom, 16)
    }

    /**
     * 班级列表内容 - 与奖品库配置一致
     */
    private var classListContent: some View {
        Group {
            if schoolClasses.isEmpty {
                emptyStateView
            } else {
                ScrollView(.vertical, showsIndicators: true) {
                    LazyVStack(spacing: 12) {
                        ForEach(schoolClasses, id: \.id) { schoolClass in
                            ClassSelectionCard(
                                schoolClass: schoolClass,
                                onTap: {
                                    onClassSelected(schoolClass)
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 20)
                }
                .frame(maxHeight: UIScreen.main.bounds.height * 0.5)
            }
        }
    }


    /**
     * 空状态视图 - 与奖品库配置一致
     */
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "folder.badge.questionmark")
                .font(.system(size: 48))
                .foregroundColor(Color(hex: "#74c07f").opacity(0.4))

            Text("lottery_tool_config.empty_state.no_classes".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.vertical, 40)
    }

    // MARK: - Helper Methods

    /**
     * 关闭弹窗
     */
    private func dismissModal() {
        onDismiss()
    }
}

/**
 * 班级选择卡片
 */
struct ClassSelectionCard: View {

    // MARK: - Properties

    let schoolClass: SchoolClass
    let onTap: () -> Void

    // MARK: - State

    @State private var isPressed = false

    // MARK: - Body

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // 班级图标
                classIcon

                // 班级信息
                classInfo

                Spacer()

                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color(hex: "#74c07f"))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(hex: "#74c07f").opacity(0.2), lineWidth: 1)
            )
            .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: 2)
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }



    // MARK: - View Components

    /**
     * 班级图标
     */
    private var classIcon: some View {
        ZStack {
            Circle()
                .fill(Color(hex: "#74c07f").opacity(0.1))
                .frame(width: 48, height: 48)

            Image(systemName: "person.3.fill")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(Color(hex: "#74c07f"))
        }
    }

    /**
     * 班级信息
     */
    private var classInfo: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(schoolClass.name ?? "未命名班级")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(1)

            HStack(spacing: 12) {
                HStack(spacing: 4) {
                    Image(systemName: "person.2.fill")
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("\(schoolClass.studentCount) 人")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                HStack(spacing: 4) {
                    Image(systemName: "star.fill")
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(Color(hex: "#87C441"))

                    Text("\(schoolClass.totalPoints) 总积分")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color(hex: "#87C441"))
                }
            }

            // 显示已配置的抽奖道具数量
            let configuredToolsCount = schoolClass.sortedLotteryConfigs.count
            if configuredToolsCount > 0 {
                HStack(spacing: 4) {
                    Image(systemName: "gamecontroller.fill")
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(Color(hex: "#FF9500"))

                    Text("已配置 \(configuredToolsCount) 种抽奖道具")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color(hex: "#FF9500"))
                }
            }
        }
    }
}

// MARK: - Preview

#if DEBUG
struct ClassSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        // 创建预览数据
        let previewContext = PersistenceController.preview.container.viewContext
        
        let user = User(context: previewContext)
        user.id = UUID()
        user.nickname = "预览用户"
        
        let class1 = SchoolClass(context: previewContext)
        class1.id = UUID()
        class1.name = "三年级一班"
        class1.owner = user
        class1.createdAt = Date()
        
        let class2 = SchoolClass(context: previewContext)
        class2.id = UUID()
        class2.name = "三年级二班"
        class2.owner = user
        class2.createdAt = Date()
        
        // 为第二个班级添加一些学生
        for i in 1...5 {
            let student = Student(context: previewContext)
            student.id = UUID()
            student.name = "学生\(i)"
            student.studentNumber = "\(i)"
            student.gender = i % 2 == 0 ? "男" : "女"
            student.point = Int32.random(in: 50...100)
            student.schoolClass = class2
        }
        
        return ClassSelectionView(
            schoolClasses: [class1, class2],
            onClassSelected: { _ in },
            onDismiss: { }
        )
        .environmentObject(CoreDataManager(persistenceController: PersistenceController.preview))
    }
}
#endif 