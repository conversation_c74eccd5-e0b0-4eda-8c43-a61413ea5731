//
//  ClassResetOptionsView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 班级操作选项菜单组件
 * 显示班级配置、重置积分、重置历史记录三个选项的下拉菜单
 */
struct ClassResetOptionsView: View {
    
    @Binding var isPresented: Bool
    let className: String
    let onConfigTapped: () -> Void
    let onResetPointsTapped: () -> Void
    let onResetHistoryTapped: () -> Void
    
    @State private var animationTrigger = false
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 选项菜单卡片
                VStack(spacing: 0) {
                    // 标题栏
                    HStack {
                        Text("class_reset.options.title".localized)
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 8)
                    
                    // 班级名称
                    HStack {
                        Text(className)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 16)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 选项列表
                    VStack(spacing: 0) {
                        // 班级配置选项
                        ClassResetOptionButton(
                            title: "class_reset.options.config".localized,
                            subtitle: "class_reset.options.config_description".localized,
                            iconName: "gearshape.fill",
                            iconColor: Color(hex: "#74c07f"),
                            action: {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                    isPresented = false
                                }
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                    onConfigTapped()
                                }
                            }
                        )
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#f5f5f5"))
                            .frame(height: 1)
                            .padding(.horizontal, 20)
                        
                        // 重置积分选项
                        ClassResetOptionButton(
                            title: "class_reset.options.reset_points".localized,
                            subtitle: "class_reset.options.reset_points_description".localized,
                            iconName: "arrow.clockwise.circle.fill",
                            iconColor: Color(hex: "#a9d051"),
                            action: {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                    isPresented = false
                                }
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                    onResetPointsTapped()
                                }
                            }
                        )
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#f5f5f5"))
                            .frame(height: 1)
                            .padding(.horizontal, 20)
                        
                        // 重置历史记录选项
                        ClassResetOptionButton(
                            title: "class_reset.options.reset_history".localized,
                            subtitle: "class_reset.options.reset_history_description".localized,
                            iconName: "trash.circle.fill",
                            iconColor: Color(hex: "#ff6b6b"),
                            isDestructive: true,
                            action: {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                    isPresented = false
                                }
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                    onResetHistoryTapped()
                                }
                            }
                        )
                    }
                    .padding(.bottom, 10)
                }
                .frame(width: 300)
                .background(Color.white)
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1.5)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                .scaleEffect(animationTrigger ? 1.0 : 0.9)
                .opacity(animationTrigger ? 1.0 : 0.0)
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
    }
}

/**
 * 班级操作选项按钮组件
 */
struct ClassResetOptionButton: View {
    
    let title: String
    let subtitle: String
    let iconName: String
    let iconColor: Color
    let isDestructive: Bool
    let action: () -> Void
    
    init(title: String, subtitle: String, iconName: String, iconColor: Color, isDestructive: Bool = false, action: @escaping () -> Void) {
        self.title = title
        self.subtitle = subtitle
        self.iconName = iconName
        self.iconColor = iconColor
        self.isDestructive = isDestructive
        self.action = action
    }
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                isPressed = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isPressed = false
                action()
            }
        }) {
            HStack(spacing: 16) {
                // 图标
                ZStack {
                    Circle()
                        .fill(iconColor.opacity(0.15))
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: iconName)
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(iconColor)
                }
                
                // 文字内容
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(isDestructive ? Color(hex: "#ff6b6b") : DesignSystem.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    Text(subtitle)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // 箭头图标
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary.opacity(0.6))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                Rectangle()
                    .fill(isPressed ? (isDestructive ? Color(hex: "#fff5f5") : Color(hex: "#f8ffe5")) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: isPressed)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.1)
            .ignoresSafeArea()
        
        ClassResetOptionsView(
            isPresented: .constant(true),
            className: "一年级1班",
            onConfigTapped: {
                print("配置班级")
            },
            onResetPointsTapped: {
                print("重置积分")
            },
            onResetHistoryTapped: {
                print("重置历史记录")
            }
        )
    }
} 