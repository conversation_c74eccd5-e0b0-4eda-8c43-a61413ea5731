//
//  LiquidTabBarBackground.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 液态导航栏背景组件
 * 负责绘制白色背景、绿色边框和柔和阴影
 */
struct LiquidTabBarBackground: View {
    
    // MARK: - Properties
    
    /// 融球中心X坐标
    let bubbleCenter: CGFloat
    
    /// 过渡进度
    let transitionProgress: CGFloat
    
    // MARK: - View
    
    var body: some View {
        ZStack {
            // 主背景形状
            LiquidTabBarShape(
                bubbleCenter: bubbleCenter,
                transitionProgress: transitionProgress
            )
            .fill(DesignSystem.LiquidTabBar.backgroundColor)
            
            // 边框效果
            LiquidTabBarShape(
                bubbleCenter: bubbleCenter,
                transitionProgress: transitionProgress
            )
            .stroke(
                DesignSystem.LiquidTabBar.borderColor,
                lineWidth: DesignSystem.LiquidTabBar.borderWidth
            )
        }
        .shadow(
            color: Color.black.opacity(0.08),
            radius: 8,
            x: 0,
            y: -4
        )
    }
}

// MARK: - Preview
#Preview {
    VStack {
        Spacer()
        
        LiquidTabBarBackground(
            bubbleCenter: 150,
            transitionProgress: 1.0
        )
        .frame(height: DesignSystem.LiquidTabBar.height)
    }
    .background(Color.gray.opacity(0.2))
} 