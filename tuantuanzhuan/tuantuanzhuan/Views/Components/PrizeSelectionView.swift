//
//  PrizeSelectionView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 奖品选择弹窗组件
 * 复用奖品库界面逻辑，但修改为选择模式
 * 用于抽奖道具配置中的奖品导入功能
 */
struct PrizeSelectionView: View {
    
    @Binding var isPresented: Bool
    let onPrizeSelected: (String) -> Void
    
    @State private var prizes: [PrizeTemplate] = []
    @State private var animationTrigger = false
    
    // MARK: - CoreData Manager
    private let coreDataManager = CoreDataManager.shared
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        closeView()
                    }
                    .transition(.opacity)
                
                // 选择对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Text("选择奖品")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            Spacer()
                            
                            // 关闭按钮
                            Button(action: {
                                closeView()
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.system(size: 24))
                                    .foregroundColor(Color.gray.opacity(0.6))
                            }
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 24)
                        .padding(.bottom, 16)
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                            .padding(.horizontal, 24)
                        
                        // 奖品列表内容
                        if prizes.isEmpty {
                            // 空状态
                            VStack(spacing: 16) {
                                Image(systemName: "gift")
                                    .font(.system(size: 48))
                                    .foregroundColor(Color(hex: "#74c07f").opacity(0.4))
                                
                                Text("暂无奖品可选择\n请先在设置中配置奖品库")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                                    .multilineTextAlignment(.center)
                                    .lineLimit(nil)
                            }
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .padding(.vertical, 40)
                        } else {
                            // 奖品列表
                            ScrollView(.vertical, showsIndicators: true) {
                                LazyVStack(spacing: 12) {
                                    ForEach(groupedPrizes.keys.sorted(), id: \.self) { type in
                                        if let typePrizes = groupedPrizes[type], !typePrizes.isEmpty {
                                            // 类型分组标题
                                            HStack {
                                                Text(type)
                                                    .font(.system(size: 16, weight: .semibold))
                                                    .foregroundColor(DesignSystem.Colors.textPrimary)
                                                
                                                Rectangle()
                                                    .fill(Color(hex: "#74c07f").opacity(0.3))
                                                    .frame(height: 1)
                                            }
                                            .padding(.horizontal, 24)
                                            .padding(.top, type == groupedPrizes.keys.sorted().first ? 16 : 24)
                                            
                                            // 该类型的奖品列表
                                            ForEach(typePrizes, id: \.self) { prize in
                                                PrizeSelectionRow(
                                                    prize: prize,
                                                    onSelect: {
                                                        selectPrize(prize)
                                                    }
                                                )
                                                .padding(.horizontal, 24)
                                            }
                                        }
                                    }
                                }
                                .padding(.bottom, 20)
                            }
                            .frame(maxHeight: geometry.size.height * 0.6)
                        }
                        
                        // 底部空白
                        Spacer(minLength: 20)
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 420))
                    .frame(maxHeight: geometry.size.height * 0.75)
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color(hex: "#74c07f").opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            print("🎁 PrizeSelectionView: onAppear 触发")
            loadPrizes()
        }
        .onChange(of: isPresented) { newValue in
            print("🎁 PrizeSelectionView: isPresented 变化为 \(newValue)")
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
                DispatchQueue.main.async {
                    loadPrizes()
                }
            } else {
                animationTrigger = false
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 按类型分组的奖品
     */
    private var groupedPrizes: [String: [PrizeTemplate]] {
        Dictionary(grouping: prizes) { prize in
            prize.type ?? "虚拟"
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 加载奖品列表
     */
    private func loadPrizes() {
        let allPrizes = coreDataManager.getAllPrizeTemplates()
        print("🎁 PrizeSelectionView: 加载奖品数量 = \(allPrizes.count)")
        
        for (index, prize) in allPrizes.enumerated() {
            print("🎁 奖品 \(index + 1): \(prize.name ?? "无名"), 类型: \(prize.type ?? "无类型"), 积分: \(prize.cost)")
        }
        
        prizes = allPrizes.sorted { prize1, prize2 in
            // 首先按类型排序
            let type1 = prize1.type ?? "虚拟"
            let type2 = prize2.type ?? "虚拟"
            if type1 != type2 {
                return type1 < type2
            }
            // 同类型内按积分成本排序
            return prize1.cost < prize2.cost
        }
        
        print("🎁 PrizeSelectionView: 排序后奖品数量 = \(prizes.count)")
    }
    
    /**
     * 关闭视图
     */
    private func closeView() {
        print("🎁 PrizeSelectionView: 开始关闭视图")

        // 先执行关闭动画
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            animationTrigger = false
        }

        // 延迟设置isPresented，确保动画完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.isPresented = false
            print("🎁 PrizeSelectionView: 视图已关闭")
        }
    }
    
    /**
     * 选择奖品
     */
    private func selectPrize(_ prize: PrizeTemplate) {
        let prizeName = prize.name ?? ""
        print("🎁 PrizeSelectionView: 选择了奖品 - \(prizeName)")
        onPrizeSelected(prizeName)
        closeView()
    }
}

/**
 * 奖品选择行组件
 */
private struct PrizeSelectionRow: View {
    
    let prize: PrizeTemplate
    let onSelect: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 16) {
                // 奖品图标
                ZStack {
                    Circle()
                        .fill(Color(hex: "#74c07f").opacity(0.1))
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: prize.prizeType == .physical ? "gift.fill" : "star.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(Color(hex: "#74c07f"))
                }
                
                // 奖品信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(prize.name ?? "")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .lineLimit(1)
                    
                    HStack(spacing: 12) {
                        Text(prize.type ?? "虚拟")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(prize.prizeType == .physical ? Color.orange : Color.blue)
                            )
                        
                        Text("\(prize.cost) 积分")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
                
                Spacer()
                
                // 选择图标
                Image(systemName: "checkmark.circle")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(Color(hex: "#74c07f"))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(hex: "#74c07f").opacity(0.2), lineWidth: 1)
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Preview

#if DEBUG
struct PrizeSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        PrizeSelectionView(
            isPresented: .constant(true),
            onPrizeSelected: { prizeName in
                print("Selected prize: \(prizeName)")
            }
        )
    }
}
#endif 