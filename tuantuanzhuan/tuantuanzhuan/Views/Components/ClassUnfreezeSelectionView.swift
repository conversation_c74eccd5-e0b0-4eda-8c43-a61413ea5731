//
//  ClassUnfreezeSelectionView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 班级解冻选择视图
 * 用于会员升级时选择要解冻的班级
 */
struct ClassUnfreezeSelectionView: View {
    // MARK: - Properties
    let frozenClasses: [SchoolClass]
    let availableUnfreezeCount: Int
    let onConfirm: ([String]) -> Void
    let onCancel: () -> Void
    let subscriptionLevelName: String
    
    // MARK: - Environment
    @EnvironmentObject private var coreDataManager: CoreDataManager
    
    // MARK: - State
    @State private var selectedClassIds: Set<String> = []
    @State private var isProcessing: Bool = false
    @State private var animationTrigger = false
    
    // MARK: - Computed Properties
    private var submitButtonEnabled: Bool {
        return !selectedClassIds.isEmpty && !isProcessing
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {} // 防止点击背景关闭弹窗
            
            // 主内容
            VStack(spacing: 0) {
                // 标题区域
                headerSection
                
                // 说明文本
                explanationSection
                
                // 班级列表
                classListSection
                
                // 分隔线
                Rectangle()
                    .fill(Color(hex: "#edf5d9"))
                    .frame(height: 1)
                    .padding(.horizontal, 20)
                
                // 底部按钮
                buttonSection
            }
            .frame(width: UIScreen.main.bounds.width * 0.85)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1.5)
            )
            .shadow(color: Color.black.opacity(0.15), radius: 15, x: 0, y: 8)
            .padding(.horizontal, 20)
            .scaleEffect(animationTrigger ? 1.0 : 0.95)
            .opacity(animationTrigger ? 1.0 : 0.0)
            .onAppear {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
                setupDefaultSelection()
            }
        }
    }
    
    // MARK: - View Components
    
    /// 标题区域
    private var headerSection: some View {
        VStack(spacing: 4) {
            Text("class_unfreeze.title".localized)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Text(String(format: "class_unfreeze.subtitle".localized, subscriptionLevelName))
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(.top, 24)
        .padding(.bottom, 16)
    }
    
    /// 说明文本区域
    private var explanationSection: some View {
        VStack(spacing: 8) {
            Text(String(format: "class_unfreeze.instruction".localized, availableUnfreezeCount))
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Text("class_unfreeze.description".localized)
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .padding(.bottom, 16)
    }
    
    /// 班级列表区域
    private var classListSection: some View {
        ScrollView {
            VStack(spacing: 8) {
                ForEach(frozenClasses, id: \.id) { schoolClass in
                    classSelectionRow(for: schoolClass)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .frame(maxHeight: 300)
        .background(Color(hex: "#f8f8f8"))
    }
    
    /// 单个班级选择行
    private func classSelectionRow(for schoolClass: SchoolClass) -> some View {
        let classId = schoolClass.id?.uuidString ?? ""
        let isSelected = selectedClassIds.contains(classId)
        
        return Button(action: {
            toggleClassSelection(classId)
        }) {
            HStack(spacing: 16) {
                // 选择框
                ZStack {
                    RoundedRectangle(cornerRadius: 4)
                        .stroke(isSelected ? Color(hex: "#a9d051") : Color.gray.opacity(0.5), lineWidth: 2)
                        .frame(width: 20, height: 20)
                    
                    if isSelected {
                        Image(systemName: "checkmark")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(Color(hex: "#a9d051"))
                    }
                }
                
                // 班级信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(schoolClass.name ?? "未命名班级")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text(String(format: "class_freeze.student_count_format".localized, 
                                schoolClass.studentCount, formatDate(schoolClass.createdAt)))
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                // 状态标签
                Text(isSelected ? "class_unfreeze.status.will_unfreeze".localized : "class_unfreeze.status.frozen".localized)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(isSelected ? Color(hex: "#74c07f") : Color(hex: "#ff9500"))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(isSelected ? Color(hex: "#74c07f").opacity(0.1) : Color(hex: "#ff9500").opacity(0.1))
                    )
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color.white)
            .cornerRadius(8)
            .shadow(color: Color.black.opacity(0.03), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(selectedClassIds.count >= availableUnfreezeCount && !selectedClassIds.contains(classId))
    }
    
    /// 底部按钮区域
    private var buttonSection: some View {
        // 确认按钮
        Button(action: {
            confirmSelection()
        }) {
            Text("class_unfreeze.confirm_button".localized)
                .fontWeight(.medium)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(submitButtonEnabled ? Color(hex: "#a9d051") : Color.gray.opacity(0.3))
                .foregroundColor(.white)
                .cornerRadius(12)
        }
        .disabled(!submitButtonEnabled)
        .padding(.horizontal, 24)
        .padding(.vertical, 20)
    }
    
    // MARK: - Helper Methods
    
    /// 设置默认选择
    private func setupDefaultSelection() {
        // 默认不选择班级
        selectedClassIds = []
    }
    
    /// 切换班级选择状态
    private func toggleClassSelection(_ classId: String) {
        // 如果已经选中，取消选中
        if selectedClassIds.contains(classId) {
            selectedClassIds.remove(classId)
        } else {
            // 如果未选中且未达到上限，则选中
            if selectedClassIds.count < availableUnfreezeCount {
                selectedClassIds.insert(classId)
            }
        }
    }
    
    /// 确认选择
    private func confirmSelection() {
        isProcessing = true
        onConfirm(Array(selectedClassIds))
        isProcessing = false
    }
    
    /// 格式化日期
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "未知日期" }
        
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy/MM/dd"
        return formatter.string(from: date)
    }
}

// MARK: - Preview
#if DEBUG
@available(iOS 17.0, *)
#Preview {
    // 创建预览数据
    let viewContext = PersistenceController.preview.container.viewContext
    
    let user = User(context: viewContext)
    user.id = UUID()
    user.nickname = "测试用户"
    
    let class1 = SchoolClass.create(name: "三年级一班", owner: user, in: viewContext)
    let class2 = SchoolClass.create(name: "三年级二班", owner: user, in: viewContext)
    let class3 = SchoolClass.create(name: "三年级三班", owner: user, in: viewContext)
    
    class1.status = "frozen"
    class2.status = "frozen"
    class3.status = "frozen"
    
    // 添加学生
    for i in 1...5 {
        let student = Student(context: viewContext)
        student.id = UUID()
        student.name = "学生\(i)"
        student.studentNumber = "\(i)"
        student.gender = i % 2 == 0 ? "男" : "女"
        student.schoolClass = class1
    }
    
    for i in 6...12 {
        let student = Student(context: viewContext)
        student.id = UUID()
        student.name = "学生\(i)"
        student.studentNumber = "\(i)"
        student.gender = i % 2 == 0 ? "男" : "女"
        student.schoolClass = class2
    }
    
    return ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        ClassUnfreezeSelectionView(
            frozenClasses: [class1, class2, class3],
            availableUnfreezeCount: 1,
            onConfirm: { selectedIds in
                print("选择解冻: \(selectedIds)")
            },
            onCancel: {
                print("取消解冻")
            },
            subscriptionLevelName: "初级会员"
        )
        .environmentObject(CoreDataManager.shared)
    }
}
#endif 