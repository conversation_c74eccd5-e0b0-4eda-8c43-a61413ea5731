//
//  ConfiguredRulesListView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/16.
//

import SwiftUI

/**
 * 已配置规则列表视图组件
 * 显示用户已配置的加分/扣分规则
 */
struct ConfiguredRulesListView: View {
    
    @Binding var isPresented: Bool
    let ruleType: RuleTemplate.RuleType
    @State private var rules: [RuleTemplate] = []
    @State private var animationTrigger = false
    
    // MARK: - CoreData Manager
    private let coreDataManager = CoreDataManager.shared
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 规则列表对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Text(ruleType == .add ? 
                                "Configured add points rules".localized : 
                                "Configured deduct points rules".localized)
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            Spacer()
                            
                            // 关闭按钮
                            Button(action: {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    isPresented = false
                                }
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.system(size: 24))
                                    .foregroundColor(Color.gray.opacity(0.6))
                            }
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 24)
                        .padding(.bottom, 16)
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                            .padding(.horizontal, 24)
                        
                        // 规则列表内容区域
                        if rules.isEmpty {
                            // 空状态
                            VStack(spacing: 16) {
                                Image(systemName: ruleType == .add ? "plus.circle" : "minus.circle")
                                    .font(.system(size: 50))
                                    .foregroundColor((ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c")).opacity(0.6))
                                
                                Text(ruleType == .add ? 
                                    "No configured add points rules yet\nGo create the first one".localized : 
                                    "No configured deduct points rules yet\nGo create the first one".localized)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                                    .multilineTextAlignment(.center)
                                    .lineLimit(2)
                            }
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .frame(minHeight: 200)
                        } else {
                            // 规则列表
                            List {
                                ForEach(rules, id: \.id) { rule in
                                    ConfiguredRuleCard(rule: rule, ruleType: ruleType)
                                        .listRowBackground(Color.clear)
                                        .listRowSeparator(.hidden)
                                        .listRowInsets(EdgeInsets(top: 6, leading: 24, bottom: 6, trailing: 24))
                                        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                                            Button(action: {
                                                deleteRule(rule)
                                            }) {
                                                Image(systemName: "trash")
                                                    .foregroundColor(.white)
                                            }
                                            .tint(.red)
                                        }
                                }
                            }
                            .listStyle(PlainListStyle())
                            .frame(maxHeight: geometry.size.height * 0.6)
                            .padding(.vertical, 14)
                        }
                        
                        // 底部按钮区域
                        HStack {
                            Button(action: {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    isPresented = false
                                }
                            }) {
                                Text("common.button.close".localized)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(DesignSystem.Colors.textPrimary)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 44)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(12)
                            }
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 20)
                        .padding(.bottom, 28)
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 420))
                    .frame(maxHeight: geometry.size.height * 0.8)
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke((ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c")).opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            loadRules()
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                loadRules()
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 加载规则数据
     */
    private func loadRules() {
        let allRules = coreDataManager.getAllRuleTemplates()
        rules = allRules.filter { rule in
            rule.type == ruleType.rawValue
        }
    }
    
    /**
     * 删除指定规则模板
     */
    private func deleteRule(_ rule: RuleTemplate) {
        // 使用CoreDataManager删除规则
        coreDataManager.delete(rule)
        // 重新加载规则列表
        loadRules()
    }
}

/**
 * 已配置规则卡片组件
 */
struct ConfiguredRuleCard: View {
    
    let rule: RuleTemplate
    let ruleType: RuleTemplate.RuleType
    
    var body: some View {
        HStack(spacing: 16) {
            // 左侧图标
            ZStack {
                Circle()
                    .fill((ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c")).opacity(0.1))
                    .frame(width: 50, height: 50)
                
                Image(systemName: ruleType == .add ? "plus.circle.fill" : "minus.circle.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c"))
            }
            
            // 规则信息
            VStack(alignment: .leading, spacing: 4) {
                Text(rule.name ?? "")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .multilineTextAlignment(.leading)
                
                Text("\(ruleType == .add ? "+" : "-")\(rule.value)" + " " + "rule_config.configured_list.points_unit".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c"))
            }
            
            Spacer()
            
            // 右侧标签
            Text(ruleType == .add ? "rule_config.configured_list.add_label".localized : "rule_config.configured_list.deduct_label".localized)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c"))
                .cornerRadius(12)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke((ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c")).opacity(0.2), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        ConfiguredRulesListView(
            isPresented: .constant(true),
            ruleType: .add
        )
    }
} 