//
//  RuleConfigFormView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/16.
//

import SwiftUI

/**
 * 规则配置表单组件
 * 支持单个和批量规则信息输入，可动态添加表单行
 */
struct RuleConfigFormView: View {
    
    @Binding var isPresented: Bool
    let ruleType: RuleTemplate.RuleType
    @State private var ruleForms: [RuleFormData] = []
    @State private var isSubmitting: Bool = false
    @State private var validationErrors: [String] = []
    @State private var animationTrigger = false
    @State private var showConfiguredRules = false
    
    let onSubmit: ([RuleFormData]) -> Void
    let onCancel: () -> Void
    
    // MARK: - CoreData Manager
    private let coreDataManager = CoreDataManager.shared
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    // 注释掉点击外部区域关闭弹窗的手势，防止误操作
                    // .onTapGesture {
                    //     if !isSubmitting {
                    //         onCancel()
                    //     }
                    // }
                    .transition(.opacity)
                
                // 表单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Text(ruleType == .add ? "Rules of adding points".localized : "Rules of deducting points".localized)
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            Spacer()
                            
                            // 添加规则按钮
                            Button(action: addNewRuleForm) {
                                Image(systemName: "plus.circle.fill")
                                    .font(.system(size: 26))
                                    .foregroundColor(ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c"))
                            }
                            .disabled(isSubmitting)
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 24)
                        .padding(.bottom, 16)
                        .onTapGesture {
                            // 点击标题栏区域关闭键盘
                            dismissKeyboard()
                        }
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                            .padding(.horizontal, 24)
                        
                        // 表单内容区域
                        ScrollView(.vertical, showsIndicators: true) {
                            VStack(spacing: 16) {
                                ForEach(ruleForms.indices, id: \.self) { index in
                                    RuleFormRow(
                                        rule: $ruleForms[index],
                                        index: index,
                                        ruleType: ruleType,
                                        canDelete: ruleForms.count > 1,
                                        onDelete: {
                                            removeRuleForm(at: index)
                                        }
                                    )
                                    .disabled(isSubmitting)
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 20)
                        }
                        .frame(maxHeight: geometry.size.height * 0.5)
                        .contentShape(Rectangle()) // 确保整个滚动区域都能响应手势
                        .onTapGesture {
                            // 点击表单内容区域关闭键盘
                            dismissKeyboard()
                        }
                        
                        // 错误提示区域
                        if !validationErrors.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                ForEach(validationErrors, id: \.self) { error in
                                    Text(error)
                                        .font(.system(size: 13, weight: .regular))
                                        .foregroundColor(.red)
                                        .multilineTextAlignment(.leading)
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(Color.red.opacity(0.05))
                            .onTapGesture {
                                // 点击错误提示区域关闭键盘
                                dismissKeyboard()
                            }
                        }
                        
                        // 已配置规则入口
                        HStack {
                            Button(action: {
                                showConfiguredRules = true
                            }) {
                                Text("rule_config.form.configured_rules".localized)
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c"))
                                    .underline()
                            }
                            .disabled(isSubmitting)
                            
                            Spacer()
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 16)
                        .onTapGesture {
                            // 点击已配置规则入口区域关闭键盘（Spacer区域）
                            dismissKeyboard()
                        }
                        
                        // 底部按钮区域
                        HStack(spacing: 16) {
                            // 取消按钮
                            Button(action: {
                                if !isSubmitting {
                                    dismissKeyboard()
                                    onCancel()
                                }
                            }) {
                                Text("common.button.cancel".localized)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(Color.gray)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 44)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(12)
                            }
                            .disabled(isSubmitting)
                            
                            // 添加规则按钮
                            Button(action: {
                                dismissKeyboard()
                                submitRules()
                            }) {
                                HStack(spacing: 8) {
                                    if isSubmitting {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    }
                                    
                                    Text(isSubmitting ? "Addting".localized : "Add".localized)
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.white)
                                }
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(
                                    LinearGradient(
                                        gradient: Gradient(colors: ruleType == .add ? [
                                            Color(hex: "#74c07f"),
                                            Color(hex: "#a9d051")
                                        ] : [
                                            Color(hex: "#e74c3c"),
                                            Color(hex: "#c0392b")
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .cornerRadius(12)
                                .opacity(isSubmitting ? 0.8 : 1.0)
                            }
                            .disabled(isSubmitting || ruleForms.isEmpty)
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 20)
                        .padding(.bottom, 28)
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 420))
                    .frame(maxHeight: geometry.size.height * 0.8)
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke((ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c")).opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .overlay(
            // 已配置规则列表弹窗
            ConfiguredRulesListView(
                isPresented: $showConfiguredRules,
                ruleType: ruleType
            )
        )
        .onAppear {
            initializeForm()
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                print("📱 [规则表单] 表单显示状态改变: \(newValue)")
                print("🔍 [规则表单] 重新检查类型一致性")
                print("📋 [规则表单] 当前ruleType: \(ruleType.rawValue)")
                
                // 强制检查所有表单项的类型一致性
                var hasTypeMismatch = false
                for (index, form) in ruleForms.enumerated() {
                    if form.type != ruleType.rawValue {
                        print("⚠️ [规则表单] 发现类型不匹配 - 表单\(index+1): \(form.type) != \(ruleType.rawValue)")
                        hasTypeMismatch = true
                    }
                }
                
                if hasTypeMismatch {
                    print("🔧 [规则表单] 修复类型不匹配问题")
                    initializeForm() // 重新初始化表单
                }
                
                // 显示动画
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
                // 重置状态
                resetForm()
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 初始化表单
     */
    private func initializeForm() {
        print("🔧 [规则表单] 初始化表单")
        print("📋 [规则表单] 当前ruleType: \(ruleType)")
        print("📋 [规则表单] ruleType.rawValue: \(ruleType.rawValue)")
        
        // 强制重新初始化表单，确保使用正确的类型
        let newForm = RuleFormData(type: ruleType.rawValue)
        print("✅ [规则表单] 强制创建新表单，类型: \(newForm.type)")
        
        // 验证类型是否正确设置
        if newForm.type == ruleType.rawValue {
            print("✅ [规则表单] 类型验证通过: \(newForm.type)")
        } else {
            print("⚠️ [规则表单] 类型验证失败: 期望\(ruleType.rawValue), 实际\(newForm.type)")
        }
        
        ruleForms = [newForm]
        print("📊 [规则表单] 初始化完成，表单数量: \(ruleForms.count)")
    }
    
    /**
     * 添加新的规则表单行
     */
    private func addNewRuleForm() {
        print("➕ [规则表单] 添加新规则表单行")
        print("📋 [规则表单] 当前ruleType: \(ruleType.rawValue)")
        
        let newForm = RuleFormData(type: ruleType.rawValue)
        print("✅ [规则表单] 创建新表单行，类型: \(newForm.type)")
        
        let _ = withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            ruleForms.append(newForm)
        }
        
        print("📊 [规则表单] 当前表单总数: \(ruleForms.count)")
    }
    
    /**
     * 移除指定索引的规则表单行
     */
    private func removeRuleForm(at index: Int) {
        if ruleForms.count > 1 {
            let _ = withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                ruleForms.remove(at: index)
            }
        }
    }
    
    /**
     * 提交规则数据
     */
    private func submitRules() {
        print("🚀 [规则表单] 开始提交规则数据")
        print("📋 [规则表单] 当前ruleType: \(ruleType.rawValue)")
        print("📊 [规则表单] 待提交表单数量: \(ruleForms.count)")
        
        // 清除之前的错误
        validationErrors.removeAll()
        
        // 检查所有表单的类型一致性
        for (index, form) in ruleForms.enumerated() {
            print("🔍 [规则表单] 检查表单\(index+1):")
            print("  - 名称: \(form.name)")
            print("  - 分值: \(form.value)")
            print("  - 类型: \(form.type)")
            print("  - 期望类型: \(ruleType.rawValue)")
            
            if form.type != ruleType.rawValue {
                print("⚠️ [规则表单] 发现类型不匹配的表单！")
                print("   表单类型: \(form.type), 期望类型: \(ruleType.rawValue)")
            }
        }
        
        // 获取现有规则模板用于重复检查
        let existingRules = coreDataManager.getAllRuleTemplates()
        
        // 批量验证
        let validationResult = ruleForms.validateBatch(existingRules: existingRules)
        
        if validationResult.isValid {
            print("✅ [规则表单] 验证通过，准备提交")
            isSubmitting = true
            
            // 延迟提交，模拟处理时间
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                print("📤 [规则表单] 调用onSubmit回调")
                onSubmit(validationResult.validRules)
                isSubmitting = false
            }
        } else {
            print("❌ [规则表单] 验证失败:")
            for error in validationResult.errorMessages {
                print("  - \(error)")
            }
            
            // 显示验证错误
            let _ = withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                validationErrors = validationResult.errorMessages
            }
        }
    }
    
    /**
     * 重置表单
     */
    private func resetForm() {
        print("🔄 [规则表单] 重置表单")
        print("📋 [规则表单] 重置时ruleType: \(ruleType.rawValue)")
        
        let newForm = RuleFormData(type: ruleType.rawValue)
        print("✅ [规则表单] 重置后创建表单，类型: \(newForm.type)")
        
        ruleForms = [newForm]
        validationErrors.removeAll()
        isSubmitting = false
        
        print("📊 [规则表单] 重置完成，表单数量: \(ruleForms.count)")
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

/**
 * 规则表单行组件
 */
struct RuleFormRow: View {
    
    @Binding var rule: RuleFormData
    let index: Int
    let ruleType: RuleTemplate.RuleType
    let canDelete: Bool
    let onDelete: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            // 行标题和删除按钮
            HStack {
                Text("rule %@".localized(with: "\(index + 1)"))
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                if canDelete {
                    Button(action: onDelete) {
                        Image(systemName: "minus.circle.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.red.opacity(0.7))
                    }
                }
            }
            
            // 表单字段
            VStack(spacing: 12) {
                // 规则名称输入
                RuleFormField(
                    title: "rule name".localized,
                    text: $rule.name,
                    placeholder: ruleType == .add ? 
                        "e.g. Active in class".localized : 
                        "e.g. Being late".localized
                )
                
                // 分值输入
                RuleFormField(
                    title: "Points".localized,
                    text: $rule.value,
                    placeholder: "5".localized,
                    keyboardType: .numberPad
                )
            }
        }
        .padding(16)
        .background((ruleType == .add ? Color(hex: "#f8ffe5") : Color(hex: "#ffeef0")).opacity(0.5))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke((ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c")).opacity(0.2), lineWidth: 1)
        )
    }
}

/**
 * 规则表单字段组件
 */
struct RuleFormField: View {
    
    let title: String
    @Binding var text: String
    let placeholder: String
    var keyboardType: UIKeyboardType = .default
    
    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            TextField(placeholder, text: $text)
                .keyboardType(keyboardType)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        RuleConfigFormView(
            isPresented: .constant(true),
            ruleType: .add,
            onSubmit: { rules in
                print("提交规则数据: \(rules)")
            },
            onCancel: {
                print("取消配置")
            }
        )
    }
} 