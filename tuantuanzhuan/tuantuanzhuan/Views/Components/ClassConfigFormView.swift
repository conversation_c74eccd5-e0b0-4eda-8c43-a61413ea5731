//
//  ClassConfigFormView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 班级配置表单组件
 * 支持统一管理班级的加分规则、扣分规则和奖品配置
 */
struct ClassConfigFormView: View {
    
    @Binding var isPresented: Bool
    let schoolClass: SchoolClass
    let userSubscriptionLevel: Subscription.Level
    @State private var formData = ClassConfigFormData()
    @State private var isSubmitting: Bool = false
    @State private var validationErrors: [String] = []
    @State private var animationTrigger = false
    @State private var showImportView = false
    
    let onSubmit: (ClassConfigFormData) -> Void
    let onCancel: () -> Void
    
    // MARK: - CoreData Manager
    private let coreDataManager = CoreDataManager.shared
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    // 注释掉点击外部区域关闭弹窗的手势，防止误操作
                    // .onTapGesture {
                    //     if !isSubmitting {
                    //         onCancel()
                    //     }
                    // }
                    .transition(.opacity)
                
                // 表单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 顶部标题栏
                        headerView
                        
                        // Tab切换区域
                        tabSelectorView
                        
                        // 表单内容区域
                        ScrollView(.vertical, showsIndicators: true) {
                            VStack(spacing: 16) {
                                // 根据当前Tab显示对应表单
                                switch formData.configType {
                                case .addRules:
                                    ruleFormsView(ruleType: .add, forms: $formData.addRuleForms)
                                case .deductRules:
                                    ruleFormsView(ruleType: .deduct, forms: $formData.deductRuleForms)
                                case .prizes:
                                    prizeFormsView(forms: $formData.prizeForms)
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 20)
                        }
                        .frame(maxHeight: geometry.size.height * 0.5)
                        .contentShape(Rectangle()) // 确保整个滚动区域都能响应手势
                        .onTapGesture {
                            // 点击表单内容区域关闭键盘
                            dismissKeyboard()
                        }
                        
                        // 错误提示区域
                        if !validationErrors.isEmpty {
                            errorView
                        }
                        
                        // 导入功能入口
                        importButtonView
                        
                        // 底部操作按钮
                        bottomActionView
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 450))
                    .frame(maxHeight: geometry.size.height * 0.85)
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color(hex: "#74c07f").opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            setupInitialState()
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                animationTrigger = true
                // 重置提交状态，修复按钮一直显示"保存中…"的问题
                isSubmitting = false
                validationErrors = []
            } else {
                animationTrigger = false
            }
        }
        .onChange(of: formData.configType) { _ in
            // 切换标签时清除错误信息
            validationErrors = []
        }
        .sheet(isPresented: $showImportView) {
            importSelectionView
        }
    }
    
    // MARK: - 头部视图
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Class Configuration".localized)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text(schoolClass.name ?? "")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            // 关闭按钮
            Button(action: {
                if !isSubmitting {
                    dismissKeyboard()
                    onCancel()
                }
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color.gray)
            }
            .disabled(isSubmitting)
        }
        .padding(.horizontal, 24)
        .padding(.top, 24)
        .padding(.bottom, 16)
        .onTapGesture {
            // 点击标题栏区域关闭键盘
            dismissKeyboard()
        }
    }
    
    // MARK: - Tab选择器视图
    private var tabSelectorView: some View {
        VStack(spacing: 0) {
            Picker("配置类型", selection: $formData.configType) {
                ForEach(ClassConfigFormData.ConfigType.allCases, id: \.self) { type in
                    Text(type.displayName)
                        .tag(type)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding(.horizontal, 24)
            .disabled(isSubmitting)
            
            // 分隔线
            Rectangle()
                .fill(Color(hex: "#edf5d9"))
                .frame(height: 1)
                .padding(.horizontal, 24)
                .padding(.top, 16)
        }
    }
    
    // MARK: - 规则表单视图
    private func ruleFormsView(ruleType: RuleTemplate.RuleType, forms: Binding<[RuleFormData]>) -> some View {
        VStack(spacing: 16) {
            // 添加按钮
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    let title = ruleType == .add ? "Common rules for adding points".localized : "Common rules for deduting points".localized
                    let maxCount = formData.maxFormsCount
                    Text("\(title) (\("rule_config.max_count_format".localized(with: "\(maxCount)")))")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                
                Spacer()
                
                if formData.canAddMoreForms {
                    Button(action: {
                        formData.addNewForm()
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(ruleType == .add ? Color(hex: "#74c07f") : Color(hex: "#e74c3c"))
                    }
                    .disabled(isSubmitting)
                }
            }
            
            // 表单列表
            ForEach(forms.wrappedValue.indices, id: \.self) { index in
                // 简化的规则表单行
                HStack(spacing: 12) {
                    // 行号
                    Text("\(index + 1)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .frame(width: 24, height: 24)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
                    
                    // 规则名称输入框
                    TextField("rule name".localized, text: forms[index].name)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .font(.system(size: 14))
                    
                    // 分值输入框
                    TextField("Points".localized, text: forms[index].value)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .font(.system(size: 14))
                        .frame(width: 60)
                        .keyboardType(.numberPad)
                    
                    // 删除按钮
                    if formData.canDeleteForms {
                        Button(action: {
                            formData.removeForm(at: index)
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(.red)
                        }
                    }
                }
                .padding(.vertical, 4)
                .disabled(isSubmitting)
            }
        }
    }
    
    // MARK: - 奖品表单视图
    private func prizeFormsView(forms: Binding<[PrizeFormData]>) -> some View {
        VStack(spacing: 16) {
            // 添加按钮
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    let title = "Class Prize Configuration".localized
                    let maxCount = formData.maxFormsCount
                    Text("\(title) (\("rule_config.max_count_format".localized(with: "\(maxCount)")))")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                
                Spacer()
                
                if formData.canAddMoreForms {
                    Button(action: {
                        formData.addNewForm()
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(Color(hex: "#74c07f"))
                    }
                    .disabled(isSubmitting)
                }
            }
            
            // 表单列表
            ForEach(forms.wrappedValue.indices, id: \.self) { index in
                // 简化的奖品表单行
                HStack(spacing: 12) {
                    // 行号
                    Text("\(index + 1)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .frame(width: 24, height: 24)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
                    
                    // 奖品名称输入框
                    TextField("Prize name".localized, text: forms[index].name)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .font(.system(size: 14))
                    
                    // 积分成本输入框
                    TextField("Cost".localized, text: forms[index].cost)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .font(.system(size: 14))
                        .frame(width: 60)
                        .keyboardType(.numberPad)
                    
                    // 删除按钮
                    if formData.canDeleteForms {
                        Button(action: {
                            formData.removeForm(at: index)
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(.red)
                        }
                    }
                }
                .padding(.vertical, 4)
                .disabled(isSubmitting)
            }
        }
    }
    
    // MARK: - 错误提示视图
    private var errorView: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(validationErrors, id: \.self) { error in
                Text(error)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(.red)
                    .multilineTextAlignment(.leading)
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 12)
        .background(Color.red.opacity(0.05))
    }
    
    // MARK: - 导入按钮视图
    private var importButtonView: some View {
        HStack {
            Button(action: {
                showImportView = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "square.and.arrow.down")
                        .font(.system(size: 14, weight: .medium))
                    Text("Import from the library".localized)
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(Color(hex: "#74c07f"))
            }
            .disabled(isSubmitting)
            
            Spacer()
        }
        .padding(.horizontal, 24)
        .padding(.top, 16)
    }
    
    // MARK: - 底部操作视图
    private var bottomActionView: some View {
        HStack(spacing: 16) {
            // 取消按钮
            Button(action: {
                if !isSubmitting {
                    dismissKeyboard()
                    onCancel()
                }
            }) {
                Text("common.button.cancel".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
            }
            .disabled(isSubmitting)
            
            // 保存按钮
            Button(action: {
                dismissKeyboard()
                submitConfiguration()
            }) {
                HStack(spacing: 8) {
                    if isSubmitting {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    }
                    
                    Text(isSubmitting ? "Saving...".localized : "Save".localized)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 44)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#74c07f"),
                            Color(hex: "#a9d051")
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(12)
                .opacity(isSubmitting ? 0.8 : 1.0)
            }
            .disabled(isSubmitting || !formData.hasValidData)
        }
        .padding(.horizontal, 24)
        .padding(.top, 20)
        .padding(.bottom, 28)
    }
    
    // MARK: - 导入选择视图
    private var importSelectionView: some View {
        NavigationView {
            VStack {
                List {
                    switch formData.configType {
                    case .addRules:
                        let templates = coreDataManager.getAllRuleTemplates().filter { $0.type == "add" }
                        if templates.isEmpty {
                            Text("暂无可导入的加分规则模板")
                                .foregroundColor(.gray)
                                .frame(maxWidth: .infinity, alignment: .center)
                        } else {
                            ForEach(templates, id: \.id) { template in
                                let isAlreadyAdded = isTemplateAlreadyAdded(template, in: formData.addRuleForms)
                                Button(action: {
                                    if !isAlreadyAdded {
                                        importRuleTemplate(template)
                                        showImportView = false
                                    }
                                }) {
                                    HStack {
                                        VStack(alignment: .leading) {
                                            Text(template.name ?? "")
                                                .font(.system(size: 16, weight: .medium))
                                                .foregroundColor(isAlreadyAdded ? .gray : .primary)
                                            Text("\(template.value)分")
                                                .font(.system(size: 14))
                                                .foregroundColor(isAlreadyAdded ? .gray : .secondary)
                                        }
                                        Spacer()
                                        if isAlreadyAdded {
                                            Text("已添加")
                                                .font(.system(size: 12, weight: .medium))
                                                .foregroundColor(.gray)
                                                .padding(.horizontal, 8)
                                                .padding(.vertical, 4)
                                                .background(Color.gray.opacity(0.2))
                                                .cornerRadius(8)
                                        } else {
                                            Image(systemName: "plus.circle.fill")
                                                .foregroundColor(.green)
                                        }
                                    }
                                    .padding(.vertical, 4)
                                    .opacity(isAlreadyAdded ? 0.6 : 1.0)
                                }
                                .buttonStyle(PlainButtonStyle())
                                .disabled(isAlreadyAdded)
                            }
                        }
                    case .deductRules:
                        let templates = coreDataManager.getAllRuleTemplates().filter { $0.type == "deduct" }
                        if templates.isEmpty {
                            Text("暂无可导入的扣分规则模板")
                                .foregroundColor(.gray)
                                .frame(maxWidth: .infinity, alignment: .center)
                        } else {
                            ForEach(templates, id: \.id) { template in
                                let isAlreadyAdded = isTemplateAlreadyAdded(template, in: formData.deductRuleForms)
                                Button(action: {
                                    if !isAlreadyAdded {
                                        importRuleTemplate(template)
                                        showImportView = false
                                    }
                                }) {
                                    HStack {
                                        VStack(alignment: .leading) {
                                            Text(template.name ?? "")
                                                .font(.system(size: 16, weight: .medium))
                                                .foregroundColor(isAlreadyAdded ? .gray : .primary)
                                            Text("\(template.value)分")
                                                .font(.system(size: 14))
                                                .foregroundColor(isAlreadyAdded ? .gray : .secondary)
                                        }
                                        Spacer()
                                        if isAlreadyAdded {
                                            Text("已添加")
                                                .font(.system(size: 12, weight: .medium))
                                                .foregroundColor(.gray)
                                                .padding(.horizontal, 8)
                                                .padding(.vertical, 4)
                                                .background(Color.gray.opacity(0.2))
                                                .cornerRadius(8)
                                        } else {
                                            Image(systemName: "plus.circle.fill")
                                                .foregroundColor(.red)
                                        }
                                    }
                                    .padding(.vertical, 4)
                                    .opacity(isAlreadyAdded ? 0.6 : 1.0)
                                }
                                .buttonStyle(PlainButtonStyle())
                                .disabled(isAlreadyAdded)
                            }
                        }
                    case .prizes:
                        let templates = coreDataManager.getAllPrizeTemplates()
                        if templates.isEmpty {
                            Text("暂无可导入的奖品模板")
                                .foregroundColor(.gray)
                                .frame(maxWidth: .infinity, alignment: .center)
                        } else {
                            ForEach(templates, id: \.id) { template in
                                let isAlreadyAdded = isPrizeTemplateAlreadyAdded(template, in: formData.prizeForms)
                                Button(action: {
                                    if !isAlreadyAdded {
                                        importPrizeTemplate(template)
                                        showImportView = false
                                    }
                                }) {
                                    HStack {
                                        VStack(alignment: .leading) {
                                            Text(template.name ?? "")
                                                .font(.system(size: 16, weight: .medium))
                                                .foregroundColor(isAlreadyAdded ? .gray : .primary)
                                            Text("\(template.cost)积分 - \(template.type ?? "虚拟")")
                                                .font(.system(size: 14))
                                                .foregroundColor(isAlreadyAdded ? .gray : .secondary)
                                        }
                                        Spacer()
                                        if isAlreadyAdded {
                                            Text("已添加")
                                                .font(.system(size: 12, weight: .medium))
                                                .foregroundColor(.gray)
                                                .padding(.horizontal, 8)
                                                .padding(.vertical, 4)
                                                .background(Color.gray.opacity(0.2))
                                                .cornerRadius(8)
                                        } else {
                                            Image(systemName: "plus.circle.fill")
                                                .foregroundColor(.green)
                                        }
                                    }
                                    .padding(.vertical, 4)
                                    .opacity(isAlreadyAdded ? 0.6 : 1.0)
                                }
                                .buttonStyle(PlainButtonStyle())
                                .disabled(isAlreadyAdded)
                            }
                        }
                    }
                }
                .listStyle(PlainListStyle())
            }
            .navigationTitle("选择模板导入")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("完成") {
                    showImportView = false
                }
            )
        }
    }
    
    // MARK: - 私有方法
    
    /**
     * 设置初始状态
     */
    private func setupInitialState() {
        if isPresented {
            // 初始化表单数据并设置用户订阅级别
            formData = ClassConfigFormData(userSubscriptionLevel: userSubscriptionLevel)
            // 加载班级现有配置
            loadExistingConfiguration()
            animationTrigger = true
        }
    }
    
    /**
     * 加载班级现有配置
     */
    private func loadExistingConfiguration() {
        // 加载现有的常用规则
        let existingAddRules = schoolClass.frequentRules.filter { $0.ruleType == .add }
        let existingDeductRules = schoolClass.frequentRules.filter { $0.ruleType == .deduct }
        let existingPrizes = schoolClass.sortedPrizes
        
        // 转换为表单数据
        if !existingAddRules.isEmpty {
            formData.addRuleForms = existingAddRules.map { rule in
                var ruleForm = RuleFormData(type: "add")
                ruleForm.name = rule.name ?? ""
                ruleForm.value = String(rule.value)
                return ruleForm
            }
            // 补齐到最少1个表单
            while formData.addRuleForms.count < 1 {
                formData.addRuleForms.append(RuleFormData(type: "add"))
            }
        }
        
        if !existingDeductRules.isEmpty {
            formData.deductRuleForms = existingDeductRules.map { rule in
                var ruleForm = RuleFormData(type: "deduct")
                ruleForm.name = rule.name ?? ""
                ruleForm.value = String(rule.value)
                return ruleForm
            }
            // 补齐到最少1个表单
            while formData.deductRuleForms.count < 1 {
                formData.deductRuleForms.append(RuleFormData(type: "deduct"))
            }
        }
        
        if !existingPrizes.isEmpty {
            formData.prizeForms = existingPrizes.map { prize in
                var prizeForm = PrizeFormData()
                prizeForm.name = prize.name ?? ""
                prizeForm.cost = String(prize.cost)
                prizeForm.type = prize.type ?? "虚拟"
                return prizeForm
            }
            // 补齐到最少1个表单
            while formData.prizeForms.count < 1 {
                formData.prizeForms.append(PrizeFormData())
            }
        }
    }
    
    /**
     * 提交配置
     */
    private func submitConfiguration() {
        // 验证表单数据
        let validationResult = formData.validateCurrentForms()
        if !validationResult.isValid {
            validationErrors = validationResult.errorMessages
            return
        }
        
        // 清除错误信息
        validationErrors = []
        isSubmitting = true
        
        // 提交数据
        onSubmit(formData)
    }
    
    /**
     * 导入单个规则模板
     */
    private func importRuleTemplate(_ template: RuleTemplate) {
        guard formData.canAddMoreForms else { return }
        
        let type = template.type ?? "add"
        var newRule = RuleFormData(type: type)
        newRule.name = template.name ?? ""
        newRule.value = String(template.value)
        
        if type == "add" {
            formData.addRuleForms.append(newRule)
        } else {
            formData.deductRuleForms.append(newRule)
        }
    }
    
    /**
     * 导入单个奖品模板
     */
    private func importPrizeTemplate(_ template: PrizeTemplate) {
        guard formData.canAddMoreForms else { return }
        
        var newPrize = PrizeFormData()
        newPrize.name = template.name ?? ""
        newPrize.cost = String(template.cost)
        newPrize.type = template.type ?? "虚拟"
        
        formData.prizeForms.append(newPrize)
    }
    
    /**
     * 检查规则模板是否已经添加到表单中
     */
    private func isTemplateAlreadyAdded<T>(_ template: RuleTemplate, in forms: [T]) -> Bool where T: Any {
        guard let forms = forms as? [RuleFormData] else { return false }
        
        let templateName = (template.name ?? "").trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        let templateValue = template.value
        
        return forms.contains { form in
            let formName = form.name.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
            let formValue = form.valueInt
            return formName == templateName && formValue == templateValue && !formName.isEmpty
        }
    }
    
    /**
     * 检查奖品模板是否已经添加到表单中
     */
    private func isPrizeTemplateAlreadyAdded(_ template: PrizeTemplate, in forms: [PrizeFormData]) -> Bool {
        let templateName = (template.name ?? "").trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        let templateCost = template.cost
        let templateType = template.type ?? "虚拟"
        
        return forms.contains { form in
            let formName = form.name.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
            let formCost = form.costInt
            let formType = form.type
            return formName == templateName && formCost == templateCost && formType == templateType && !formName.isEmpty
        }
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
} 