//
//  ExcelImportView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI
import UniformTypeIdentifiers

/**
 * Excel导入界面组件
 * 支持选择Excel文件并导入学生数据
 */
struct ExcelImportView: View {
    
    @Binding var isPresented: Bool
    @State private var selectedFileURL: URL?
    @State private var fileName: String = ""
    @State private var isImporting: Bool = false
    @State private var importError: String?
    @State private var animationTrigger = false
    @State private var showFilePicker = false
    
    let onImport: (Data) -> Void
    let onCancel: () -> Void
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        if !isImporting {
                            onCancel()
                        }
                    }
                    .transition(.opacity)
                
                // 导入对话框
                VStack(spacing: 0) {
                    // 标题栏
                    HStack {
                        Text("add_student.excel.title".localized)
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 24)
                    .padding(.top, 24)
                    .padding(.bottom, 16)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 24)
                    
                    // 内容区域
                    VStack(spacing: 24) {
                        // 说明文字
                        VStack(spacing: 12) {
                            Text("add_student.excel.description".localized)
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .multilineTextAlignment(.center)
                            
                            Text("add_student.excel.format_hint".localized)
                                .font(.system(size: 14, weight: .regular))
                                .foregroundColor(Color(hex: "#a9d051"))
                                .multilineTextAlignment(.center)
                        }
                        .padding(.horizontal, 20)
                        
                        // 格式示例
                        VStack(alignment: .leading, spacing: 8) {
                            Text("add_student.excel.format_example".localized)
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("姓名")
                                        .frame(width: 60, alignment: .leading)
                                    Text("学号")
                                        .frame(width: 60, alignment: .leading)
                                    Text("性别")
                                        .frame(width: 60, alignment: .leading)
                                    Text("初始积分")
                                        .frame(width: 60, alignment: .leading)
                                }
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(Color(hex: "#666666"))
                                
                                Rectangle()
                                    .fill(Color.gray.opacity(0.3))
                                    .frame(height: 1)
                                
                                HStack {
                                    Text("张三")
                                        .frame(width: 60, alignment: .leading)
                                    Text("01")
                                        .frame(width: 60, alignment: .leading)
                                    Text("男")
                                        .frame(width: 60, alignment: .leading)
                                    Text("0")
                                        .frame(width: 60, alignment: .leading)
                                }
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                
                                HStack {
                                    Text("李四")
                                        .frame(width: 60, alignment: .leading)
                                    Text("02")
                                        .frame(width: 60, alignment: .leading)
                                    Text("女")
                                        .frame(width: 60, alignment: .leading)
                                    Text("10")
                                        .frame(width: 60, alignment: .leading)
                                }
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            }
                        }
                        .padding(16)
                        .background(Color(hex: "#f8ffe5").opacity(0.6))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
                        )
                        .padding(.horizontal, 24)
                        
                        // 文件选择区域
                        VStack(spacing: 16) {
                            // 选择文件按钮
                            Button(action: {
                                showFilePicker = true
                            }) {
                                HStack(spacing: 12) {
                                    Image(systemName: "doc.badge.plus")
                                        .font(.system(size: 24))
                                        .foregroundColor(Color(hex: "#a9d051"))
                                    
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text("add_student.excel.select_file".localized)
                                            .font(.system(size: 16, weight: .semibold))
                                            .foregroundColor(DesignSystem.Colors.textPrimary)
                                        
                                        Text("add_student.excel.supported_formats".localized)
                                            .font(.system(size: 13, weight: .regular))
                                            .foregroundColor(DesignSystem.Colors.textSecondary)
                                    }
                                    
                                    Spacer()
                                    
                                    Image(systemName: "chevron.right")
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                }
                                .padding(16)
                                .background(Color.white)
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color(hex: "#a9d051").opacity(0.3), lineWidth: 1.5)
                                )
                            }
                            .disabled(isImporting)
                            
                            // 已选择的文件信息
                            if !fileName.isEmpty {
                                HStack(spacing: 12) {
                                    Image(systemName: "doc.text.fill")
                                        .font(.system(size: 20))
                                        .foregroundColor(Color(hex: "#74c07f"))
                                    
                                    VStack(alignment: .leading, spacing: 2) {
                                        Text("add_student.excel.selected_file".localized)
                                            .font(.system(size: 13, weight: .regular))
                                            .foregroundColor(DesignSystem.Colors.textSecondary)
                                        
                                        Text(fileName)
                                            .font(.system(size: 14, weight: .medium))
                                            .foregroundColor(DesignSystem.Colors.textPrimary)
                                            .lineLimit(1)
                                    }
                                    
                                    Spacer()
                                }
                                .padding(12)
                                .background(Color(hex: "#f0f9ff"))
                                .cornerRadius(8)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color(hex: "#74c07f").opacity(0.3), lineWidth: 1)
                                )
                            }
                        }
                        .padding(.horizontal, 24)
                        
                        // 错误提示
                        if let error = importError {
                            Text(error)
                                .font(.system(size: 14, weight: .regular))
                                .foregroundColor(.red)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 24)
                                .padding(.vertical, 12)
                                .background(Color.red.opacity(0.05))
                                .cornerRadius(8)
                                .padding(.horizontal, 24)
                        }
                    }
                    .padding(.vertical, 24)
                    
                    // 底部按钮区域
                    HStack(spacing: 16) {
                        // 取消按钮
                        Button(action: {
                            if !isImporting {
                                onCancel()
                            }
                        }) {
                            Text("common.button.cancel".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color.gray)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(12)
                        }
                        .disabled(isImporting)
                        
                        // 导入按钮
                        Button(action: importFile) {
                            HStack(spacing: 8) {
                                if isImporting {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                }
                                
                                Text(isImporting ? "add_student.excel.importing".localized : "add_student.excel.import_button".localized)
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 44)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#a9d051"),
                                        Color(hex: "#74c07f")
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .cornerRadius(12)
                            .opacity(selectedFileURL != nil && !isImporting ? 1.0 : 0.6)
                        }
                        .disabled(selectedFileURL == nil || isImporting)
                    }
                    .padding(.horizontal, 24)
                    .padding(.bottom, 28)
                }
                .frame(maxWidth: 380)
                .background(Color.white)
                .cornerRadius(20)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1.5)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                .scaleEffect(animationTrigger ? 1.0 : 0.9)
                .opacity(animationTrigger ? 1.0 : 0.0)
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
                resetState()
            }
        }
        .fileImporter(
            isPresented: $showFilePicker,
            allowedContentTypes: [
                .commaSeparatedText,        // CSV文件
                .tabSeparatedText,          // TSV文件
                .plainText,                 // 纯文本文件
                UTType(filenameExtension: "csv") ?? .commaSeparatedText  // 确保支持.csv扩展名
            ],
            allowsMultipleSelection: false
        ) { result in
            handleFileSelection(result)
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 处理文件选择结果
     */
    private func handleFileSelection(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            if let url = urls.first {
                print("📁 文件选择成功: \(url.lastPathComponent)")
                print("📁 文件路径: \(url.path)")
                print("📁 文件大小: \(getFileSize(url: url))")

                selectedFileURL = url
                fileName = url.lastPathComponent
                importError = nil
            }
        case .failure(let error):
            print("❌ 文件选择失败: \(error.localizedDescription)")
            importError = "add_student.excel.file_selection_error".localized
        }
    }

    /**
     * 获取文件大小
     */
    private func getFileSize(url: URL) -> String {
        do {
            let resources = try url.resourceValues(forKeys: [.fileSizeKey])
            if let fileSize = resources.fileSize {
                return "\(fileSize) bytes"
            }
        } catch {
            print("无法获取文件大小: \(error)")
        }
        return "未知"
    }
    
    /**
     * 导入文件
     */
    private func importFile() {
        guard let fileURL = selectedFileURL else { return }

        isImporting = true
        importError = nil

        DispatchQueue.global(qos: .userInitiated).async {
            // 开始访问安全作用域资源
            let accessGranted = fileURL.startAccessingSecurityScopedResource()

            defer {
                // 确保在函数结束时停止访问安全作用域资源
                if accessGranted {
                    fileURL.stopAccessingSecurityScopedResource()
                }
            }

            do {
                let data = try Data(contentsOf: fileURL)

                DispatchQueue.main.async {
                    onImport(data)
                    isImporting = false
                }
            } catch {
                DispatchQueue.main.async {
                    importError = "add_student.excel.read_error".localized
                    isImporting = false
                    print("文件读取错误: \(error.localizedDescription)")
                }
            }
        }
    }
    
    /**
     * 重置状态
     */
    private func resetState() {
        selectedFileURL = nil
        fileName = ""
        importError = nil
        isImporting = false
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        ExcelImportView(
            isPresented: .constant(true),
            onImport: { data in
                print("导入数据: \(data.count) bytes")
            },
            onCancel: {
                print("取消导入")
            }
        )
    }
}