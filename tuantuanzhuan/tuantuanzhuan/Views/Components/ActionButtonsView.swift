//
//  ActionButtonsView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/6/23.
//

import SwiftUI

/**
 * 操作按钮区域组件
 */
struct ActionButtonsView: View {
    
    let totalScore: Int
    let dateRangeText: String
    let onAddStudentTapped: () -> Void
    let onClassOperationTapped: () -> Void
    let onTotalScoreTapped: () -> Void
    
    @State private var totalScoreAnimationTrigger = false
    @State private var addStudentPressed = false
    @State private var classOperationPressed = false
    @State private var totalScorePressed = false
    
    var body: some View {
        GeometryReader { geometry in
            let totalPadding: CGFloat = 50
            let buttonSpacing: CGFloat = 16
            let availableWidth = geometry.size.width - totalPadding - buttonSpacing
            let equalButtonWidth = availableWidth / 2
            
            HStack(spacing: buttonSpacing) {
                // 左侧大按钮 - 全班一起加分（美化版）
                Button(action: {
                    // 立即设置按下状态并触发回调
                    totalScorePressed = true
                    totalScoreAnimationTrigger.toggle()
                    onTotalScoreTapped()
                    
                    // 短暂延迟后重置按下状态，仅用于视觉反馈
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                        totalScorePressed = false
                    }
                }) {
                    ZStack {
                        // 美化背景容器
                        RoundedRectangle(cornerRadius: 20)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#f8ffe5"),
                                        Color(hex: "#edf6d9")
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: equalButtonWidth, height: 120)
                            .shadow(color: Color(hex: "#a9d051").opacity(0.3), radius: totalScorePressed ? 12 : 8, x: 0, y: totalScorePressed ? 6 : 4)
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                Color(hex: "#a9d051").opacity(0.3),
                                                Color(hex: "#74c07f").opacity(0.2)
                                            ]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        lineWidth: 2
                                    )
                            )
                        
                        // 装饰性背景图案
                        Circle()
                            .fill(Color(hex: "#a9d051").opacity(0.08))
                            .frame(width: 80, height: 80)
                            .offset(x: 35, y: -25)
                        
                        Circle()
                            .fill(Color(hex: "#74c07f").opacity(0.06))
                            .frame(width: 60, height: 60)
                            .offset(x: -30, y: 30)
                        
                        // 内容区域 - 使用VStack垂直布局避免重叠
                        VStack(alignment: .leading, spacing: 4) {
                            // 标题文字 - 显示时间范围信息
                            VStack(alignment: .leading, spacing: 2) {
                                HStack {
                                    Text("home.button.class_total_score".localized)
                                        .font(.system(size: 14, weight: .semibold))
                                        .foregroundColor(Color(hex: "#a9d051"))
                                        .lineLimit(1)
                                        .minimumScaleFactor(0.8)
                                    Spacer()
                                }
                                
                                HStack {
                                    Text(dateRangeText)
                                        .font(.system(size: 12, weight: .medium))
                                        .foregroundColor(Color(hex: "#74c07f"))
                                        .lineLimit(1)
                                        .minimumScaleFactor(0.7)
                                    Spacer()
                                }
                            }
                            .padding(.top, 8)
                            .padding(.horizontal, 16)
                            
                            Spacer()
                            
                            // 积分数字 - 居中显示
                            HStack {
                                Spacer()
                                Text("\(totalScore)")
                                    .font(.system(size: 36, weight: .black))
                                    .foregroundColor(Color(hex: "#74c07f"))
                                    .shadow(color: Color(hex: "#74c07f").opacity(0.3), radius: 4, x: 0, y: 2)
                                    .scaleEffect(totalScoreAnimationTrigger ? 1.1 : 1.0)
                                    .animation(.spring(response: 0.4, dampingFraction: 0.8), value: totalScoreAnimationTrigger)
                                    .lineLimit(1)
                                    .minimumScaleFactor(0.7)
                                Spacer()
                            }
                            .padding(.bottom, 12)
                        }
                        
                        // 闪烁效果
                        if totalScorePressed {
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color.white.opacity(0.3))
                                .frame(width: equalButtonWidth, height: 120)
                                .transition(.opacity)
                        }
                    }
                    .frame(width: equalButtonWidth, height: 120)
                    .scaleEffect(totalScorePressed ? 0.95 : 1.0)
                }
                .buttonStyle(PlainButtonStyle())
                
                // 右侧按钮组（美化版）
                VStack(spacing: DesignSystem.Spacing.sm) {
                    // 添加学生按钮
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            addStudentPressed = true
                        }
                        
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            addStudentPressed = false
                            onAddStudentTapped()
                        }
                    }) {
                        ZStack {
                            // 美化背景容器
                            RoundedRectangle(cornerRadius: 16)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(hex: "#f8ffe5"),
                                            Color(hex: "#edf6d9")
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: equalButtonWidth, height: 56)
                                .shadow(color: Color(hex: "#a9d051").opacity(0.25), radius: addStudentPressed ? 10 : 6, x: 0, y: addStudentPressed ? 4 : 2)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
                                )
                            
                            // 装饰圆点
                            HStack {
                                Spacer()
                                VStack {
                                    Circle()
                                        .fill(Color(hex: "#a9d051").opacity(0.15))
                                        .frame(width: 30, height: 30)
                                        .offset(x: 10, y: -10)
                                    Spacer()
                                }
                            }
                            
                            // 文字 - 左对齐
                            HStack {
                                Text("home.button.add_student".localized)
                                    .font(.system(size: 18, weight: .semibold))
                                    .foregroundColor(Color(hex: "#a9d051"))
                                    .padding(.leading, 24)
                                Spacer()
                            }
                            
                            // 图标 - 右对齐
                            HStack {
                                Spacer()
                                Image("添加学生")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 50, height: 50)
                                    .padding(.trailing, 8)
                                    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                            }
                            
                            // 按压闪烁效果
                            if addStudentPressed {
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color.white.opacity(0.4))
                                    .frame(width: equalButtonWidth, height: 56)
                                    .transition(.opacity)
                            }
                        }
                        .frame(width: equalButtonWidth, height: 56)
                        .scaleEffect(addStudentPressed ? 0.95 : 1.0)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    // 全班操作按钮
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            classOperationPressed = true
                        }
                        
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            classOperationPressed = false
                            onClassOperationTapped()
                        }
                    }) {
                        ZStack {
                            // 美化背景容器
                            RoundedRectangle(cornerRadius: 16)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(hex: "#f8ffe5"),
                                            Color(hex: "#edf6d9")
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: equalButtonWidth, height: 56)
                                .shadow(color: Color(hex: "#a9d051").opacity(0.25), radius: classOperationPressed ? 10 : 6, x: 0, y: classOperationPressed ? 4 : 2)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
                                )
                            
                            // 装饰圆点
                            HStack {
                                VStack {
                                    Circle()
                                        .fill(Color(hex: "#a9d051").opacity(0.15))
                                        .frame(width: 25, height: 25)
                                        .offset(x: -5, y: 8)
                                    Spacer()
                                }
                                Spacer()
                            }
                            
                            // 文字 - 左对齐
                            HStack {
                                Text("home.button.class_operation".localized)
                                    .font(.system(size: 18, weight: .semibold))
                                    .foregroundColor(Color(hex: "#a9d051"))
                                    .padding(.leading, 24)
                                Spacer()
                            }
                            
                            // 图标 - 右对齐
                            HStack {
                                Spacer()
                                Image("全班操作")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 40, height: 40)
                                    .padding(.trailing, 12)
                                    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                            }
                            
                            // 按压闪烁效果
                            if classOperationPressed {
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color.white.opacity(0.4))
                                    .frame(width: equalButtonWidth, height: 56)
                                    .transition(.opacity)
                            }
                        }
                        .frame(width: equalButtonWidth, height: 56)
                        .scaleEffect(classOperationPressed ? 0.95 : 1.0)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .frame(width: equalButtonWidth)
            }
            .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
        }
        .frame(height: 130)
        .onAppear {
            // 入场动画
            withAnimation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.4)) {
                totalScoreAnimationTrigger.toggle()
            }
        }
    }
}

// MARK: - Preview
#Preview {
    ActionButtonsView(
        totalScore: 861,
        dateRangeText: "本月",
        onAddStudentTapped: { print("添加学生") },
        onClassOperationTapped: { print("全班操作") },
        onTotalScoreTapped: { print("创建游戏") }
    )
    .background(
        LinearGradient(
            gradient: Gradient(colors: [DesignSystem.Colors.background, Color.white]),
            startPoint: .top,
            endPoint: .bottom
        )
    )
} 