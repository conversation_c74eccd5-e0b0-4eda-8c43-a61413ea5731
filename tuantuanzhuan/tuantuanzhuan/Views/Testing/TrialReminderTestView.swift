//
//  TrialReminderTestView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/20.
//

import SwiftUI

/**
 * 试用期提醒功能测试视图
 * 用于测试试用期订阅提醒弹窗的显示和交互
 */
struct TrialReminderTestView: View {
    
    // MARK: - State
    @State private var showTrialReminderModal = false
    @State private var showSubscriptionView = false
    @State private var testTrialActive = true
    
    // MARK: - Dependencies
    @StateObject private var trialManager = TrialManager.shared
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题
                Text("试用期提醒功能测试")
                    .font(.title)
                    .fontWeight(.bold)
                    .padding()
                
                // 当前试用状态显示
                VStack(spacing: 10) {
                    Text("当前试用状态")
                        .font(.headline)
                    
                    Text(trialManager.isTrialActive ? "试用中" : "未试用")
                        .font(.body)
                        .foregroundColor(trialManager.isTrialActive ? .green : .red)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(trialManager.isTrialActive ? Color.green.opacity(0.1) : Color.red.opacity(0.1))
                        )
                }
                
                // 测试按钮组
                VStack(spacing: 15) {
                    // 模拟点击查看订阅方案按钮
                    Button(action: {
                        handleViewPlansPressed()
                    }) {
                        Text("模拟点击「查看订阅方案」")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(Color.blue)
                            .cornerRadius(12)
                    }
                    
                    // 直接显示提醒弹窗
                    Button(action: {
                        showTrialReminderModal = true
                    }) {
                        Text("直接显示试用期提醒弹窗")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(Color.orange)
                            .cornerRadius(12)
                    }
                    
                    // 切换试用状态（仅用于测试）
                    Button(action: {
                        testTrialActive.toggle()
                    }) {
                        Text("切换测试试用状态: \(testTrialActive ? "激活" : "未激活")")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(Color.purple)
                            .cornerRadius(12)
                    }
                }
                .padding(.horizontal, 20)
                
                Spacer()
                
                // 状态信息
                VStack(spacing: 8) {
                    Text("测试说明:")
                        .font(.headline)
                    
                    Text("• 如果用户处于试用期，点击「查看订阅方案」会先显示提醒弹窗")
                        .font(.caption)
                        .multilineTextAlignment(.leading)
                    
                    Text("• 点击「谢谢提醒」后会关闭弹窗并跳转到订阅页面")
                        .font(.caption)
                        .multilineTextAlignment(.leading)
                    
                    Text("• 如果用户不在试用期，直接跳转到订阅页面")
                        .font(.caption)
                        .multilineTextAlignment(.leading)
                }
                .padding(.horizontal, 20)
                .foregroundColor(.secondary)
            }
            .navigationTitle("试用期提醒测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showTrialReminderModal) {
            TrialSubscriptionReminderModal(isPresented: $showTrialReminderModal) {
                print("🔔 测试: 用户确认试用期提醒，跳转到订阅页面")
                showSubscriptionView = true
            }
        }
        .fullScreenCover(isPresented: $showSubscriptionView) {
            // 这里可以显示实际的订阅页面，或者一个测试页面
            NavigationView {
                VStack {
                    Text("订阅页面")
                        .font(.title)
                        .padding()
                    
                    Text("这里是订阅页面的内容")
                        .padding()
                    
                    Button("关闭") {
                        showSubscriptionView = false
                    }
                    .padding()
                }
                .navigationTitle("订阅页面")
                .navigationBarTitleDisplayMode(.inline)
            }
        }
    }
    
    // MARK: - Methods
    
    /**
     * 模拟处理查看会员方案按钮点击
     */
    private func handleViewPlansPressed() {
        print("🔍 测试: 查看会员方案功能")
        
        // 使用测试状态或实际试用状态
        let isTrialActive = testTrialActive && trialManager.isTrialActive
        
        if isTrialActive {
            print("🔔 测试: 用户处于试用期内，显示温馨提醒")
            showTrialReminderModal = true
        } else {
            print("📱 测试: 用户不在试用期，直接跳转到订阅页面")
            showSubscriptionView = true
        }
    }
}

// MARK: - Preview
#Preview {
    TrialReminderTestView()
}
