//
//  ScratchCardView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 刮刮卡主视图
 * 集成所有刮刮卡相关组件，提供完整的刮刮卡体验
 */
struct ScratchCardView: View {
    
    // MARK: - Properties
    let student: Student
    let schoolClass: SchoolClass
    let onNavigateToSettings: (() -> Void)?
    
    // MARK: - View Models
    @StateObject private var viewModel: ScratchCardViewModel
    
    // MARK: - State
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Initialization
    
    init(student: Student, schoolClass: SchoolClass, onNavigateToSettings: (() -> Void)? = nil) {
        self.student = student
        self.schoolClass = schoolClass
        self.onNavigateToSettings = onNavigateToSettings
        self._viewModel = StateObject(wrappedValue: ScratchCardViewModel(student: student, schoolClass: schoolClass))
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                backgroundGradient
                
                // 主要内容
                mainContent(geometry: geometry)
                
                // 刮除覆盖层
                if viewModel.showScratchOverlay, let selectedIndex = viewModel.selectedCardIndex {
                    scratchOverlay(cardIndex: selectedIndex)
                        .allowsHitTesting(true) // 禁用点击外面退回
                }
                
                // 结果弹窗
                if viewModel.showResult {
                    resultOverlay
                }
                
                // 积分不足提示
                if viewModel.showInsufficientPoints {
                    insufficientPointsAlert
                }
                
                // 粒子效果层
                particleEffectLayer
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("scratch_card.title".localized)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                backButton
            }
        }
        .onAppear {
            viewModel.loadScratchCardConfig()
        }
    }
    
    // MARK: - Background Gradient
    
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color(hex: "#ffeef0").opacity(0.3),
                Color(hex: "#fff8e1").opacity(0.2),
                Color.white
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Main Content
    
    private func mainContent(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // 顶部统计信息
            if !viewModel.cardItems.isEmpty {
                scratchCardStatsView
                    .padding(.top, 20)
                    .padding(.bottom, 16)
            }
            
            // 刮刮卡网格或空状态
            contentArea(geometry: geometry)
        }
    }
    
    // MARK: - Content Area
    
    @ViewBuilder
    private func contentArea(geometry: GeometryProxy) -> some View {
        if viewModel.isLoading {
            loadingView
        } else if viewModel.cardItems.isEmpty {
            emptyStateView
        } else {
            scratchCardGridView(geometry: geometry)
        }
    }
    
    // MARK: - Stats View
    
    private var scratchCardStatsView: some View {
        VStack(spacing: 12) {
            HStack {
                // 剩余卡片数
                HStack(spacing: 8) {
                    Image(systemName: "square.stack.3d.up")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color(hex: "#ff6b6b"))
                    
                    Text(String(format: "scratch_card.remaining_format".localized, viewModel.unscatchedCount))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                
                Spacer()
                
                // 消耗积分
                HStack(spacing: 8) {
                    Image(systemName: "star.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color(hex: "#a9d051"))
                    
                    Text(String(format: "scratch_card.cost_format".localized, viewModel.costPerScratch))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }
            
            // 当前积分
            HStack(spacing: 8) {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.blue)
                
                Text(String(format: "scratch_card.current_points_format".localized, student.point))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                // 积分状态
                Text(viewModel.canAffordScratching ? "scratch_card.points_sufficient".localized : "scratch_card.points_insufficient".localized)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(viewModel.canAffordScratching ? Color.green : Color.red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill((viewModel.canAffordScratching ? Color.green : Color.red).opacity(0.1))
                    )
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.8))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal, 20)
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .progressViewStyle(CircularProgressViewStyle(tint: Color(hex: "#ff6b6b")))
            
            Text("scratch_card.loading".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white.opacity(0.8))
    }
    
    // MARK: - Empty State View
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "square.stack.3d.up")
                .font(.system(size: 60, weight: .light))
                .foregroundColor(Color.gray.opacity(0.5))
            
            VStack(spacing: 8) {
                Text("scratch_card.no_config".localized)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("scratch_card.config_description".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: {
                dismiss()
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    onNavigateToSettings?()
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "gear")
                        .font(.system(size: 16, weight: .medium))
                    
                    Text("scratch_card.go_settings".localized)
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(hex: "#ff6b6b"))
                )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white.opacity(0.8))
    }
    
    // MARK: - Scratch Card Grid View
    
    private func scratchCardGridView(geometry: GeometryProxy) -> some View {
        ScratchCardGridView(
            viewModel: viewModel,
            geometry: geometry,
            onCardTapped: { index in
                handleCardTapped(at: index)
            }
        )
    }
    
    // MARK: - Scratch Overlay
    
    private func scratchOverlay(cardIndex: Int) -> some View {
        ZStack {
            // 背景遮罩（不允许点击外面退回）
            Color.black.opacity(0.8)
                .ignoresSafeArea()
                .onTapGesture {
                    // 不执行任何操作，禁用点击外面退回
                }
            
            VStack(spacing: 20) {
                // 添加"开心刮刮卡"标题
                VStack(spacing: 20) {
                    Text("scratch_card.happy_title".localized)
                        .font(.system(size: DeviceDetection.isPad ? 40 : 30, weight: .bold))
                        .foregroundColor(.white)
                        .shadow(color: Color.black.opacity(0.3), radius: 2, x: 0, y: 1)
                    
                    Text("scratch_card.happy_subtitle".localized)
                        .font(.system(size: DeviceDetection.isPad ? 24 : 18, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding(.top, 80)
                
                // 刮刮卡（缩小尺寸）
                ScratchCardCanvasView(
                    cardItem: viewModel.cardItems[cardIndex],
                    onProgressUpdate: { progress in
                        viewModel.updateScratchProgress(index: cardIndex, progress: progress)
                    },
                    onScratchComplete: {
                        viewModel.revealPrize(at: cardIndex)
                    }
                )
                .scaleEffect(viewModel.cardItems[cardIndex].animationState == .selected ? 1.0 : 0.8)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: viewModel.cardItems[cardIndex].animationState)
                
                Spacer()
            }
        }
    }
    
    // MARK: - Result Overlay
    
    private var resultOverlay: some View {
        ScratchCardResultView(
            prizeName: viewModel.resultPrize,
            costPoints: viewModel.costPerScratch,
            onConfirm: {
                viewModel.confirmResult()
            }
        )
        .zIndex(1001)
    }
    
    // MARK: - Alert Views
    
    private var insufficientPointsAlert: some View {
        ScratchCardAlertView(
            title: "scratch_card.insufficient_points_title".localized,
            message: String(format: "scratch_card.insufficient_points_message_format".localized, viewModel.costPerScratch, student.point),
            primaryButton: .init(
                title: "common.confirm".localized,
                action: {
                    viewModel.showInsufficientPoints = false
                }
            ),
            secondaryButton: nil
        )
        .zIndex(999)
    }
    
    // MARK: - Particle Effect Layer
    
    private var particleEffectLayer: some View {
        ParticleSystemView(
            particles: viewModel.particles,
            isActive: !viewModel.particles.isEmpty
        )
        .zIndex(998)
    }
    
    // MARK: - Back Button
    
    private var backButton: some View {
        Button(action: {
            dismiss()
        }) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .semibold))
                
                Text("scratch_card.back".localized)
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(DesignSystem.Colors.textPrimary)
        }
    }
    
    // MARK: - Action Handlers
    
    /**
     * 处理卡片点击
     */
    private func handleCardTapped(at index: Int) {
        _ = viewModel.selectCard(at: index)
    }
}

/**
 * 刮刮卡自定义警告弹窗组件
 */
struct ScratchCardAlertView: View {
    
    struct AlertButton {
        let title: String
        let action: () -> Void
    }
    
    let title: String
    let message: String
    let primaryButton: AlertButton
    let secondaryButton: AlertButton?
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.4)
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                VStack(spacing: 8) {
                    Text(title)
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text(message)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
                
                HStack(spacing: 12) {
                    if let secondaryButton = secondaryButton {
                        Button(action: secondaryButton.action) {
                            Text(secondaryButton.title)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.gray.opacity(0.1))
                                )
                        }
                    }
                    
                    Button(action: primaryButton.action) {
                        Text(primaryButton.title)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 44)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(hex: "#ff6b6b"))
                            )
                    }
                }
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 4)
            )
            .frame(maxWidth: 280)
        }
    }
}

// MARK: - Preview

#Preview {
    NavigationView {
        ScratchCardView(
            student: Student(),
            schoolClass: SchoolClass()
        )
    }
}