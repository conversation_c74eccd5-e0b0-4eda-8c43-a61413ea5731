//
//  ScratchCardResultView.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 刮刮卡结果展示组件
 * 显示刮除结果，包含奖品信息、庆祝动画和确认操作
 */
struct ScratchCardResultView: View {
    
    // MARK: - Properties
    let prizeName: String
    let costPoints: Int
    let onConfirm: () -> Void
    
    // MARK: - Animation States
    @State private var showResult = false
    @State private var celebrationTriggered = false
    @State private var scaleEffect: CGFloat = 0.3
    @State private var rotationAngle: Double = 0
    @State private var bounceOffset: CGFloat = 0
    @State private var glowIntensity: Double = 0.0
    @State private var sparkleAnimation: Bool = false
    
    var body: some View {
        ZStack {
            // 背景遮罩
            backgroundOverlay
            
            // 主要内容
            if showResult {
                resultContentView
                    .scaleEffect(scaleEffect)
                    .rotation3DEffect(
                        .degrees(rotationAngle),
                        axis: (x: 0, y: 1, z: 0)
                    )
                    .offset(y: bounceOffset)
            }
            
            // 庆祝特效
            if celebrationTriggered {
                celebrationEffects
            }
        }
        .onAppear {
            startPresentationAnimation()
        }
    }
    
    // MARK: - Background Overlay
    
    private var backgroundOverlay: some View {
        Color.black.opacity(0.6)
            .ignoresSafeArea()
    }
    
    // MARK: - Result Content View
    
    private var resultContentView: some View {
        VStack(spacing: 24) {
            // 顶部庆祝区域
            celebrationHeader
            
            // 奖品信息卡片
            prizeInfoCard
            
            // 操作按钮
            actionButtons
        }
        .padding(30)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(
                    LinearGradient(
                        colors: [
                            Color(hex: "#ffeef0"),
                            Color(hex: "#fff8e1")
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
        )
        .frame(maxWidth: 320)
    }
    
    // MARK: - Celebration Header
    
    private var celebrationHeader: some View {
        VStack(spacing: 16) {
            // 主要庆祝图标
            ZStack {
                // 光晕效果
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.yellow.opacity(glowIntensity),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 0,
                            endRadius: 40
                        )
                    )
                    .frame(width: 80, height: 80)
                
                // 主图标
                Image(systemName: "star.fill")
                    .font(.system(size: 40, weight: .bold))
                    .foregroundColor(Color.yellow)
                    .shadow(color: Color.orange.opacity(0.6), radius: 4, x: 0, y: 2)
                    .scaleEffect(sparkleAnimation ? 1.2 : 1.0)
                    .rotationEffect(.degrees(sparkleAnimation ? 360 : 0))
            }
            
            // 庆祝文字
            VStack(spacing: 8) {
                Text("scratch_card.congratulations".localized)
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(Color(hex: "#ff6b6b"))
                
                Text("scratch_card.big_reveal".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
        }
    }
    
    // MARK: - Prize Info Card
    
    private var prizeInfoCard: some View {
        VStack(spacing: 16) {
            // 奖品名称
            Text(prizeName)
                .font(.system(size: 35, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .multilineTextAlignment(.center)
                .lineLimit(3)
                .padding(.horizontal, 16)
            
            // 装饰分割线
            decorationLine
            
            // 消耗积分信息
            costInfoView
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(hex: "#ff6b6b").opacity(0.2), lineWidth: 2)
                )
        )
    }
    
    // MARK: - Decoration Line
    
    private var decorationLine: some View {
        HStack(spacing: 8) {
            ForEach(0..<5, id: \.self) { _ in
                Circle()
                    .fill(Color(hex: "#ff6b6b").opacity(0.6))
                    .frame(width: 6, height: 6)
            }
        }
    }
    
    // MARK: - Cost Info View
    
    private var costInfoView: some View {
        HStack(spacing: 12) {
            // 积分图标
            Image(systemName: "star.circle.fill")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color(hex: "#a9d051"))
            
            // 消耗信息
            Text(String(format: "scratch_card.points_consumed_format".localized, costPoints))
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
            Capsule()
                .fill(Color(hex: "#a9d051").opacity(0.1))
        )
    }
    
    // MARK: - Action Buttons
    
    private var actionButtons: some View {
        VStack(spacing: 12) {
            // 确认领取按钮
            Button(action: onConfirm) {
                Text("scratch_card.confirm_claim".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        LinearGradient(
                            colors: [
                                Color(hex: "#ff6b6b"),
                                Color(hex: "#ff8e8e")
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .shadow(color: Color(hex: "#ff6b6b").opacity(0.4), radius: 8, x: 0, y: 4)
            }
            .scaleEffect(1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: 1.0)
        }
    }
    
    // MARK: - Celebration Effects
    
    private var celebrationEffects: some View {
        ZStack {
            // 彩带效果
            ForEach(0..<8, id: \.self) { index in
                confettiPiece(index: index)
            }
            
            // 光束效果
            ForEach(0..<4, id: \.self) { index in
                lightBeam(index: index)
            }
        }
        .ignoresSafeArea()
    }
    
    /**
     * 彩带片段
     */
    private func confettiPiece(index: Int) -> some View {
        Rectangle()
            .fill([Color.yellow, Color.orange, Color.red, Color.green, Color.blue, Color.purple].randomElement() ?? Color.yellow)
            .frame(width: 8, height: 16)
            .rotationEffect(.degrees(Double.random(in: 0...360)))
            .position(
                x: CGFloat.random(in: 50...350),
                y: celebrationTriggered ? CGFloat.random(in: 600...800) : CGFloat.random(in: -100...0)
            )
            .animation(
                .easeOut(duration: 2.0)
                .delay(Double(index) * 0.1),
                value: celebrationTriggered
            )
    }
    
    /**
     * 光束效果
     */
    private func lightBeam(index: Int) -> some View {
        Rectangle()
            .fill(
                LinearGradient(
                    colors: [Color.yellow.opacity(0.8), Color.clear],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .frame(width: 2, height: 200)
            .position(
                x: CGFloat(index * 100 + 50),
                y: 150
            )
            .rotationEffect(.degrees(Double(index * 45)))
            .opacity(celebrationTriggered ? 1.0 : 0.0)
            .animation(
                .easeInOut(duration: 1.5)
                .repeatCount(3, autoreverses: true)
                .delay(0.3),
                value: celebrationTriggered
            )
    }
    
    // MARK: - Animation Methods
    
    /**
     * 开始展示动画
     */
    private func startPresentationAnimation() {
        // 阶段1：显示结果
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            showResult = true
            scaleEffect = 1.0
        }
        
        // 阶段2：3D旋转效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.easeInOut(duration: 0.4)) {
                rotationAngle = 360
            }
        }
        
        // 阶段3：弹跳效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.6)) {
                bounceOffset = -10
            }
            
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8).delay(0.1)) {
                bounceOffset = 0
            }
        }
        
        // 阶段4：启动庆祝效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            celebrationTriggered = true
            
            // 光晕动画
            withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                glowIntensity = 0.6
            }
            
            // 闪烁动画
            withAnimation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
                sparkleAnimation = true
            }
        }
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        ScratchCardResultView(
            prizeName: "iPhone 15 Pro Max",
            costPoints: 50,
            onConfirm: {
                print("Confirmed!")
            }
        )
    }
} 