# 30天高级会员试用功能

## 功能概述

为每个新注册用户提供一次性的30天高级会员试用机会，用户点击领取后立即生效，享受所有高级会员权益，支持多设备同步。

## 技术实现

### 1. 核心组件

#### TrialManager
- **位置**: `Models/TrialManager.swift`
- **功能**: 管理试用状态、NSUbiquitousKeyValueStore同步、权限检查
- **关键方法**:
  - `canClaimTrial()`: 检查是否可以领取试用
  - `claimTrial()`: 领取试用
  - `checkTrialValidity()`: 检查试用有效性
  - `getTrialDisplayInfo()`: 获取显示信息

#### TrialClaimModal
- **位置**: `Views/Components/TrialClaimModal.swift`
- **功能**: 信纸样式的试用领取弹窗
- **特点**: 包含感谢文案和福利说明

### 2. 数据存储

#### NSUbiquitousKeyValueStore
- `hasReceivedTrial`: 是否已领取试用（多设备同步）
- `trialExpirationDate`: 试用到期时间

#### CoreData
- `Subscription.expirationDate`: 试用到期时间
- `Subscription.level`: 会员等级（试用期间为premium）

### 3. 权限系统集成

#### 优先级顺序
1. RevenueCat正式订阅（最高优先级）
2. 试用状态
3. 免费版

#### RevenueCatManager修改
- `determineSubscriptionLevel()`: 集成试用状态判断
- `expirationDate`: 支持试用到期时间
- 监听试用状态变化

#### PermissionManager修改
- 监听试用状态变化
- 自动更新权限

### 4. UI组件

#### SubscriptionBannerSection
- 根据试用状态显示不同文案
- 未领取：显示"你有一个限时福利可领取" + "立即领取"按钮
- 已领取：显示"升级会员，解锁更多专属权益" + "查看会员方案"按钮

#### UserInfoSection
- 显示试用会员状态："高级会员（试用中）"
- 显示试用到期时间

## 使用流程

### 1. 新用户注册登录
- 系统自动检查试用资格
- 个人中心显示试用领取入口

### 2. 领取试用
1. 用户点击"立即领取"按钮
2. 弹出信纸样式确认弹窗
3. 用户点击"立即领取"确认
4. 系统立即激活30天高级会员试用

### 3. 试用期间
- 享受所有高级会员权益
- 可创建最多5个班级
- 解锁所有游戏功能
- 获得AI分析报告
- 配置最多10条班级规则

### 4. 试用过期
- 自动降级为免费版
- 保留试用记录，不可重复领取

## 多设备同步

### NSUbiquitousKeyValueStore
- 试用领取状态在所有设备间同步
- 防止用户在不同设备重复领取

### CloudKit
- 试用到期时间通过CoreData同步
- 确保所有设备显示一致的会员状态

## 安全机制

### 防重复领取
1. NSUbiquitousKeyValueStore标记已领取状态
2. 多设备实时同步
3. 删除账号后重新注册仍无法重复领取

### 状态一致性
1. 定时检查试用有效性（每小时）
2. 应用启动时验证状态
3. 外部存储变化时自动更新

## 本地化支持

### 中文
- 试用相关文案
- 弹窗内容
- 状态显示

### 英文
- 完整的英文本地化
- 适配国际用户

## 测试验证

### 功能测试
1. 新用户可以看到试用入口
2. 点击领取弹出确认弹窗
3. 确认后立即激活试用
4. 试用期间享受高级权益
5. 过期后自动降级

### 多设备测试
1. 在设备A领取试用
2. 设备B应该显示已领取状态
3. 两设备显示相同的到期时间

### 边界测试
1. 重复领取被阻止
2. 试用过期正确处理
3. 与正式订阅的优先级正确

## 注意事项

1. **不影响RevenueCat**: 试用状态仅在本地管理，不写入RevenueCat
2. **优先级明确**: 正式订阅始终优先于试用
3. **一次性机会**: 每个Apple ID仅能领取一次
4. **自动过期**: 无需手动处理，系统自动降级
5. **多设备一致**: 所有设备显示相同状态

## 后续扩展

1. 可以添加试用提醒功能
2. 可以统计试用转化率
3. 可以添加试用延期机制
4. 可以个性化试用时长
