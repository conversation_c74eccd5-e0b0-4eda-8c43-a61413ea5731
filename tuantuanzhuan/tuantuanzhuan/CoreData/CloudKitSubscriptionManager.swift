//
//  CloudKitSubscriptionManager.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData
import CloudKit
import SwiftUI

/**
 * CloudKit订阅管理器
 * 提供便捷的方法来管理用户订阅等级和CloudKit同步状态
 */
@MainActor
class CloudKitSubscriptionManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = CloudKitSubscriptionManager()
    
    // MARK: - Published Properties
    @Published var currentSubscriptionLevel: Subscription.Level = .free
    @Published var isCloudKitEnabled: Bool = false
    @Published var syncStatus: SyncStatus = .idle
    @Published var lastSyncDate: Date?
    
    // MARK: - Private Properties
    private var coreDataManager: CoreDataManager {
        return CoreDataManager.shared
    }
    
    // MARK: - Enums
    
    enum SyncStatus {
        case idle           // 空闲
        case syncing        // 同步中
        case success        // 同步成功
        case failed(Error)  // 同步失败
        
        var displayText: String {
            switch self {
            case .idle:
                return "待同步"
            case .syncing:
                return "同步中..."
            case .success:
                return "同步成功"
            case .failed(let error):
                return "同步失败: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - Initialization
    
    private init() {
        loadCurrentSubscriptionLevel()
        updateCloudKitStatus()
    }
    
    // MARK: - Subscription Level Management
    
    /**
     * 更新订阅等级
     * - Parameter newLevel: 新的订阅等级
     * - Parameter shouldMigrate: 是否需要数据迁移（预留参数）
     */
    func updateSubscriptionLevel(_ newLevel: Subscription.Level, shouldMigrate: Bool = false) {
        let oldLevel = currentSubscriptionLevel
        currentSubscriptionLevel = newLevel
        
        // 更新用户订阅数据（通过CoreData自动同步）
        updateUserSubscription(to: newLevel)
        
        // 如果从付费用户降级到免费用户，清理超出限制的规则
        if oldLevel != .free && newLevel == .free {
            cleanupRulesForDowngrade(to: newLevel)
        }
        
        // 检查并处理订阅级别变更
        SubscriptionService.shared.handleSubscriptionLevelChange(
            oldLevel: oldLevel.rawValue,
            newLevel: newLevel.rawValue
        )
        
        print("📱 订阅等级已更新: \(oldLevel.rawValue) → \(newLevel.rawValue)")
        print("☁️ 数据将自动同步到所有设备")
        
        // 由于所有用户都使用CloudKit，不需要触发迁移
        if shouldMigrate {
            print("ℹ️ 数据迁移已简化：所有用户统一使用CloudKit同步")
        }
    }
    
    /**
     * 获取当前订阅等级
     */
    func getCurrentSubscriptionLevel() -> Subscription.Level {
        return currentSubscriptionLevel
    }
    
    /**
     * 检查功能权限
     */
    func hasPermission(for feature: Subscription.Feature) -> Bool {
        guard let user = coreDataManager.getCurrentUser(),
              let subscription = user.subscription else {
            return false
        }
        
        return subscription.hasPermission(for: feature)
    }
    
    // MARK: - CloudKit Sync Management
    
    /**
     * 手动触发同步
     */
    func triggerSync() async {
        guard isCloudKitEnabled else {
            syncStatus = .failed(SyncError.cloudKitNotEnabled)
            return
        }
        
        syncStatus = .syncing
        
        // 执行同步
        await performSync()
        syncStatus = .success
        lastSyncDate = Date()
    }
    
    /**
     * 检查同步状态
     */
    func checkSyncStatus() -> String {
        if !isCloudKitEnabled {
            return "CloudKit同步未启用"
        }
        
        return syncStatus.displayText
    }
    
    /**
     * 获取上次同步时间
     */
    func getLastSyncTimeText() -> String {
        guard let lastSync = lastSyncDate else {
            return "从未同步"
        }
        
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return "上次同步: \(formatter.string(from: lastSync))"
    }
    
    // MARK: - Private Methods
    
    /**
     * 加载当前订阅等级
     */
    private func loadCurrentSubscriptionLevel() {
        if let user = coreDataManager.getCurrentUser(),
           let subscription = user.subscription,
           let levelString = subscription.level {
            currentSubscriptionLevel = Subscription.Level(rawValue: levelString) ?? .free
        } else {
            // 如果没有订阅记录，创建免费订阅
            createDefaultSubscription()
        }
    }
    
    /**
     * 创建默认订阅
     */
    private func createDefaultSubscription() {
        let user = coreDataManager.getOrCreateDefaultUser()
        _ = Subscription.createFreeSubscription(
            for: user,
            in: coreDataManager.viewContext
        )
        coreDataManager.save()
        
        currentSubscriptionLevel = .free
    }
    
    /**
     * 更新用户订阅记录
     */
    private func updateUserSubscription(to newLevel: Subscription.Level) {
        guard let user = coreDataManager.getCurrentUser() else { return }
        
        if let subscription = user.subscription {
            subscription.upgrade(to: newLevel)
        } else {
            // 创建新的订阅记录
            let subscription = Subscription(context: coreDataManager.viewContext)
            subscription.level = newLevel.rawValue
            subscription.maxClasses = Int32(newLevel.maxClasses)
            subscription.updatedAt = Date()
            subscription.user = user
        }
        
        coreDataManager.save()
    }
    
    /**
     * 降级时清理超出限制的规则
     * 对所有班级执行规则清理
     */
    private func cleanupRulesForDowngrade(to newLevel: Subscription.Level) {
        guard let currentUser = coreDataManager.getCurrentUser() else {
            print("⚠️ 无法获取当前用户，跳过规则清理")
            return
        }
        
        let allClasses = currentUser.sortedClasses
        
        for schoolClass in allClasses {
            coreDataManager.cleanupExcessRules(for: schoolClass, userSubscriptionLevel: newLevel)
        }
        
        print("🧹 已为 \(allClasses.count) 个班级清理超出限制的规则")
    }
    
    /**
     * 更新CloudKit状态
     */
    private func updateCloudKitStatus() {
        isCloudKitEnabled = currentSubscriptionLevel.supportsMultiDeviceSync && 
                           coreDataManager.cloudKitSyncEnabled
    }
    
    /**
     * 执行实际的同步操作
     */
    private func performSync() async {
        // 这里实现实际的同步逻辑
        // 可以调用 CoreDataManager 的同步方法
        
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 模拟同步延迟
        
        // 触发CoreData保存以启动CloudKit同步
        if coreDataManager.viewContext.hasChanges {
            coreDataManager.save()
        }
    }
}

// MARK: - Sync Error

enum SyncError: LocalizedError {
    case cloudKitNotEnabled
    case networkUnavailable
    case quotaExceeded
    case unknown(Error)
    
    var errorDescription: String? {
        switch self {
        case .cloudKitNotEnabled:
            return "CloudKit同步未启用"
        case .networkUnavailable:
            return "网络不可用"
        case .quotaExceeded:
            return "iCloud存储空间不足"
        case .unknown(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
}

