//
//  PointRecord+CoreDataClass.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData

@objc(PointRecord)
public class PointRecord: NSManagedObject {
    
    // MARK: - Computed Properties
    
    /**
     * 格式化显示时间
     */
    var formattedTime: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: timestamp)
    }
    
    /**
     * 格式化显示分值
     */
    var formattedPoints: String {
        if value > 0 {
            return "+\(value)"
        } else {
            return "\(value)"
        }
    }
    
    /**
     * 分值颜色（十六进制字符串）
     */
    var pointsColorHex: String {
        return value > 0 ? "#26C34B" : "#FF5B5B"
    }
    
    /**
     * 是否为加分记录
     */
    var isPositive: Bool {
        return value > 0
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新积分记录的便利方法
     */
    @discardableResult
    static func create(
        reason: String,
        value: Int,
        for student: Student,
        in context: NSManagedObjectContext
    ) -> PointRecord {
        let record = PointRecord(context: context)
        record.id = UUID()
        record.reason = reason
        record.value = Int32(value)
        record.timestamp = Date()
        record.isReversed = false
        record.student = student
        return record
    }
}

extension PointRecord {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<PointRecord> {
        return NSFetchRequest<PointRecord>(entityName: "PointRecord")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var reason: String?
    @NSManaged public var value: Int32
    @NSManaged public var timestamp: Date?
    @NSManaged public var isReversed: Bool
    @NSManaged public var student: Student?
    
}

// MARK: - HistoryRecordProtocol Implementation

extension PointRecord: HistoryRecordProtocol {
    
    /// 记录唯一标识
    var recordId: UUID? {
        return id
    }
    
    /// 记录显示名称
    var displayName: String {
        return reason ?? "积分记录"
    }
    
    /// 积分数值
    var pointsValue: Int {
        return Int(value)
    }
    
    /// 记录类型
    var recordType: HistoryRecordType {
        return .points
    }
    
    /// 记录时间戳
    var recordTimestamp: Date? {
        return timestamp
    }
    
    /// 是否可以删除
    var canDelete: Bool {
        // 已撤销的记录不能删除
        return !isReversed
    }
    
    /// 删除记录的积分影响描述
    var deleteImpactDescription: String {
        if value > 0 {
            return "删除后将扣除 \(value) 积分"
        } else if value < 0 {
            return "删除后将返还 \(abs(value)) 积分"
        } else {
            return "删除此记录不会影响积分"
        }
    }
} 