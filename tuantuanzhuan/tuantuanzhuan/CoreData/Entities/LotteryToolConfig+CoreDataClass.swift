//
//  LotteryToolConfig+CoreDataClass.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/16.
//

import Foundation
import CoreData

@objc(LotteryToolConfig)
public class LotteryToolConfig: NSManagedObject {
    
    // MARK: - Enums
    
    enum ToolType: String, CaseIterable {
        case wheel = "大转盘"
        case box = "盲盒"
        case scratch = "刮刮卡"
        
        var displayName: String {
            return self.rawValue
        }
        
        var defaultItemCount: Int {
            switch self {
            case .wheel:
                return 8   // 大转盘默认8个分区（4-12范围中间值）
            case .box, .scratch:
                return 5   // 盲盒和刮刮卡默认5个
            }
        }
        
        var maxItemCount: Int {
            switch self {
            case .wheel:
                return 12  // 大转盘最多12个分区
            case .box, .scratch:
                return 20  // 盲盒和刮刮卡最多20个
            }
        }
        
        var minItemCount: Int {
            switch self {
            case .wheel:
                return 4   // 大转盘最少4个分区
            case .box, .scratch:
                return 1   // 盲盒和刮刮卡最少1个
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取抽奖道具类型枚举
     */
    var lotteryToolType: ToolType {
        return ToolType(rawValue: toolType ?? "大转盘") ?? .wheel
    }
    
    /**
     * 格式化显示时间
     */
    var formattedCreatedTime: String {
        guard let createdAt = createdAt else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: createdAt)
    }
    
    /**
     * 获取道具项目数组（按索引排序）
     */
    var sortedItems: [LotteryToolItem] {
        guard let itemsSet = items as? Set<LotteryToolItem> else { return [] }
        return Array(itemsSet).sorted { $0.itemIndex < $1.itemIndex }
    }
    
    /**
     * 检查配置是否完整
     */
    var isComplete: Bool {
        let currentItemCount = items?.count ?? 0
        return currentItemCount == itemCount && currentItemCount > 0
    }
    
    /**
     * 获取缺失的项目索引
     */
    var missingItemIndexes: [Int] {
        let existingIndexes = Set(sortedItems.map { Int($0.itemIndex) })
        let requiredIndexes = Set(1...Int(itemCount))
        return Array(requiredIndexes.subtracting(existingIndexes)).sorted()
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新抽奖道具配置的便利方法
     */
    @discardableResult
    static func create(
        toolType: String,
        itemCount: Int,
        costPerPlay: Int,
        for schoolClass: SchoolClass,
        in context: NSManagedObjectContext
    ) -> LotteryToolConfig {
        let config = LotteryToolConfig(context: context)
        config.id = UUID()
        config.toolType = toolType
        config.itemCount = Int32(itemCount)
        config.costPerPlay = Int32(costPerPlay)
        config.createdAt = Date()
        config.updatedAt = Date()
        config.schoolClass = schoolClass
        return config
    }
    
    /**
     * 更新配置信息
     */
    func updateConfig(itemCount: Int, costPerPlay: Int) {
        self.itemCount = Int32(itemCount)
        self.costPerPlay = Int32(costPerPlay)
        self.updatedAt = Date()
    }
    
    /**
     * 添加道具项目
     */
    @discardableResult
    func addItem(index: Int, prizeName: String, in context: NSManagedObjectContext) -> LotteryToolItem {
        let item = LotteryToolItem.create(
            index: index,
            prizeName: prizeName,
            for: self,
            in: context
        )
        return item
    }
    
    /**
     * 移除指定索引的道具项目
     */
    func removeItem(at index: Int, in context: NSManagedObjectContext) {
        guard let itemToRemove = sortedItems.first(where: { $0.itemIndex == index }) else { return }
        context.delete(itemToRemove)
        self.updatedAt = Date()
    }
    
    /**
     * 批量创建道具项目
     */
    func createDefaultItems(prizeNames: [String], in context: NSManagedObjectContext) {
        // 清除现有项目
        if let existingItems = items as? Set<LotteryToolItem> {
            for item in existingItems {
                context.delete(item)
            }
        }
        
        // 创建新项目
        for (index, prizeName) in prizeNames.enumerated() {
            if index < itemCount {
                addItem(index: index + 1, prizeName: prizeName, in: context)
            }
        }
        
        self.updatedAt = Date()
    }
}

extension LotteryToolConfig {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<LotteryToolConfig> {
        return NSFetchRequest<LotteryToolConfig>(entityName: "LotteryToolConfig")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var toolType: String?
    @NSManaged public var itemCount: Int32
    @NSManaged public var costPerPlay: Int32
    @NSManaged public var createdAt: Date?
    @NSManaged public var updatedAt: Date?
    @NSManaged public var schoolClass: SchoolClass?
    @NSManaged public var items: NSSet?
}

// MARK: - Generated accessors for items
extension LotteryToolConfig {
    
    @objc(addItemsObject:)
    @NSManaged public func addToItems(_ value: LotteryToolItem)
    
    @objc(removeItemsObject:)
    @NSManaged public func removeFromItems(_ value: LotteryToolItem)
    
    @objc(addItems:)
    @NSManaged public func addToItems(_ values: NSSet)
    
    @objc(removeItems:)
    @NSManaged public func removeFromItems(_ values: NSSet)
} 