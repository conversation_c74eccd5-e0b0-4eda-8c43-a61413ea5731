//
//  RuleTemplate+CoreDataClass.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData

@objc(RuleTemplate)
public class RuleTemplate: NSManagedObject {
    
    // MARK: - Enums
    
    enum RuleType: String, CaseIterable {
        case add = "add"
        case deduct = "deduct"
        
        var displayName: String {
            switch self {
            case .add:
                return "加分"
            case .deduct:
                return "扣分"
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取规则类型枚举
     */
    var ruleType: RuleType {
        return RuleType(rawValue: type ?? "add") ?? .add
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新规则模板的便利方法
     */
    @discardableResult
    static func create(
        name: String,
        value: Int,
        type: String,
        in context: NSManagedObjectContext
    ) -> RuleTemplate {
        let template = RuleTemplate(context: context)
        template.id = UUID()
        template.name = name
        template.value = Int32(value)
        template.type = type
        return template
    }
    
    /**
     * 转换为班级规则
     */
    func toRule(in schoolClass: SchoolClass, context: NSManagedObjectContext) -> Rule {
        return Rule.create(
            name: name ?? "",
            value: Int(value),
            type: type ?? "add",
            isFrequent: false,
            in: schoolClass,
            context: context
        )
    }
}

extension RuleTemplate {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<RuleTemplate> {
        return NSFetchRequest<RuleTemplate>(entityName: "RuleTemplate")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var value: Int32
    @NSManaged public var type: String?
    
} 