//
//  RedemptionRecord+CoreDataClass.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData

@objc(RedemptionRecord)
public class RedemptionRecord: NSManagedObject {
    
    // MARK: - Computed Properties
    
    /**
     * 格式化显示时间
     */
    var formattedTime: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: timestamp)
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新兑换记录的便利方法
     */
    @discardableResult
    static func create(
        prizeName: String,
        cost: Int,
        for student: Student,
        in context: NSManagedObjectContext
    ) -> RedemptionRecord {
        let record = RedemptionRecord(context: context)
        record.id = UUID()
        record.prizeName = prizeName
        record.cost = Int32(cost)
        record.timestamp = Date()
        record.student = student
        record.source = "redemption" // 默认为兑换类型
        return record
    }
    
    /**
     * 创建刮刮卡记录的便利方法
     */
    @discardableResult
    static func createScratchCardRecord(
        prizeName: String,
        cost: Int,
        for student: Student,
        in context: NSManagedObjectContext
    ) -> RedemptionRecord {
        let record = RedemptionRecord(context: context)
        record.id = UUID()
        record.prizeName = prizeName
        record.cost = Int32(cost)
        record.timestamp = Date()
        record.student = student
        record.source = "scratchCard" // 刮刮卡类型
        return record
    }
}

extension RedemptionRecord {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<RedemptionRecord> {
        return NSFetchRequest<RedemptionRecord>(entityName: "RedemptionRecord")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var prizeName: String?
    @NSManaged public var cost: Int32
    @NSManaged public var timestamp: Date?
    @NSManaged public var student: Student?
    @NSManaged public var source: String?
    
}

// MARK: - HistoryRecordProtocol Implementation

extension RedemptionRecord: HistoryRecordProtocol {
    
    /// 记录唯一标识
    var recordId: UUID? {
        return id
    }
    
    /// 记录显示名称
    var displayName: String {
        return prizeName ?? "兑换记录"
    }
    
    /// 积分数值（兑换记录为负值，表示消耗积分）
    var pointsValue: Int {
        return -Int(cost)
    }
    
    /// 记录类型
    var recordType: HistoryRecordType {
        guard let source = source else { return .redemption }
        
        switch source {
        case "scratchCard":
            return .scratchLottery
        case "wheelLottery":
            return .wheelLottery
        case "boxLottery":
            return .boxLottery
        default:
            return .redemption
        }
    }
    
    /// 记录时间戳
    var recordTimestamp: Date? {
        return timestamp
    }
    
    /// 删除记录的积分影响描述
    var deleteImpactDescription: String {
        return "删除后将返还 \(cost) 积分"
    }
} 