//
//  Subscription+CoreDataClass.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData

@objc(Subscription)
public class Subscription: NSManagedObject {

    // MARK: - Enums
    
    enum Level: String, CaseIterable {
        case free = "free"
        case basic = "basic"
        case premium = "premium"

        var displayName: String {
            switch self {
            case .free:
                return "免费版"
            case .basic:
                return "初级会员"
            case .premium:
                return "高级会员"
            }
        }
        
        var maxClasses: Int {
           switch self {
            case .free:
                return 1
            case .basic:
                return 2
            case .premium:
                return 5
           }
        }
        
        var rank: Int {
            switch self {
            case .free:
                return 0
            case .basic:
                return 1
            case .premium:
                return 2
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取订阅级别枚举
     */
    var subscriptionLevel: Level {
        return Level(rawValue: level ?? "free") ?? .free
    }
    
    /**
     * 是否为付费订阅
     */
    var isPaid: Bool {
        return subscriptionLevel != .free
    }
    
    /**
     * 是否支持大转盘抽奖
     */
    var supportsLottery: Bool {
        return subscriptionLevel == .basic || subscriptionLevel == .premium
    }
    
    /**
     * 是否支持盲盒和刮刮卡
     */
    var supportsAdvancedGames: Bool {
        return subscriptionLevel == .premium
    }
    
    /**
     * 是否支持AI分析报告
     */
    var supportsAIAnalysis: Bool {
        return subscriptionLevel == .premium
    }
    
    /**
     * 是否支持配置更多班级常用规则
     */
    var supportsMultiDeviceSync: Bool {
        return isPaid
    }
    
    /**
     * 获取规则配置的最大数量限制
     * 免费用户：5条，付费用户：10条
     */
    var maxRulesCount: Int {
        return subscriptionLevel == .free ? 5 : 10
    }

    /**
     * 是否为试用状态
     */
    var isTrialActive: Bool {
        guard let expirationDate = expirationDate else { return false }
        return Date() < expirationDate && subscriptionLevel != .free
    }

    /**
     * 是否已过期（包括试用过期）
     */
    var isExpired: Bool {
        guard let expirationDate = expirationDate else { return false }
        return Date() >= expirationDate
    }
    
    /**
     * 获取奖品配置的最大数量限制
     * 所有用户：10条
     */
    var maxPrizesCount: Int {
        return 10
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建免费订阅
     */
    @discardableResult
    static func createFreeSubscription(
        for user: User,
        in context: NSManagedObjectContext
    ) -> Subscription {
        let subscription = Subscription(context: context)
        subscription.level = Level.free.rawValue
        subscription.maxClasses = Int32(Level.free.maxClasses)
        subscription.updatedAt = Date()
        subscription.user = user
        return subscription
    }
    
    /**
     * 升级订阅
     */
    func upgrade(to newLevel: Level) {
        level = newLevel.rawValue
        maxClasses = Int32(newLevel.maxClasses)
        updatedAt = Date()
    }

    /**
     * 升级订阅并设置到期时间（用于试用）
     */
    func upgrade(to newLevel: Level, expirationDate: Date) {
        level = newLevel.rawValue
        maxClasses = Int32(newLevel.maxClasses)
        updatedAt = Date()
        self.expirationDate = expirationDate
    }
    
    /**
     * 检查功能权限
     */
    func hasPermission(for feature: Feature) -> Bool {
        switch feature {
        case .basicScoring:
            return true
        case .prizeExchange:
            return true
        case .lottery:
            return supportsLottery
        case .advancedGames:
            return supportsAdvancedGames
        case .aiAnalysis:
            return supportsAIAnalysis
        case .multiDeviceSync:
            return supportsMultiDeviceSync
        }
    }
    
    enum Feature {
        case basicScoring      // 基础加扣分
        case prizeExchange     // 奖品兑换
        case lottery          // 大转盘抽奖
        case advancedGames    // 盲盒/刮刮卡
        case aiAnalysis       // AI分析报告
        case multiDeviceSync  // 配置更多班级常用规则
    }
}

extension Subscription {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<Subscription> {
        return NSFetchRequest<Subscription>(entityName: "Subscription")
    }
    
    @NSManaged public var level: String?
    @NSManaged public var maxClasses: Int32
    @NSManaged public var updatedAt: Date?
    @NSManaged public var expirationDate: Date?
    @NSManaged public var hasReceivedTrial: Bool
    @NSManaged public var user: User?
    
} 
