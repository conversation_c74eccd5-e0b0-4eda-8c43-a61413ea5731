//
//  User+CoreDataClass.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData

@objc(User)
public class User: NSManagedObject {
    
    // MARK: - Computed Properties
    
    /**
     * 获取用户当前订阅等级
     */
    var subscriptionLevel: String {
        get {
            return subscription?.level ?? "free"
        }
        set {
            // 如果subscription不存在，创建一个新的
            if subscription == nil {
                subscription = Subscription.createFreeSubscription(for: self, in: managedObjectContext!)
            }
            subscription?.level = newValue
        }
    }
    
    /**
     * 获取可创建的最大班级数
     */
    var maxClassesAllowed: Int {
        return Int(subscription?.maxClasses ?? 1)
    }
    
    /**
     * 获取用户的班级数组（排序）
     */
    var sortedClasses: [SchoolClass] {
        guard let classesSet = classes as? Set<SchoolClass> else { return [] }
        return classesSet.sorted { $0.createdAt ?? Date() < $1.createdAt ?? Date() }
    }
    
    /**
     * 检查是否为付费用户
     */
    var isPremiumUser: Bool {
        return subscriptionLevel == "basic" || subscriptionLevel == "premium"
    }
    
    /**
     * 检查是否为高级用户
     */
    var isAdvancedUser: Bool {
        return subscriptionLevel == "premium"
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新用户的便利方法
     */
    @discardableResult
    static func create(
        nickname: String,
        email: String,
        in context: NSManagedObjectContext
    ) -> User {
        let user = User(context: context)
        user.id = UUID()
        user.nickname = nickname
        user.email = email
        user.createdAt = Date()
        
        // 创建默认免费订阅
        let subscription = Subscription.createFreeSubscription(for: user, in: context)
        user.subscription = subscription
        
        return user
    }
    
    /**
     * 检查是否可以创建更多班级
     */
    func canCreateMoreClasses() -> Bool {
        let currentClassCount = classes?.count ?? 0
        return currentClassCount < maxClassesAllowed
    }
    
    /**
     * 获取用户的第一个班级（默认班级）
     */
    func getDefaultClass() -> SchoolClass? {
        return sortedClasses.first
    }
    
    // MARK: - 时间范围偏好管理
    
    /**
     * 获取用户的日期范围偏好设置
     */
    func getDateRangePreference() -> DateRangeType {
        let typeString = preferredDateRangeType ?? "thisMonth"
        return DateRangeType.from(string: typeString, customStart: customStartDate, customEnd: customEndDate)
    }
    
    /**
     * 保存用户的日期范围偏好设置
     */
    func saveDateRangePreference(_ dateRange: DateRangeType) {
        preferredDateRangeType = dateRange.stringValue
        
        // 如果是自定义范围，保存具体日期
        if case .custom(let start, let end) = dateRange {
            customStartDate = start
            customEndDate = end
        } else {
            // 清除自定义日期
            customStartDate = nil
            customEndDate = nil
        }
        
        // 保存到CoreData
        do {
            try managedObjectContext?.save()
        } catch {
            print("保存日期范围偏好失败: \(error)")
        }
    }
    
    /**
     * 获取当前选择的日期范围
     */
    func getCurrentDateRange() -> (start: Date, end: Date) {
        return getDateRangePreference().dateRange
    }
    
    /**
     * 计算指定时间范围内全班加分总数
     */
    func calculateClassTotalScore(for dateRange: DateRangeType, in schoolClass: SchoolClass? = nil) -> Int {
        guard let targetClass = schoolClass ?? getDefaultClass(),
              let students = targetClass.students as? Set<Student> else {
            return 0
        }
        
        let range = dateRange.dateRange
        var totalScore = 0
        
        for student in students {
            if let pointRecords = student.pointRecords as? Set<PointRecord> {
                for record in pointRecords {
                    // 只统计正数加分记录，且在时间范围内
                    if record.value > 0,
                       !record.isReversed,
                       let timestamp = record.timestamp,
                       timestamp >= range.start && timestamp <= range.end {
                        totalScore += Int(record.value)
                    }
                }
            }
        }
        
        return totalScore
    }
    
    /**
     * 获取当前时间范围内的全班加分总数
     */
    func getCurrentRangeTotalScore(in schoolClass: SchoolClass? = nil) -> Int {
        let dateRange = getDateRangePreference()
        return calculateClassTotalScore(for: dateRange, in: schoolClass)
    }
}

extension User {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<User> {
        return NSFetchRequest<User>(entityName: "User")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var nickname: String?
    @NSManaged public var name: String?
    @NSManaged public var email: String?
    @NSManaged public var appleUserID: String?
    @NSManaged public var lastLoginAt: Date?
    @NSManaged public var createdAt: Date?
    @NSManaged public var preferredDateRangeType: String?
    @NSManaged public var customStartDate: Date?
    @NSManaged public var customEndDate: Date?
    @NSManaged public var subscription: Subscription?
    @NSManaged public var classes: NSSet?
    
}

// MARK: Generated accessors for classes
extension User {
    
    @objc(addClassesObject:)
    @NSManaged public func addToClasses(_ value: SchoolClass)
    
    @objc(removeClassesObject:)
    @NSManaged public func removeFromClasses(_ value: SchoolClass)
    
    @objc(addClasses:)
    @NSManaged public func addToClasses(_ values: NSSet)
    
    @objc(removeClasses:)
    @NSManaged public func removeFromClasses(_ values: NSSet)
    
} 