//
//  Rule+CoreDataClass.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData

@objc(Rule)
public class Rule: NSManagedObject {
    
    // MARK: - Enums
    
    enum RuleType: String, CaseIterable {
        case add = "add"
        case deduct = "deduct"
        
        var displayName: String {
            switch self {
            case .add:
                return "加分"
            case .deduct:
                return "扣分"
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取规则类型枚举
     */
    var ruleType: RuleType {
        return RuleType(rawValue: type ?? "add") ?? .add
    }
    
    /**
     * 是否为加分规则
     */
    var isAddRule: Bool {
        return ruleType == .add
    }
    
    /**
     * 是否为扣分规则
     */
    var isDeductRule: Bool {
        return ruleType == .deduct
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新规则的便利方法
     */
    @discardableResult
    static func create(
        name: String,
        value: Int,
        type: String,
        isFrequent: Bool = false,
        in schoolClass: SchoolClass,
        context: NSManagedObjectContext
    ) -> Rule {
        let rule = Rule(context: context)
        rule.id = UUID()
        rule.name = name
        rule.value = Int32(value)
        rule.type = type
        rule.isFrequent = isFrequent
        rule.schoolClass = schoolClass
        return rule
    }
}

extension Rule {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<Rule> {
        return NSFetchRequest<Rule>(entityName: "Rule")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var value: Int32
    @NSManaged public var type: String?
    @NSManaged public var isFrequent: Bool
    @NSManaged public var schoolClass: SchoolClass?
    
} 