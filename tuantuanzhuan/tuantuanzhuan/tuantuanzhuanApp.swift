//
//  tuantuanzhuanApp.swift
//  tuantuanzhuan
//
//  Created by rainkygong on 2025/6/23.
//

import SwiftUI
import RevenueCat

@main
struct tuantuanzhuanApp: App {
    
    // MARK: - Properties
    
    @StateObject private var coreDataManager = CoreDataManager.shared
    @StateObject private var revenueCatManager = RevenueCatManager.shared
    @StateObject private var trialManager = TrialManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    @State private var isInitializing = true
    @State private var showMigrationAlert = false
    @State private var migrationMessage = ""
    
    // MARK: - Initialization
    
    init() {
        print("🚀 应用启动 - 统一CloudKit多设备同步模式")
        
        // 执行待处理的数据迁移
        CoreDataManager.performPendingMigrationIfNeeded()
        
        // 配置RevenueCat
        configureRevenueCat()
    }
    
    // MARK: - App Scene
    
    var body: some Scene {
        WindowGroup {
            Group {
                if isInitializing {
                    LaunchScreenView()
                        .onAppear {
                            initializeApp()
                        }
                } else {
                    ContentView()
                        .environment(\.managedObjectContext, coreDataManager.viewContext)
                        .environmentObject(coreDataManager)
                        .environmentObject(revenueCatManager)
                        .environmentObject(trialManager)
                        .environmentObject(authManager)
                }
            }
            .alert("数据迁移", isPresented: $showMigrationAlert) {
                Button("知道了") {
                    showMigrationAlert = false
                }
            } message: {
                Text(migrationMessage)
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ShowCloudKitUpgradeAlert"))) { _ in
                showCloudKitUpgradeAlert()
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ShowLocalDowngradeAlert"))) { _ in
                showLocalDowngradeAlert()
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 配置RevenueCat
     */
    private func configureRevenueCat() {
        // TODO: 请将此API Key替换为您从RevenueCat Dashboard获取的真实API Key
        let apiKey = "appl_GzPHLwaDLPnAcusWETDnYhSfJDv"
        
        // 获取当前用户ID (如果已登录)
        let userId = AuthenticationManager.shared.currentUser?.appleUserID
        
        // 配置RevenueCat
        Task { @MainActor in
            RevenueCatManager.shared.configure(apiKey: apiKey, userId: userId)
        }
        
        print("🔧 RevenueCat配置完成")
    }
    
    /**
     * 初始化应用
     */
    private func initializeApp() {
        // 立即开始登录状态检查，避免在ContentView中再次触发
        authManager.checkLoginStatus()

        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { // 增加时间让Logo旋转动画完成
            // 刷新试用状态，确保状态正确
            print("🔄 刷新试用状态...")
            trialManager.refreshTrialStatus()

            // 执行数据一致性检查，确保用户状态正确
            print("🔍 开始应用启动数据一致性检查...")
            coreDataManager.performDataConsistencyCheck()

            print("✅ 应用初始化完成 - CloudKit同步已启用")
            isInitializing = false
        }
    }
    
    /**
     * 显示CloudKit升级提示
     */
    private func showCloudKitUpgradeAlert() {
        migrationMessage = "您已升级到付费会员，现在可以享受多设备数据同步功能！应用将在下次启动时启用CloudKit同步。"
        showMigrationAlert = true
    }
    
    /**
     * 显示本地降级提示
     */
    private func showLocalDowngradeAlert() {
        migrationMessage = "您的会员已到期，多设备同步功能将被禁用。您的数据仍将保存在本地设备上。"
        showMigrationAlert = true
    }
}

// MARK: - Launch Screen View

/**
 * 启动屏幕视图 - 全新设计
 */
struct LaunchScreenView: View {
    @State private var logoScale: CGFloat = 0.6
    @State private var logoOpacity: Double = 0.0
    @State private var titleScale: CGFloat = 0.8
    @State private var titleOpacity: Double = 0.0
    @State private var subtitleOffset: CGFloat = 50
    @State private var subtitleOpacity: Double = 0.0
    @State private var progressOpacity: Double = 0.0
    @State private var particlesOpacity: Double = 0.0
    @State private var backgroundScale: CGFloat = 1.2

    var body: some View {
        ZStack {
            // 动态背景渐变
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#E8F9C5").opacity(0.8),
                    Color(hex: "#f8fdf0"),
                    Color(hex: "#B5E36B").opacity(0.3)
                ]),
                center: .center,
                startRadius: 100,
                endRadius: 400
            )
            .scaleEffect(backgroundScale)
            .ignoresSafeArea()

            // 浮动装饰元素
            GeometryReader { geometry in
                // 教育主题装饰元素
                ForEach(0..<6, id: \.self) { index in
                    FloatingEducationElement(
                        index: index,
                        geometry: geometry,
                        opacity: particlesOpacity
                    )
                }
            }

            // 主要内容区域
            VStack(spacing: 0) {
                Spacer()

                // Logo和标题区域
                VStack(spacing: 40) {
                    // 主Logo区域
                    ZStack {
                        // 外层光晕效果
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#B5E36B").opacity(0.4),
                                        Color(hex: "#B5E36B").opacity(0.2),
                                        Color.clear
                                    ]),
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 120
                                )
                            )
                            .frame(width: 240, height: 240)
                            .scaleEffect(logoScale * 1.3)
                            .opacity(logoOpacity * 0.8)

                        // 中层背景圆
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.95),
                                        Color(hex: "#E8F9C5").opacity(0.9)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 160, height: 160)
                            .shadow(color: Color(hex: "#B5E36B").opacity(0.3), radius: 30, x: 0, y: 20)
                            .scaleEffect(logoScale)

                        // Logo图片
                        Image("logo")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 90, height: 90)
                            .scaleEffect(logoScale)
                    }
                    .opacity(logoOpacity)

                    // 副标题
                    Text("让学习变得更有趣")
                        .font(.system(size: 24, weight: .medium, design: .rounded))
                        .foregroundColor(Color(hex: "#999999"))
                        .opacity(subtitleOpacity)
                        .offset(y: subtitleOffset)
                }

                Spacer()

                // 底部加载区域
                VStack(spacing: 24) {
                    // 现代化加载指示器
                    HStack(spacing: 12) {
                        ForEach(0..<3) { index in
                            RoundedRectangle(cornerRadius: 6)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(hex: "#B5E36B"),
                                            Color(hex: "#87C441")
                                        ]),
                                        startPoint: .top,
                                        endPoint: .bottom
                                    )
                                )
                                .frame(width: 8, height: 24)
                                .scaleEffect(y: logoScale)
                                .animation(
                                    .easeInOut(duration: 0.8)
                                    .repeatForever(autoreverses: true)
                                    .delay(Double(index) * 0.3),
                                    value: logoScale
                                )
                        }
                    }
                    .opacity(progressOpacity)

                    // 初始化状态提示
                    Text("正在初始化应用...")
                        .font(.system(size: 16, weight: .medium, design: .rounded))
                        .foregroundColor(Color(hex: "#999999"))
                        .opacity(progressOpacity)
                }
                .padding(.bottom, 80)
            }
        }
        .onAppear {
            startAnimationSequence()
        }
    }

    // MARK: - Animation Sequence
    private func startAnimationSequence() {
        // 背景动画
        withAnimation(.easeInOut(duration: 2.0)) {
            backgroundScale = 1.0
        }

        // Logo出现动画
        withAnimation(.spring(response: 1.2, dampingFraction: 0.8).delay(0.2)) {
            logoScale = 1.0
            logoOpacity = 1.0
        }

        // 副标题出现动画 - 调整到0.8s开始
        withAnimation(.easeOut(duration: 0.8).delay(0.8)) {
            subtitleOffset = 0
            subtitleOpacity = 1.0
        }

        // 装饰元素出现动画
        withAnimation(.easeOut(duration: 1.0).delay(0.6)) {
            particlesOpacity = 1.0
        }

        // 加载指示器出现动画
        withAnimation(.easeOut(duration: 0.6).delay(1.2)) {
            progressOpacity = 1.0
        }
    }
}

// MARK: - Floating Education Element

/**
 * 浮动教育主题装饰元素
 */
struct FloatingEducationElement: View {
    let index: Int
    let geometry: GeometryProxy
    let opacity: Double

    @State private var offset: CGSize = .zero
    @State private var rotation: Double = 0
    @State private var scale: CGFloat = 1.0

    private var educationSymbols: [String] {
        ["📚", "✏️", "🎯", "⭐", "🏆", "🎨"]
    }

    private var colors: [Color] {
        [
            Color(hex: "#B5E36B").opacity(0.6),
            Color(hex: "#FFE49E").opacity(0.6),
            Color(hex: "#87C441").opacity(0.6),
            Color(hex: "#E8F9C5").opacity(0.8),
            Color.white.opacity(0.7),
            Color(hex: "#B5E36B").opacity(0.4)
        ]
    }

    var body: some View {
        ZStack {
            // 背景圆形
            Circle()
                .fill(colors[index % colors.count])
                .frame(width: elementSize, height: elementSize)
                .blur(radius: 2)

            // 教育符号
            Text(educationSymbols[index % educationSymbols.count])
                .font(.system(size: elementSize * 0.4))
                .rotationEffect(.degrees(rotation))
        }
        .scaleEffect(scale)
        .offset(offset)
        .opacity(opacity)
        .position(initialPosition)
        .onAppear {
            startFloatingAnimation()
        }
    }

    private var elementSize: CGFloat {
        let sizes: [CGFloat] = [40, 50, 35, 45, 38, 42]
        return sizes[index % sizes.count]
    }

    private var initialPosition: CGPoint {
        let positions: [(CGFloat, CGFloat)] = [
            (0.15, 0.2),   // 左上
            (0.85, 0.25),  // 右上
            (0.1, 0.7),    // 左下
            (0.9, 0.75),   // 右下
            (0.2, 0.45),   // 左中
            (0.8, 0.55)    // 右中
        ]
        let pos = positions[index % positions.count]
        return CGPoint(
            x: geometry.size.width * pos.0,
            y: geometry.size.height * pos.1
        )
    }

    private func startFloatingAnimation() {
        let duration = Double.random(in: 3.0...6.0)
        let delay = Double(index) * 0.3

        // 浮动动画
        withAnimation(
            .easeInOut(duration: duration)
            .repeatForever(autoreverses: true)
            .delay(delay)
        ) {
            offset = CGSize(
                width: Double.random(in: -30...30),
                height: Double.random(in: -40...40)
            )
        }

        // 旋转动画
        withAnimation(
            .linear(duration: duration * 2)
            .repeatForever(autoreverses: false)
            .delay(delay)
        ) {
            rotation = 360
        }

        // 缩放动画
        withAnimation(
            .easeInOut(duration: duration * 0.8)
            .repeatForever(autoreverses: true)
            .delay(delay + 0.5)
        ) {
            scale = Double.random(in: 0.8...1.2)
        }
    }
}
