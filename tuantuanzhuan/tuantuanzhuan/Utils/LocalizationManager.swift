//
//  LocalizationManager.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import SwiftUI

/**
 * 支持的语言类型
 */
enum SupportedLanguage: String, CaseIterable {
    case chinese = "zh-Hans"
    case english = "en"
    
    var displayName: String {
        switch self {
        case .chinese:
            return "中文"
        case .english:
            return "English"
        }
    }
    
    var nativeDisplayName: String {
        switch self {
        case .chinese:
            return "中文"
        case .english:
            return "English"
        }
    }
}

/**
 * 本地化管理器
 * 负责应用的多语言支持和语言切换功能
 */
class LocalizationManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = LocalizationManager()
    
    // MARK: - Properties
    @Published var currentLanguage: SupportedLanguage
    
    // MARK: - Constants
    private let languageKey = "AppSelectedLanguage"
    
    // MARK: - Initialization
    private init() {
        // 从UserDefaults读取保存的语言设置，默认为中文
        if let savedLanguageCode = UserDefaults.standard.string(forKey: languageKey),
           let savedLanguage = SupportedLanguage(rawValue: savedLanguageCode) {
            self.currentLanguage = savedLanguage
        } else {
            // 根据系统语言自动选择默认语言
            let systemLanguage = Locale.preferredLanguages.first ?? "zh-Hans"
            if systemLanguage.hasPrefix("en") {
                self.currentLanguage = .english
            } else {
                self.currentLanguage = .chinese
            }
        }
        
        // 设置应用的语言环境
        setAppLanguage(currentLanguage)
    }
    
    // MARK: - Public Methods
    
    /**
     * 切换应用语言
     * @param language 目标语言
     */
    func changeLanguage(to language: SupportedLanguage) {
        guard language != currentLanguage else { return }
        
        // 保存语言选择到UserDefaults
        UserDefaults.standard.set(language.rawValue, forKey: languageKey)
        UserDefaults.standard.synchronize()
        
        // 更新当前语言
        currentLanguage = language
        
        // 设置应用语言环境
        setAppLanguage(language)
        
        // 显示重启提示并退出应用
        showRestartAlert()
    }
    
    /**
     * 获取本地化字符串
     * @param key 本地化键值
     * @param arguments 字符串参数（用于格式化）
     * @return 本地化后的字符串
     */
    func localizedString(for key: String, arguments: CVarArg...) -> String {
        let localizedString = NSLocalizedString(key, comment: "")
        
        if arguments.isEmpty {
            return localizedString
        } else {
            return String(format: localizedString, arguments: arguments)
        }
    }
    
    /**
     * 检查是否为当前语言
     * @param language 要检查的语言
     * @return 是否为当前语言
     */
    func isCurrentLanguage(_ language: SupportedLanguage) -> Bool {
        return currentLanguage == language
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置应用语言环境
     * @param language 目标语言
     */
    private func setAppLanguage(_ language: SupportedLanguage) {
        // 设置NSLocalizedString的语言环境
        UserDefaults.standard.set([language.rawValue], forKey: "AppleLanguages")
        UserDefaults.standard.synchronize()
        
        print("应用语言已设置为: \(language.displayName)")
    }
    
    /**
     * 显示重启提示并退出应用
     */
    private func showRestartAlert() {
        DispatchQueue.main.async {
            let alert = UIAlertController(
                title: self.localizedString(for: "language.restart.title"),
                message: self.localizedString(for: "language.restart.message"),
                preferredStyle: .alert
            )
            
            let confirmAction = UIAlertAction(
                title: self.localizedString(for: "common.button.confirm"),
                style: .default
            ) { _ in
                // 延迟退出，确保用户看到确认
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    exit(0)
                }
            }
            
            alert.addAction(confirmAction)
            
            // 获取当前窗口并显示警告
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first {
                window.rootViewController?.present(alert, animated: true)
            }
        }
    }
} 