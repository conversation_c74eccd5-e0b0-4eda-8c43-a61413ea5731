//
//  KeychainManager.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation
import Security

/**
 * Keychain管理工具
 * 提供安全的数据存储和检索功能，替代UserDefaults存储敏感信息
 */
class KeychainManager {
    
    // MARK: - Singleton
    static let shared = KeychainManager()
    private init() {}
    
    // MARK: - Constants
    private enum KeychainKeys {
        static let serviceName = "com.rainkygong.tuantuanzhuan"
        static let appleUserID = "apple_user_id"
        static let userName = "user_name"
        static let userEmail = "user_email"
        static let isLoggedIn = "is_logged_in"
    }
    
    // MARK: - Public Methods
    
    /**
     * 保存用户登录信息到Keychain
     */
    func saveLoginInfo(appleUserID: String, userName: String? = nil, userEmail: String? = nil) {
        // 保存Apple用户ID
        save(key: KeychainKeys.appleUserID, value: appleUserID)
        
        // 保存用户名
        if let userName = userName {
            save(key: KeychainKeys.userName, value: userName)
        }
        
        // 保存邮箱
        if let userEmail = userEmail {
            save(key: KeychainKeys.userEmail, value: userEmail)
        }
        
        // 保存登录状态
        save(key: KeychainKeys.isLoggedIn, value: "true")
        
        print("🔐 用户登录信息已保存到Keychain")
    }
    
    /**
     * 获取Apple用户ID
     */
    func getAppleUserID() -> String? {
        return getString(key: KeychainKeys.appleUserID)
    }
    
    /**
     * 获取用户名
     */
    func getUserName() -> String? {
        return getString(key: KeychainKeys.userName)
    }
    
    /**
     * 获取用户邮箱
     */
    func getUserEmail() -> String? {
        return getString(key: KeychainKeys.userEmail)
    }
    
    /**
     * 获取登录状态
     */
    func isLoggedIn() -> Bool {
        return getString(key: KeychainKeys.isLoggedIn) == "true"
    }
    
    /**
     * 清除所有登录信息
     */
    func clearLoginInfo() {
        delete(key: KeychainKeys.appleUserID)
        delete(key: KeychainKeys.userName)
        delete(key: KeychainKeys.userEmail)
        delete(key: KeychainKeys.isLoggedIn)
        
        print("🗑️ 用户登录信息已从Keychain中清除")
    }
    
    /**
     * 更新用户名
     */
    func updateUserName(_ userName: String) {
        save(key: KeychainKeys.userName, value: userName)
    }
    
    /**
     * 更新用户邮箱
     */
    func updateUserEmail(_ userEmail: String) {
        save(key: KeychainKeys.userEmail, value: userEmail)
    }
    
    // MARK: - Private Methods
    
    /**
     * 保存字符串到Keychain
     */
    private func save(key: String, value: String) {
        guard let data = value.data(using: .utf8) else {
            print("❌ 无法将字符串转换为Data: \(value)")
            return
        }
        
        // 删除现有项目（如果存在）
        delete(key: key)
        
        // 创建查询字典
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: KeychainKeys.serviceName,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        // 添加到Keychain
        let status = SecItemAdd(query as CFDictionary, nil)
        
        if status == errSecSuccess {
            print("✅ 成功保存到Keychain: \(key)")
        } else {
            print("❌ 保存到Keychain失败: \(key), 状态: \(status)")
        }
    }
    
    /**
     * 从Keychain获取字符串
     */
    private func getString(key: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: KeychainKeys.serviceName,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess,
           let data = result as? Data,
           let string = String(data: data, encoding: .utf8) {
            return string
        } else if status != errSecItemNotFound {
            print("❌ 从Keychain读取失败: \(key), 状态: \(status)")
        }
        
        return nil
    }
    
    /**
     * 从Keychain删除项目
     */
    private func delete(key: String) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: KeychainKeys.serviceName,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        if status == errSecSuccess {
            print("🗑️ 成功从Keychain删除: \(key)")
        } else if status != errSecItemNotFound {
            print("❌ 从Keychain删除失败: \(key), 状态: \(status)")
        }
    }
} 