//
//  ExcelParser.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation

/**
 * Excel/CSV数据解析工具类
 * 用于解析导入的学生数据文件并转换为StudentFormData对象
 */
class ExcelParser {
    
    // MARK: - Public Methods
    
    /**
     * 解析CSV/Excel数据
     * @param data 文件数据
     * @return 解析结果，包含学生数据或错误信息
     */
    static func parseExcelData(_ data: Data) -> Result<[StudentFormData], ExcelParseError> {
        print("📄 开始解析Excel数据，文件大小: \(data.count) bytes")

        // 尝试多种编码格式解析数据
        var content: String?
        var usedEncoding: String = ""

        // 首先尝试UTF-8编码
        content = String(data: data, encoding: .utf8)
        if content != nil {
            usedEncoding = "UTF-8"
        }

        // 如果UTF-8失败，尝试UTF-16编码
        if content == nil {
            content = String(data: data, encoding: .utf16)
            if content != nil {
                usedEncoding = "UTF-16"
            }
        }

        // 如果UTF-16也失败，尝试GB18030编码（中文常用编码）
        if content == nil {
            let gb18030Encoding = CFStringEncoding(CFStringEncodings.GB_18030_2000.rawValue)
            let nsEncoding = CFStringConvertEncodingToNSStringEncoding(gb18030Encoding)
            content = String(data: data, encoding: String.Encoding(rawValue: nsEncoding))
            if content != nil {
                usedEncoding = "GB18030"
            }
        }

        // 如果所有编码都失败，尝试GBK编码
        if content == nil {
            let gbkEncoding = CFStringEncoding(CFStringEncodings.dosChineseSimplif.rawValue)
            let nsEncoding = CFStringConvertEncodingToNSStringEncoding(gbkEncoding)
            content = String(data: data, encoding: String.Encoding(rawValue: nsEncoding))
            if content != nil {
                usedEncoding = "GBK"
            }
        }

        guard let finalContent = content else {
            print("❌ 文件编码解析失败，尝试了UTF-8、UTF-16、GB18030、GBK编码")
            return .failure(.invalidEncoding)
        }

        print("✅ 文件编码解析成功，使用编码: \(usedEncoding)")
        print("📝 文件内容预览: \(String(finalContent.prefix(200)))")

        return parseCSVContent(finalContent)
    }
    
    /**
     * 验证Excel文件格式
     * @param data 文件数据
     * @return 是否为有效格式
     */
    static func validateExcelFormat(_ data: Data) -> Bool {
        // 尝试多种编码格式解析数据
        var content: String?
        
        // 首先尝试UTF-8编码
        content = String(data: data, encoding: .utf8)
        
        // 如果UTF-8失败，尝试UTF-16编码
        if content == nil {
            content = String(data: data, encoding: .utf16)
        }
        
        // 如果UTF-16也失败，尝试GB18030编码（中文常用编码）
        if content == nil {
            let gb18030Encoding = CFStringEncoding(CFStringEncodings.GB_18030_2000.rawValue)
            let nsEncoding = CFStringConvertEncodingToNSStringEncoding(gb18030Encoding)
            content = String(data: data, encoding: String.Encoding(rawValue: nsEncoding))
        }
        
        // 如果所有编码都失败，尝试GBK编码
        if content == nil {
            let gbkEncoding = CFStringEncoding(CFStringEncodings.dosChineseSimplif.rawValue)
            let nsEncoding = CFStringConvertEncodingToNSStringEncoding(gbkEncoding)
            content = String(data: data, encoding: String.Encoding(rawValue: nsEncoding))
        }
        
        guard let finalContent = content else {
            return false
        }
        
        let lines = finalContent.components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        
        // 至少需要标题行和一行数据
        return lines.count >= 2
    }
    
    /**
     * 获取CSV模板数据
     * @return CSV格式的模板字符串
     */
    static func getCSVTemplate() -> String {
        let header = "姓名,学号,性别,初始积分"
        let example1 = "张三,001,男,0"
        let example2 = "李四,002,女,10"
        
        return "\(header)\n\(example1)\n\(example2)"
    }
    
    // MARK: - Private Methods
    
    /**
     * 解析CSV内容
     * @param content CSV文本内容
     * @return 解析结果
     */
    private static func parseCSVContent(_ content: String) -> Result<[StudentFormData], ExcelParseError> {
        let lines = content.components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        
        guard !lines.isEmpty else {
            return .failure(.emptyFile)
        }
        
        // 解析标题行
        let headerLine = lines[0]
        let headers = parseCSVLine(headerLine)
        
        guard validateHeaders(headers) else {
            return .failure(.invalidHeaders(expected: getExpectedHeaders(), found: headers))
        }
        
        // 解析数据行
        var students: [StudentFormData] = []
        var errors: [String] = []
        
        for (index, line) in lines.dropFirst().enumerated() {
            let rowNumber = index + 2 // 从第二行开始，索引从1开始
            
            let fields = parseCSVLine(line)
            
            guard fields.count >= 4 else {
                errors.append("excel.parse.error.insufficient_fields".localized(with: "\(rowNumber)", "\(fields.count)"))
                continue
            }
            
            let name = fields[0].trimmingCharacters(in: .whitespacesAndNewlines)
            let studentNumber = fields[1].trimmingCharacters(in: .whitespacesAndNewlines)
            let genderString = fields[2].trimmingCharacters(in: .whitespacesAndNewlines)
            let pointsString = fields[3].trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 验证和转换数据
            let gender = normalizeGender(genderString)
            let initialPoints = normalizePoints(pointsString)
            
            // 创建学生数据
            var student = StudentFormData()
            student.name = name
            student.studentNumber = studentNumber
            student.gender = gender
            student.initialPoints = initialPoints

            print("📊 解析学生数据 - 姓名: \(name), 学号: \(studentNumber), 性别: \(gender), 积分字符串: '\(pointsString)', 积分值: \(student.initialPointsValue)")

            // 验证学生数据
            let validation = student.validateAll()
            if case .invalid(let message) = validation {
                errors.append("excel.parse.error.invalid_data".localized(with: "\(rowNumber)", message))
                continue
            }
            
            students.append(student)
        }
        
        if students.isEmpty {
            if errors.isEmpty {
                return .failure(.noValidData)
            } else {
                return .failure(.dataValidationErrors(errors))
            }
        }
        
        // 检查学号重复
        let duplicateCheck = checkForDuplicateStudentNumbers(students)
        if case .failure(let duplicateError) = duplicateCheck {
            return .failure(duplicateError)
        }
        
        return .success(students)
    }
    
    /**
     * 解析CSV行，处理逗号分隔的字段
     * @param line CSV行内容
     * @return 字段数组
     */
    private static func parseCSVLine(_ line: String) -> [String] {
        var fields: [String] = []
        var currentField = ""
        var inQuotes = false
        var i = line.startIndex
        
        while i < line.endIndex {
            let char = line[i]
            
            if char == "\"" {
                inQuotes.toggle()
            } else if char == "," && !inQuotes {
                fields.append(currentField)
                currentField = ""
            } else {
                currentField.append(char)
            }
            
            i = line.index(after: i)
        }
        
        // 添加最后一个字段
        fields.append(currentField)
        
        return fields.map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
    }
    
    /**
     * 验证CSV标题行
     * @param headers 标题数组
     * @return 是否有效
     */
    private static func validateHeaders(_ headers: [String]) -> Bool {
        let expectedHeaders = getExpectedHeaders()
        
        guard headers.count >= expectedHeaders.count else {
            return false
        }
        
        // 检查必需的列是否存在（支持不同的列名变体）
        let nameVariants = ["姓名", "名字", "学生姓名", "name"]
        let numberVariants = ["学号", "学生号", "编号", "number", "student_number"]
        let genderVariants = ["性别", "gender"]
        let pointsVariants = ["初始积分", "积分", "分数", "points", "initial_points"]
        
        let hasName = headers.contains { header in
            nameVariants.contains { variant in
                header.lowercased().contains(variant.lowercased())
            }
        }
        
        let hasNumber = headers.contains { header in
            numberVariants.contains { variant in
                header.lowercased().contains(variant.lowercased())
            }
        }
        
        let hasGender = headers.contains { header in
            genderVariants.contains { variant in
                header.lowercased().contains(variant.lowercased())
            }
        }
        
        let hasPoints = headers.contains { header in
            pointsVariants.contains { variant in
                header.lowercased().contains(variant.lowercased())
            }
        }
        
        return hasName && hasNumber && hasGender && hasPoints
    }
    
    /**
     * 获取期望的标题行
     * @return 期望的标题数组
     */
    private static func getExpectedHeaders() -> [String] {
        return ["姓名", "学号", "性别", "初始积分"]
    }
    
    /**
     * 标准化性别字段
     * @param genderString 原始性别字符串
     * @return 标准化的性别值
     */
    private static func normalizeGender(_ genderString: String) -> String {
        let lowercased = genderString.lowercased()
        
        if lowercased.contains("男") || lowercased.contains("male") || lowercased == "m" {
            return "male"
        } else if lowercased.contains("女") || lowercased.contains("female") || lowercased == "f" {
            return "female"
        } else {
            return "male" // 默认值
        }
    }
    
    /**
     * 标准化积分字段
     * @param pointsString 原始积分字符串
     * @return 标准化的积分字符串
     */
    private static func normalizePoints(_ pointsString: String) -> String {
        let trimmed = pointsString.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if trimmed.isEmpty {
            return "0"
        }
        
        // 尝试解析为整数
        if let points = Int(trimmed), points >= 0 {
            return String(points)
        }
        
        return "0" // 无效值默认为0
    }
    
    /**
     * 检查学号重复
     * @param students 学生数据数组
     * @return 检查结果
     */
    private static func checkForDuplicateStudentNumbers(_ students: [StudentFormData]) -> Result<Void, ExcelParseError> {
        var seenNumbers = Set<String>()
        var duplicates = Set<String>()
        
        for student in students {
            let number = student.formattedStudentNumber
            if !number.isEmpty {
                if seenNumbers.contains(number) {
                    duplicates.insert(number)
                } else {
                    seenNumbers.insert(number)
                }
            }
        }
        
        if !duplicates.isEmpty {
            return .failure(.duplicateStudentNumbers(Array(duplicates)))
        }
        
        return .success(())
    }
}

// MARK: - Error Types

/**
 * Excel解析错误枚举
 */
enum ExcelParseError: Error, LocalizedError {
    case invalidEncoding
    case emptyFile
    case invalidHeaders(expected: [String], found: [String])
    case noValidData
    case dataValidationErrors([String])
    case duplicateStudentNumbers([String])
    
    var errorDescription: String? {
        switch self {
        case .invalidEncoding:
            return "excel.parse.error.invalid_encoding".localized
        case .emptyFile:
            return "excel.parse.error.empty_file".localized
        case .invalidHeaders(let expected, let found):
            return "excel.parse.error.invalid_headers".localized(with: expected.joined(separator: ", "), found.joined(separator: ", "))
        case .noValidData:
            return "excel.parse.error.no_valid_data".localized
        case .dataValidationErrors(let errors):
            return "excel.parse.error.validation_errors".localized(with: errors.joined(separator: "\n"))
        case .duplicateStudentNumbers(let numbers):
            return "excel.parse.error.duplicate_numbers".localized(with: numbers.joined(separator: ", "))
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .invalidEncoding:
            return "excel.parse.recovery.invalid_encoding".localized
        case .emptyFile:
            return "excel.parse.recovery.empty_file".localized
        case .invalidHeaders:
            return "excel.parse.recovery.invalid_headers".localized
        case .noValidData:
            return "excel.parse.recovery.no_valid_data".localized
        case .dataValidationErrors:
            return "excel.parse.recovery.validation_errors".localized
        case .duplicateStudentNumbers:
            return "excel.parse.recovery.duplicate_numbers".localized
        }
    }
} 