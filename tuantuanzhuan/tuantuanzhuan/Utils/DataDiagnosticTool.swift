//
//  DataDiagnosticTool.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/20.
//

import Foundation
import CoreData
import CloudKit

/**
 * 数据诊断工具
 * 提供全面的数据分析、报告和自动修复功能
 */
class DataDiagnosticTool {
    
    // MARK: - Singleton
    static let shared = DataDiagnosticTool()
    private init() {}
    
    // MARK: - Properties
    private let coreDataManager = CoreDataManager.shared
    
    // MARK: - 诊断报告结构
    struct DiagnosticReport {
        let timestamp: Date
        let userAnalysis: UserAnalysis
        let classAnalysis: ClassAnalysis
        let studentAnalysis: StudentAnalysis
        let cloudKitStatus: CloudKitAnalysis
        let consistencyIssues: [ConsistencyIssue]
        let recommendations: [String]
        
        var summary: String {
            let issues = consistencyIssues.count
            let status = issues == 0 ? "✅ 数据一致性良好" : "⚠️ 发现 \(issues) 个问题"
            
            return """
            📊 数据诊断报告 - \(DateFormatter.shortDateTime.string(from: timestamp))
            
            \(status)
            
            👤 用户数据: \(userAnalysis.totalUsers) 个用户
            🏫 班级数据: \(classAnalysis.totalClasses) 个班级
            👨‍🎓 学生数据: \(studentAnalysis.totalStudents) 个学生
            ☁️ CloudKit: \(cloudKitStatus.statusText)
            """
        }
    }
    
    struct UserAnalysis {
        let totalUsers: Int
        let usersWithAppleID: Int
        let usersWithoutAppleID: Int
        let duplicateAppleIDs: [String]
        let usersWithoutSubscription: Int
    }
    
    struct ClassAnalysis {
        let totalClasses: Int
        let orphanedClasses: Int
        let activeClasses: Int
        let classesPerUser: [String: Int]
    }
    
    struct StudentAnalysis {
        let totalStudents: Int
        let studentsWithoutClass: Int
        let studentsPerClass: [String: Int]
    }
    
    struct CloudKitAnalysis {
        let isEnabled: Bool
        let accountStatus: String
        let lastSyncDate: Date?
        let syncStatus: String
        
        var statusText: String {
            if isEnabled {
                if let lastSync = lastSyncDate {
                    return "已启用 (上次同步: \(DateFormatter.shortDateTime.string(from: lastSync)))"
                } else {
                    return "已启用 (未同步)"
                }
            } else {
                return "未启用"
            }
        }
    }
    
    struct ConsistencyIssue {
        let type: IssueType
        let description: String
        let severity: Severity
        let affectedEntities: [String]
        let autoFixable: Bool
        
        enum IssueType {
            case duplicateUsers
            case orphanedData
            case missingSubscription
            case inconsistentUserData
            case cloudKitSyncIssue
        }
        
        enum Severity {
            case low, medium, high, critical
            
            var emoji: String {
                switch self {
                case .low: return "🟡"
                case .medium: return "🟠"
                case .high: return "🔴"
                case .critical: return "💥"
                }
            }
        }
    }
    
    // MARK: - 公共方法
    
    /**
     * 执行完整的数据诊断
     */
    func performFullDiagnostic() -> DiagnosticReport {
        print("🔍 开始完整数据诊断...")
        
        let userAnalysis = analyzeUsers()
        let classAnalysis = analyzeClasses()
        let studentAnalysis = analyzeStudents()
        let cloudKitStatus = analyzeCloudKitStatus()
        let consistencyIssues = detectConsistencyIssues()
        let recommendations = generateRecommendations(issues: consistencyIssues)
        
        let report = DiagnosticReport(
            timestamp: Date(),
            userAnalysis: userAnalysis,
            classAnalysis: classAnalysis,
            studentAnalysis: studentAnalysis,
            cloudKitStatus: cloudKitStatus,
            consistencyIssues: consistencyIssues,
            recommendations: recommendations
        )
        
        print("✅ 数据诊断完成，发现 \(consistencyIssues.count) 个问题")
        
        return report
    }
    
    /**
     * 自动修复可修复的问题
     */
    func autoFixIssues(_ issues: [ConsistencyIssue]) -> (fixed: Int, failed: Int) {
        var fixedCount = 0
        var failedCount = 0
        
        for issue in issues where issue.autoFixable {
            do {
                try fixIssue(issue)
                fixedCount += 1
                print("✅ 已修复: \(issue.description)")
            } catch {
                failedCount += 1
                print("❌ 修复失败: \(issue.description) - \(error)")
            }
        }
        
        // 保存修复结果
        coreDataManager.save()
        
        return (fixed: fixedCount, failed: failedCount)
    }
    
    /**
     * 生成详细报告
     */
    func generateDetailedReport(_ report: DiagnosticReport) -> String {
        var output = [String]()
        
        output.append("📋 团团转数据诊断详细报告")
        output.append("生成时间: \(DateFormatter.fullDateTime.string(from: report.timestamp))")
        output.append("")
        
        // 用户分析
        output.append("👤 用户数据分析")
        output.append("• 总用户数: \(report.userAnalysis.totalUsers)")
        output.append("• 已绑定Apple ID: \(report.userAnalysis.usersWithAppleID)")
        output.append("• 未绑定Apple ID: \(report.userAnalysis.usersWithoutAppleID)")
        output.append("• 缺少订阅信息: \(report.userAnalysis.usersWithoutSubscription)")
        if !report.userAnalysis.duplicateAppleIDs.isEmpty {
            output.append("• 重复Apple ID: \(report.userAnalysis.duplicateAppleIDs.joined(separator: ", "))")
        }
        output.append("")
        
        // 班级分析
        output.append("🏫 班级数据分析")
        output.append("• 总班级数: \(report.classAnalysis.totalClasses)")
        output.append("• 活跃班级: \(report.classAnalysis.activeClasses)")
        output.append("• 孤立班级: \(report.classAnalysis.orphanedClasses)")
        output.append("")
        
        // 学生分析
        output.append("👨‍🎓 学生数据分析")
        output.append("• 总学生数: \(report.studentAnalysis.totalStudents)")
        output.append("• 孤立学生: \(report.studentAnalysis.studentsWithoutClass)")
        output.append("")
        
        // CloudKit状态
        output.append("☁️ CloudKit同步状态")
        output.append("• 同步功能: \(report.cloudKitStatus.isEnabled ? "已启用" : "未启用")")
        output.append("• 账户状态: \(report.cloudKitStatus.accountStatus)")
        output.append("• 同步状态: \(report.cloudKitStatus.syncStatus)")
        if let lastSync = report.cloudKitStatus.lastSyncDate {
            output.append("• 上次同步: \(DateFormatter.fullDateTime.string(from: lastSync))")
        }
        output.append("")
        
        // 一致性问题
        if !report.consistencyIssues.isEmpty {
            output.append("⚠️ 发现的问题")
            for (index, issue) in report.consistencyIssues.enumerated() {
                output.append("\(index + 1). \(issue.severity.emoji) \(issue.description)")
                if !issue.affectedEntities.isEmpty {
                    output.append("   影响实体: \(issue.affectedEntities.joined(separator: ", "))")
                }
                output.append("   可自动修复: \(issue.autoFixable ? "是" : "否")")
            }
            output.append("")
        }
        
        // 建议
        if !report.recommendations.isEmpty {
            output.append("💡 修复建议")
            for (index, recommendation) in report.recommendations.enumerated() {
                output.append("\(index + 1). \(recommendation)")
            }
        }
        
        return output.joined(separator: "\n")
    }
    
    // MARK: - 私有分析方法
    
    private func analyzeUsers() -> UserAnalysis {
        let request: NSFetchRequest<User> = User.fetchRequest()
        
        do {
            let allUsers = try coreDataManager.viewContext.fetch(request)
            let usersWithAppleID = allUsers.filter { $0.appleUserID != nil && !$0.appleUserID!.isEmpty }
            let usersWithoutAppleID = allUsers.filter { $0.appleUserID == nil || $0.appleUserID!.isEmpty }
            let usersWithoutSubscription = allUsers.filter { $0.subscription == nil }
            
            // 检查重复Apple ID
            let appleIDs = usersWithAppleID.compactMap { $0.appleUserID }
            let duplicateAppleIDs = Dictionary(grouping: appleIDs) { $0 }
                .filter { $1.count > 1 }
                .map { $0.key }
            
            return UserAnalysis(
                totalUsers: allUsers.count,
                usersWithAppleID: usersWithAppleID.count,
                usersWithoutAppleID: usersWithoutAppleID.count,
                duplicateAppleIDs: duplicateAppleIDs,
                usersWithoutSubscription: usersWithoutSubscription.count
            )
        } catch {
            print("❌ 分析用户数据失败: \(error)")
            return UserAnalysis(totalUsers: 0, usersWithAppleID: 0, usersWithoutAppleID: 0, duplicateAppleIDs: [], usersWithoutSubscription: 0)
        }
    }
    
    private func analyzeClasses() -> ClassAnalysis {
        let request: NSFetchRequest<SchoolClass> = SchoolClass.fetchRequest()
        
        do {
            let allClasses = try coreDataManager.viewContext.fetch(request)
            let orphanedClasses = allClasses.filter { $0.owner == nil }
            let activeClasses = allClasses.filter { $0.isActive }
            
            // 统计每个用户的班级数量
            var classesPerUser = [String: Int]()
            for schoolClass in allClasses {
                let userKey = schoolClass.owner?.name ?? "未知用户"
                classesPerUser[userKey, default: 0] += 1
            }
            
            return ClassAnalysis(
                totalClasses: allClasses.count,
                orphanedClasses: orphanedClasses.count,
                activeClasses: activeClasses.count,
                classesPerUser: classesPerUser
            )
        } catch {
            print("❌ 分析班级数据失败: \(error)")
            return ClassAnalysis(totalClasses: 0, orphanedClasses: 0, activeClasses: 0, classesPerUser: [:])
        }
    }
    
    private func analyzeStudents() -> StudentAnalysis {
        let request: NSFetchRequest<Student> = Student.fetchRequest()
        
        do {
            let allStudents = try coreDataManager.viewContext.fetch(request)
            let studentsWithoutClass = allStudents.filter { $0.schoolClass == nil }
            
            // 统计每个班级的学生数量
            var studentsPerClass = [String: Int]()
            for student in allStudents {
                let classKey = student.schoolClass?.name ?? "无班级"
                studentsPerClass[classKey, default: 0] += 1
            }
            
            return StudentAnalysis(
                totalStudents: allStudents.count,
                studentsWithoutClass: studentsWithoutClass.count,
                studentsPerClass: studentsPerClass
            )
        } catch {
            print("❌ 分析学生数据失败: \(error)")
            return StudentAnalysis(totalStudents: 0, studentsWithoutClass: 0, studentsPerClass: [:])
        }
    }
    
    private func analyzeCloudKitStatus() -> CloudKitAnalysis {
        return CloudKitAnalysis(
            isEnabled: coreDataManager.cloudKitSyncEnabled,
            accountStatus: coreDataManager.cloudKitStatus.displayText,
            lastSyncDate: coreDataManager.lastSyncDate,
            syncStatus: coreDataManager.isSyncing ? "同步中" : "空闲"
        )
    }
    
    private func detectConsistencyIssues() -> [ConsistencyIssue] {
        var issues = [ConsistencyIssue]()
        
        // 检查重复用户
        issues.append(contentsOf: checkDuplicateUsers())
        
        // 检查孤立数据
        issues.append(contentsOf: checkOrphanedData())
        
        // 检查缺失订阅
        issues.append(contentsOf: checkMissingSubscriptions())
        
        // 检查用户数据一致性
        issues.append(contentsOf: checkUserDataConsistency())
        
        return issues
    }
    
    private func checkDuplicateUsers() -> [ConsistencyIssue] {
        var issues = [ConsistencyIssue]()
        
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "appleUserID != nil AND appleUserID != ''")
        
        do {
            let usersWithAppleID = try coreDataManager.viewContext.fetch(request)
            let groupedUsers = Dictionary(grouping: usersWithAppleID) { $0.appleUserID }
            
            for (appleID, users) in groupedUsers where users.count > 1 {
                let issue = ConsistencyIssue(
                    type: .duplicateUsers,
                    description: "发现重复Apple ID用户: \(appleID ?? "Unknown")",
                    severity: .high,
                    affectedEntities: users.map { $0.name ?? "Unknown" },
                    autoFixable: true
                )
                issues.append(issue)
            }
        } catch {
            print("❌ 检查重复用户失败: \(error)")
        }
        
        return issues
    }
    
    private func checkOrphanedData() -> [ConsistencyIssue] {
        var issues = [ConsistencyIssue]()
        
        // 检查孤立班级
        let classRequest: NSFetchRequest<SchoolClass> = SchoolClass.fetchRequest()
        classRequest.predicate = NSPredicate(format: "owner == nil")
        
        do {
            let orphanedClasses = try coreDataManager.viewContext.fetch(classRequest)
            if !orphanedClasses.isEmpty {
                let issue = ConsistencyIssue(
                    type: .orphanedData,
                    description: "发现 \(orphanedClasses.count) 个孤立班级",
                    severity: .medium,
                    affectedEntities: orphanedClasses.map { $0.name ?? "Unknown" },
                    autoFixable: true
                )
                issues.append(issue)
            }
        } catch {
            print("❌ 检查孤立班级失败: \(error)")
        }
        
        // 检查孤立学生
        let studentRequest: NSFetchRequest<Student> = Student.fetchRequest()
        studentRequest.predicate = NSPredicate(format: "schoolClass == nil")
        
        do {
            let orphanedStudents = try coreDataManager.viewContext.fetch(studentRequest)
            if !orphanedStudents.isEmpty {
                let issue = ConsistencyIssue(
                    type: .orphanedData,
                    description: "发现 \(orphanedStudents.count) 个孤立学生",
                    severity: .medium,
                    affectedEntities: orphanedStudents.map { $0.name ?? "Unknown" },
                    autoFixable: false // 学生需要手动分配到班级
                )
                issues.append(issue)
            }
        } catch {
            print("❌ 检查孤立学生失败: \(error)")
        }
        
        return issues
    }
    
    private func checkMissingSubscriptions() -> [ConsistencyIssue] {
        var issues = [ConsistencyIssue]()
        
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "subscription == nil")
        
        do {
            let usersWithoutSubscription = try coreDataManager.viewContext.fetch(request)
            if !usersWithoutSubscription.isEmpty {
                let issue = ConsistencyIssue(
                    type: .missingSubscription,
                    description: "发现 \(usersWithoutSubscription.count) 个用户缺少订阅信息",
                    severity: .low,
                    affectedEntities: usersWithoutSubscription.map { $0.name ?? "Unknown" },
                    autoFixable: true
                )
                issues.append(issue)
            }
        } catch {
            print("❌ 检查订阅信息失败: \(error)")
        }
        
        return issues
    }
    
    private func checkUserDataConsistency() -> [ConsistencyIssue] {
        var issues = [ConsistencyIssue]()
        
        // 检查当前用户一致性
        if let authManager = AuthenticationManagerRegistry.shared.currentManager,
           let authUser = authManager.currentUser,
           let coreDataUser = coreDataManager.getCurrentUser(),
           authUser != coreDataUser {
            
            let issue = ConsistencyIssue(
                type: .inconsistentUserData,
                description: "AuthenticationManager和CoreDataManager的当前用户不一致",
                severity: .critical,
                affectedEntities: [
                    "Auth用户: \(authUser.name ?? "Unknown")",
                    "CoreData用户: \(coreDataUser.name ?? "Unknown")"
                ],
                autoFixable: true
            )
            issues.append(issue)
        }
        
        return issues
    }
    
    private func generateRecommendations(issues: [ConsistencyIssue]) -> [String] {
        var recommendations = [String]()
        
        if issues.isEmpty {
            recommendations.append("✅ 数据状态良好，无需特殊处理")
            return recommendations
        }
        
        let autoFixableCount = issues.filter { $0.autoFixable }.count
        if autoFixableCount > 0 {
            recommendations.append("🔧 有 \(autoFixableCount) 个问题可以自动修复，建议立即执行自动修复")
        }
        
        let criticalIssues = issues.filter { $0.severity == .critical }
        if !criticalIssues.isEmpty {
            recommendations.append("🚨 发现 \(criticalIssues.count) 个关键问题，需要立即处理")
        }
        
        let duplicateUserIssues = issues.filter { $0.type == .duplicateUsers }
        if !duplicateUserIssues.isEmpty {
            recommendations.append("👥 建议合并重复用户数据，保留最新登录的用户")
        }
        
        let orphanedDataIssues = issues.filter { $0.type == .orphanedData }
        if !orphanedDataIssues.isEmpty {
            recommendations.append("🏠 建议将孤立数据关联到当前用户")
        }
        
        if !coreDataManager.cloudKitSyncEnabled {
            recommendations.append("☁️ 建议启用CloudKit同步以确保数据在多设备间一致")
        }
        
        return recommendations
    }
    
    // MARK: - 修复方法
    
    private func fixIssue(_ issue: ConsistencyIssue) throws {
        switch issue.type {
        case .duplicateUsers:
            try fixDuplicateUsers()
        case .orphanedData:
            try fixOrphanedData()
        case .missingSubscription:
            try fixMissingSubscriptions()
        case .inconsistentUserData:
            try fixInconsistentUserData()
        case .cloudKitSyncIssue:
            try fixCloudKitSyncIssue()
        }
    }
    
    private func fixDuplicateUsers() throws {
        coreDataManager.performDataConsistencyCheck()
    }
    
    private func fixOrphanedData() throws {
        coreDataManager.performDataConsistencyCheck()
    }
    
    private func fixMissingSubscriptions() throws {
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "subscription == nil")
        
        let usersWithoutSubscription = try coreDataManager.viewContext.fetch(request)
        for user in usersWithoutSubscription {
            let subscription = Subscription.createFreeSubscription(for: user, in: coreDataManager.viewContext)
            user.subscription = subscription
        }
    }
    
    private func fixInconsistentUserData() throws {
        // 确保AuthenticationManager和CoreDataManager使用相同的用户
        if let authManager = AuthenticationManagerRegistry.shared.currentManager,
           let authUser = authManager.currentUser {
            coreDataManager.setCurrentUser(authUser)
        }
    }
    
    private func fixCloudKitSyncIssue() throws {
        // 触发CloudKit同步
        coreDataManager.triggerCloudKitSync()
    }
}

// MARK: - 日期格式化扩展

extension DateFormatter {
    static let shortDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter
    }()
    
    static let fullDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .medium
        return formatter
    }()
}