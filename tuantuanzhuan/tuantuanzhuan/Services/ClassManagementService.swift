//
//  ClassManagementService.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/7/6.
//

import Foundation
import CoreData
import SwiftUI

/**
 * 班级管理服务
 * 提供班级管理的核心业务逻辑，包括冻结和解冻功能
 */
class ClassManagementService {
    
    // MARK: - Shared Instance
    static let shared = ClassManagementService()
    
    // MARK: - Private Properties
    private let coreDataManager = CoreDataManager.shared
    
    // MARK: - Private Initialization
    private init() {}
    
    // MARK: - Public Methods
    
    /**
     * 获取用户的所有班级（包括活跃和冻结状态）
     * @param user 用户对象
     * @return 所有班级列表
     */
    func getAllClasses(for user: User) -> [SchoolClass] {
        return user.sortedClasses
    }
    
    /**
     * 获取用户的活跃班级列表
     * @param user 用户对象
     * @return 活跃状态的班级列表
     */
    func getActiveClasses(for user: User) -> [SchoolClass] {
        return user.sortedClasses.filter { $0.isActive }
    }
    
    /**
     * 获取用户的冻结班级列表
     * @param user 用户对象
     * @return 冻结状态的班级列表
     */
    func getFrozenClasses(for user: User) -> [SchoolClass] {
        return user.sortedClasses.filter { $0.isFrozen }
    }
    
    /**
     * 处理会员降级逻辑
     * 用户降级后，根据新会员等级限制活跃班级数量
     * @param user 用户对象
     * @param oldLevel 旧会员等级
     * @param newLevel 新会员等级
     * @param activeClassIds 要保持活跃的班级ID列表
     * @return 是否成功处理
     */
    func handleSubscriptionDowngrade(
        for user: User,
        fromLevel oldLevel: Subscription.Level,
        toLevel newLevel: Subscription.Level,
        keepingActiveClassIds activeClassIds: [String]
    ) -> Bool {
        let context = coreDataManager.viewContext
        let maxAllowedClasses = newLevel.maxClasses
        let currentActiveClasses = getActiveClasses(for: user)
        
        // 如果活跃班级数量已经低于等于新等级允许的数量，无需冻结
        if currentActiveClasses.count <= maxAllowedClasses {
            return true
        }
        
        // 将除了selectedClassIds之外的班级冻结
        for schoolClass in currentActiveClasses {
            guard let classId = schoolClass.id?.uuidString else { continue }
            
            if !activeClassIds.contains(classId) {
                schoolClass.freeze(in: context)
                
                // 发送班级状态变更通知
                NotificationCenter.default.post(
                    name: .classStatusChanged,
                    object: nil,
                    userInfo: [
                        NotificationUserInfoKey.classId: classId,
                        NotificationUserInfoKey.className: schoolClass.name ?? "未命名班级",
                        NotificationUserInfoKey.newStatus: SchoolClass.Status.frozen.rawValue,
                        NotificationUserInfoKey.oldStatus: SchoolClass.Status.active.rawValue
                    ]
                )
            }
        }
        
        // 保存更改
        do {
            try context.save()
            return true
        } catch {
            print("❌ 处理会员降级冻结班级失败: \(error)")
            return false
        }
    }
    
    /**
     * 处理会员升级解冻班级
     * 用户升级后，根据用户选择的班级ID列表解冻班级
     * @param user 用户对象
     * @param classIdsToUnfreeze 要解冻的班级ID列表
     * @return 是否成功处理
     */
    func handleClassUnfreeze(
        for user: User,
        unfreezeClassIds classIdsToUnfreeze: [String]
    ) -> Bool {
        let context = coreDataManager.viewContext
        let maxAllowedClasses = user.maxClassesAllowed
        let currentActiveClasses = getActiveClasses(for: user)
        
        // 检查解冻后的活跃班级数量是否超出限制
        if currentActiveClasses.count + classIdsToUnfreeze.count > maxAllowedClasses {
            print("⚠️ 解冻后的班级总数将超出会员等级限制")
            return false
        }
        
        // 解冻选中的班级
        for classId in classIdsToUnfreeze {
            guard let schoolClass = user.sortedClasses.first(where: { $0.id?.uuidString == classId }) else {
                continue
            }
            
            // 确保班级处于冻结状态
            if schoolClass.isFrozen {
                let oldStatus = schoolClass.status ?? SchoolClass.Status.frozen.rawValue
                schoolClass.unfreeze(in: context)
                
                // 发送班级状态变更通知
                NotificationCenter.default.post(
                    name: .classStatusChanged,
                    object: nil,
                    userInfo: [
                        NotificationUserInfoKey.classId: classId,
                        NotificationUserInfoKey.className: schoolClass.name ?? "未命名班级",
                        NotificationUserInfoKey.newStatus: SchoolClass.Status.active.rawValue,
                        NotificationUserInfoKey.oldStatus: oldStatus
                    ]
                )
            }
        }
        
        // 保存更改
        do {
            try context.save()
            return true
        } catch {
            print("❌ 处理班级解冻失败: \(error)")
            return false
        }
    }
    
    /**
     * 检查用户是否需要处理班级冻结
     * 当会员降级后，活跃班级数量超出限制时，需要进行班级冻结处理
     * @param user 用户对象
     * @return 是否需要处理班级冻结
     */
    func needsClassFreezeHandling(for user: User) -> Bool {
        let maxAllowedClasses = user.maxClassesAllowed
        let currentActiveClasses = getActiveClasses(for: user)
        
        let needsFreezing = currentActiveClasses.count > maxAllowedClasses
        
        if needsFreezing {
            print("⚠️ 检测到需要处理班级冻结: 当前活跃班级数 \(currentActiveClasses.count) > 最大允许班级数 \(maxAllowedClasses)")
        } else {
            print("✅ 不需要处理班级冻结: 当前活跃班级数 \(currentActiveClasses.count) <= 最大允许班级数 \(maxAllowedClasses)")
        }
        
        return needsFreezing
    }
    
    /**
     * 获取会员降级后需要冻结的班级数量
     * @param user 用户对象
     * @return 需要冻结的班级数量
     */
    func getFreezingClassesCount(for user: User) -> Int {
        let maxAllowedClasses = user.maxClassesAllowed
        let currentActiveClasses = getActiveClasses(for: user)
        
        return max(0, currentActiveClasses.count - maxAllowedClasses)
    }
    
    /**
     * 获取当前会员等级允许解冻的班级数量
     * @param user 用户对象
     * @return 可解冻的班级数量
     */
    func getAvailableUnfreezeCount(for user: User) -> Int {
        let maxAllowedClasses = user.maxClassesAllowed
        let currentActiveClasses = getActiveClasses(for: user)
        
        return max(0, maxAllowedClasses - currentActiveClasses.count)
    }
    
    /**
     * 冻结单个班级
     * @param schoolClass 要冻结的班级
     * @return 是否成功冻结
     */
    func freezeClass(_ schoolClass: SchoolClass) -> Bool {
        let context = coreDataManager.viewContext
        
        schoolClass.freeze(in: context)
        
        do {
            try context.save()
            return true
        } catch {
            print("❌ 冻结班级失败: \(error)")
            return false
        }
    }
    
    /**
     * 解冻单个班级
     * @param schoolClass 要解冻的班级
     * @param user 用户对象，用于检查是否超出限制
     * @return 是否成功解冻
     */
    func unfreezeClass(_ schoolClass: SchoolClass, for user: User) -> Bool {
        // 检查解冻后是否会超出会员等级限制
        if getAvailableUnfreezeCount(for: user) <= 0 {
            print("⚠️ 无法解冻班级：已达到会员等级允许的最大班级数")
            return false
        }
        
        let context = coreDataManager.viewContext
        schoolClass.unfreeze(in: context)
        
        do {
            try context.save()
            return true
        } catch {
            print("❌ 解冻班级失败: \(error)")
            return false
        }
    }
}