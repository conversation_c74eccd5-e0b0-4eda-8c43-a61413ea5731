//
//  String+Localization.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import SwiftUI

// MARK: - String Localization Extensions

/**
 * String扩展，提供便捷的本地化方法
 */
extension String {
    
    /**
     * 获取本地化字符串（简化版）
     * 使用当前字符串作为键值进行本地化
     * @return 本地化后的字符串
     */
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    /**
     * 获取格式化的本地化字符串
     * @param arguments 格式化参数
     * @return 格式化后的本地化字符串
     */
    func localized(with arguments: CVarArg...) -> String {
        let localizedString = NSLocalizedString(self, comment: "")
        if arguments.isEmpty {
            return localizedString
        } else {
            return String(format: localizedString, arguments: arguments)
        }
    }
    
    /**
     * 获取带注释的本地化字符串
     * @param comment 注释说明，帮助翻译人员理解上下文
     * @return 本地化后的字符串
     */
    func localized(comment: String) -> String {
        return NSLocalizedString(self, comment: comment)
    }
    
    /**
     * 获取格式化的带注释本地化字符串
     * @param comment 注释说明
     * @param arguments 格式化参数
     * @return 格式化后的本地化字符串
     */
    func localized(comment: String, arguments: CVarArg...) -> String {
        let localizedString = NSLocalizedString(self, comment: comment)
        if arguments.isEmpty {
            return localizedString
        } else {
            return String(format: localizedString, arguments: arguments)
        }
    }
}

// MARK: - SwiftUI Text Localization Extensions

/**
 * Text扩展，提供便捷的本地化Text组件创建方法
 */
extension Text {
    
    /**
     * 创建本地化Text组件
     * @param key 本地化键值
     * @return 本地化的Text组件
     */
    init(localized key: String) {
        self.init(key.localized)
    }
    
    /**
     * 创建格式化的本地化Text组件
     * @param key 本地化键值
     * @param arguments 格式化参数
     * @return 格式化的本地化Text组件
     */
    init(localized key: String, arguments: CVarArg...) {
        self.init(key.localized(with: arguments))
    }
    
    /**
     * 创建带注释的本地化Text组件
     * @param key 本地化键值
     * @param comment 注释说明
     * @return 本地化的Text组件
     */
    init(localized key: String, comment: String) {
        self.init(key.localized(comment: comment))
    }
}

// MARK: - Localization Helper Functions

/**
 * 全局本地化函数，简化调用
 * @param key 本地化键值
 * @return 本地化后的字符串
 */
func L(_ key: String) -> String {
    return key.localized
}

/**
 * 全局格式化本地化函数
 * @param key 本地化键值
 * @param arguments 格式化参数
 * @return 格式化后的本地化字符串
 */
func L(_ key: String, _ arguments: CVarArg...) -> String {
    return key.localized(with: arguments)
}

/**
 * 全局带注释本地化函数
 * @param key 本地化键值
 * @param comment 注释说明
 * @return 本地化后的字符串
 */
func LC(_ key: String, comment: String) -> String {
    return key.localized(comment: comment)
} 