//
//  StudentRedemptionOperation.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation

/**
 * 兑换操作类型
 */
enum StudentRedemptionOperationType {
    case prize    // 奖品兑换
    case custom   // 自定义兑换
}

/**
 * 兑换操作项
 */
struct StudentRedemptionOperationItem {
    var name: String
    var cost: Int
    var type: String?  // 奖品类型（虚拟/实物）
    
    /**
     * 初始化方法
     */
    init(name: String = "", cost: Int = 0, type: String? = nil) {
        self.name = name
        self.cost = cost
        self.type = type
    }
    
    /**
     * 从Prize对象创建
     */
    init(from prize: Prize) {
        self.name = prize.name ?? ""
        self.cost = Int(prize.cost)
        self.type = prize.type
    }
    
    /**
     * 验证操作项是否有效
     */
    var isValid: Bool {
        return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && cost > 0
    }
    
    /**
     * 格式化名称（去除空白字符）
     */
    var formattedName: String {
        return name.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

/**
 * 兑换操作数据结构
 */
struct StudentRedemptionOperation {
    let items: [StudentRedemptionOperationItem]
    
    /**
     * 初始化方法
     */
    init(items: [StudentRedemptionOperationItem]) {
        self.items = items
    }
    
    /**
     * 从单个操作项创建
     */
    init(item: StudentRedemptionOperationItem) {
        self.items = [item]
    }
    
    /**
     * 从Prize对象创建
     */
    init(prize: Prize) {
        let item = StudentRedemptionOperationItem(from: prize)
        self.items = [item]
    }
    
    /**
     * 计算总消耗积分
     */
    var totalCost: Int {
        return items.reduce(0) { $0 + $1.cost }
    }
    
    /**
     * 验证所有操作项是否有效
     */
    var isValid: Bool {
        return !items.isEmpty && items.allSatisfy { $0.isValid }
    }
    
    /**
     * 获取有效的操作项
     */
    var validItems: [StudentRedemptionOperationItem] {
        return items.filter { $0.isValid }
    }
} 