//
//  StudentFormData.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation

/**
 * 学生表单数据模型
 * 用于添加学生功能的数据传递和验证
 */
struct StudentFormData: Identifiable, Equatable {
    
    let id = UUID()
    var name: String = ""
    var studentNumber: String = ""
    var gender: String = "male"
    var initialPoints: String = "0"
    
    // MARK: - Computed Properties
    
    /**
     * 获取初始积分的整数值
     */
    var initialPointsValue: Int {
        return Int(initialPoints) ?? 0
    }
    
    /**
     * 检查表单数据是否有效
     */
    var isValid: Bool {
        return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !studentNumber.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               initialPointsValue >= 0
    }
    
    /**
     * 获取格式化的姓名（去除空格）
     */
    var formattedName: String {
        return name.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /**
     * 获取格式化的学号（去除空格）
     */
    var formattedStudentNumber: String {
        return studentNumber.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    // MARK: - Validation Methods
    
    /**
     * 验证姓名格式
     */
    func validateName() -> ValidationResult {
        let trimmedName = formattedName
        if trimmedName.isEmpty {
            return .invalid("student.form.validation.name_empty".localized)
        }
        if trimmedName.count > 20 {
            return .invalid("student.form.validation.name_too_long".localized)
        }
        return .valid
    }
    
    /**
     * 验证学号格式
     */
    func validateStudentNumber() -> ValidationResult {
        let trimmedNumber = formattedStudentNumber
        if trimmedNumber.isEmpty {
            return .invalid("student.form.validation.number_empty".localized)
        }
        if trimmedNumber.count > 15 {
            return .invalid("student.form.validation.number_too_long".localized)
        }
        return .valid
    }
    
    /**
     * 验证初始积分
     */
    func validateInitialPoints() -> ValidationResult {
        if initialPoints.isEmpty {
            return .valid // 空值默认为0
        }
        guard let points = Int(initialPoints) else {
            return .invalid("student.form.validation.points_invalid".localized)
        }
        if points < 0 {
            return .invalid("student.form.validation.points_negative".localized)
        }
        if points > 1000 {
            return .invalid("student.form.validation.points_too_high".localized)
        }
        return .valid
    }
    
    /**
     * 全面验证表单数据
     */
    func validateAll() -> ValidationResult {
        let nameResult = validateName()
        if case .invalid = nameResult { return nameResult }
        
        let numberResult = validateStudentNumber()
        if case .invalid = numberResult { return numberResult }
        
        let pointsResult = validateInitialPoints()
        if case .invalid = pointsResult { return pointsResult }
        
        return .valid
    }
}

// MARK: - Validation Result

/**
 * 验证结果枚举
 */
enum ValidationResult {
    case valid
    case invalid(String)
    
    var isValid: Bool {
        switch self {
        case .valid:
            return true
        case .invalid:
            return false
        }
    }
    
    var errorMessage: String? {
        switch self {
        case .valid:
            return nil
        case .invalid(let message):
            return message
        }
    }
}

// MARK: - Batch Validation

extension Array where Element == StudentFormData {
    
    /**
     * 批量验证学生数据
     */
    func validateBatch() -> BatchValidationResult {
        var errors: [String] = []
        var duplicateNumbers: Set<String> = []
        var allNumbers: Set<String> = []
        
        for (index, student) in self.enumerated() {
            // 单个学生验证
            let validation = student.validateAll()
            if case .invalid(let message) = validation {
                errors.append("student.form.validation.row_error".localized(with: "\(index + 1)", message))
            }
            
            // 学号重复检查
            let number = student.formattedStudentNumber
            if !number.isEmpty {
                if allNumbers.contains(number) {
                    duplicateNumbers.insert(number)
                } else {
                    allNumbers.insert(number)
                }
            }
        }
        
        // 添加重复学号错误
        for number in duplicateNumbers {
            errors.append("student.form.validation.duplicate_number".localized(with: number))
        }
        
        if errors.isEmpty {
            return .valid
        } else {
            return .invalid(errors)
        }
    }
}

/**
 * 批量验证结果
 */
enum BatchValidationResult {
    case valid
    case invalid([String])
    
    var isValid: Bool {
        switch self {
        case .valid:
            return true
        case .invalid:
            return false
        }
    }
    
    var errorMessages: [String] {
        switch self {
        case .valid:
            return []
        case .invalid(let messages):
            return messages
        }
    }
} 