//
//  HistoryRecordProtocol.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation
import SwiftUI

/**
 * 历史记录类型枚举
 * 定义所有支持的记录类型及其显示属性
 */
enum HistoryRecordType: String, CaseIterable {
    case points = "积分"
    case redemption = "兑换"
    case wheelLottery = "大转盘"
    case boxLottery = "盲盒"
    case scratchLottery = "刮刮卡"
    
    /// 显示名称
    var displayName: String {
        return self.rawValue
    }
    
    /// 类型图标名称
    var iconName: String {
        switch self {
        case .points:
            return "lishi"
        case .redemption:
            return "lingqujilu"
        case .wheelLottery:
            return "choujiang"
        case .boxLottery:
            return "choujiang"
        case .scratchLottery:
            return "choujiang"
        }
    }
    
    /// 类型标识颜色
    var typeColor: Color {
        switch self {
        case .points:
            return Color(hex: "#a9d051")
        case .redemption:
            return Color(hex: "#4A90E2")
        case .wheelLottery:
            return Color(hex: "#F5A623")
        case .boxLottery:
            return Color(hex: "#9013FE")
        case .scratchLottery:
            return Color(hex: "#50E3C2")
        }
    }
    
    /// 是否为抽奖类型
    var isLotteryType: Bool {
        switch self {
        case .wheelLottery, .boxLottery, .scratchLottery:
            return true
        case .points, .redemption:
            return false
        }
    }
}

/**
 * 统一的历史记录协议
 * 为所有类型的记录提供统一的访问接口
 */
protocol HistoryRecordProtocol {
    /// 记录唯一标识
    var recordId: UUID? { get }
    
    /// 记录显示名称
    var displayName: String { get }
    
    /// 格式化显示时间
    var formattedTime: String { get }
    
    /// 积分数值（正数为加分，负数为扣分，0为无积分变化）
    var pointsValue: Int { get }
    
    /// 格式化显示积分
    var formattedPoints: String { get }
    
    /// 记录类型
    var recordType: HistoryRecordType { get }
    
    /// 记录时间戳
    var recordTimestamp: Date? { get }
    
    /// 是否可以删除（有些记录可能不允许删除）
    var canDelete: Bool { get }
    
    /// 删除记录的积分影响描述
    var deleteImpactDescription: String { get }
}

/**
 * 历史记录协议的默认实现
 */
extension HistoryRecordProtocol {
    
    /// 默认格式化积分显示
    var formattedPoints: String {
        if pointsValue > 0 {
            return "+\(pointsValue)"
        } else if pointsValue < 0 {
            return "\(pointsValue)"
        } else {
            return "0"
        }
    }
    
    /// 默认可以删除
    var canDelete: Bool {
        return true
    }
    
    /// 默认删除影响描述
    var deleteImpactDescription: String {
        switch recordType {
        case .points:
            if pointsValue > 0 {
                return "删除后将扣除 \(pointsValue) 积分"
            } else if pointsValue < 0 {
                return "删除后将返还 \(abs(pointsValue)) 积分"
            } else {
                return "删除此记录不会影响积分"
            }
        case .redemption, .wheelLottery, .boxLottery, .scratchLottery:
            return "删除后将返还 \(abs(pointsValue)) 积分"
        }
    }
} 