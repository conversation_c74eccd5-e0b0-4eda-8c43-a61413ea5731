//
//  RuleFormData.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/16.
//

import Foundation

/**
 * 规则表单数据模型
 * 用于规则配置功能的数据传递和验证
 */
struct RuleFormData: Identifiable, Equatable {
    
    let id = UUID()
    var name: String = ""
    var value: String = ""
    var type: String // "add" 或 "deduct"
    
    // MARK: - 初始化方法
    init(type: String = "add") {
        print("🏗️ [RuleFormData] 构造函数调用")
        print("📥 [RuleFormData] 传入参数type: '\(type)'")
        self.type = type
        print("📋 [RuleFormData] 设置后self.type: '\(self.type)'")
        print("✅ [RuleFormData] 构造完成，实例类型: '\(self.type)'")
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取分值的整数值
     */
    var valueInt: Int {
        return Int(value) ?? 1
    }
    
    /**
     * 检查表单数据是否有效
     */
    var isValid: Bool {
        return !formattedName.isEmpty &&
               valueInt > 0 &&
               formattedName.count <= 20
    }
    
    /**
     * 获取格式化的规则名称（去除空格）
     */
    var formattedName: String {
        return name.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /**
     * 获取规则类型显示名称
     */
    var typeDisplayName: String {
        return type == "add" ? "加分" : "扣分"
    }
    
    // MARK: - Validation Methods
    
    /**
     * 验证规则名称格式
     */
    func validateName() -> ValidationResult {
        let trimmedName = formattedName
        if trimmedName.isEmpty {
            return .invalid("Rule name cannot be empty".localized)
        }
        if trimmedName.count > 20 {
            return .invalid("Rule name cannot exceed 20 characters".localized)
        }
        return .valid
    }
    
    /**
     * 验证分值格式
     */
    func validateValue() -> ValidationResult {
        if value.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return .invalid("Rule name cannot exceed 20 characters".localized)
        }
        
        guard let intValue = Int(value.trimmingCharacters(in: .whitespacesAndNewlines)) else {
            return .invalid("Please enter a valid points value".localized)
        }
        
        if intValue <= 0 {
            return .invalid("Points cost must be greater than 0".localized)
        }
        
        if intValue > 100 {
            return .invalid("Points cost cannot exceed 1000".localized)
        }
        
        return .valid
    }
    
    /**
     * 检查规则名称是否与现有规则模板重复
     */
    func validateUniqueName(existingRules: [RuleTemplate]) -> ValidationResult {
        let trimmedName = formattedName
        let duplicateExists = existingRules.contains { rule in
            guard let ruleName = rule.name else { return false }
            return ruleName.trimmingCharacters(in: .whitespacesAndNewlines).lowercased() == 
                   trimmedName.lowercased() && rule.type == type
        }
        
        if duplicateExists {
            return .invalid("Rule name already exists".localized)
        }
        
        return .valid
    }
    
    /**
     * 完整验证（包括重复检查）
     */
    func validate(existingRules: [RuleTemplate] = []) -> ValidationResult {
        // 1. 验证名称
        let nameResult = validateName()
        if case .invalid = nameResult {
            return nameResult
        }
        
        // 2. 验证分值
        let valueResult = validateValue()
        if case .invalid = valueResult {
            return valueResult
        }
        
        // 3. 验证唯一性
        let uniqueResult = validateUniqueName(existingRules: existingRules)
        if case .invalid = uniqueResult {
            return uniqueResult
        }
        
        return .valid
    }
}

// MARK: - 批量验证扩展
extension Array where Element == RuleFormData {
    
    /**
     * 批量验证规则表单数据
     */
    func validateBatch(existingRules: [RuleTemplate] = []) -> RuleBatchValidationResult {
        var errorMessages: [String] = []
        var validRules: [RuleFormData] = []
        
        // 检查内部重复
        var seenNames: [String: Int] = [:]
        
        for (index, rule) in self.enumerated() {
            let ruleIndex = index + 1
            let trimmedName = rule.formattedName.lowercased()
            
            // 检查基本验证
            let basicValidation = rule.validate(existingRules: existingRules)
            if case .invalid(let message) = basicValidation {
                errorMessages.append("rule_config.validation.row_error".localized(with: "\(ruleIndex)", message))
                continue
            }
            
            // 检查批次内重复
            if let firstIndex = seenNames[trimmedName] {
                errorMessages.append("rule_config.validation.row_error".localized(with: "\(ruleIndex)", 
                    "rule_config.validation.internal_duplicate".localized(with: "\(firstIndex)")))
                continue
            }
            
            seenNames[trimmedName] = ruleIndex
            validRules.append(rule)
        }
        
        return RuleBatchValidationResult(
            isValid: errorMessages.isEmpty,
            errorMessages: errorMessages,
            validRules: validRules
        )
    }
}

// MARK: - 规则批量验证结果
struct RuleBatchValidationResult {
    let isValid: Bool
    let errorMessages: [String]
    let validRules: [RuleFormData]
    
    var errorSummary: String {
        return errorMessages.joined(separator: "\n")
    }
} 
