//
//  SubscriptionViewModel.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/20.
//  Updated to use RevenueCat for real subscription management
//

import Foundation
import SwiftUI
import RevenueCat
import Combine

/**
 * 订阅视图模型
 * 管理订阅相关的UI状态和业务逻辑
 */
@MainActor
class SubscriptionViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var subscriptionLevel: String = "free"
    @Published var expirationDate: Date?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String = ""
    @Published var showError: Bool = false
    @Published var agreementAccepted: Bool = false
    @Published var showAgreementAlert: Bool = false
    @Published var purchaseSuccess: Bool = false
    @Published var purchaseInProgress: Bool = false
    @Published var restoreSuccess: Bool = false
    @Published var customerInfo: CustomerInfo?
    @Published var offerings: Offerings?
    
    // MARK: - Private Properties
    private let revenueCatManager = RevenueCatManager.shared
    private let subscriptionService = SubscriptionService.shared
    private let authManager = AuthenticationManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init() {
        setupObservers()
        loadSubscriptionData()
    }
    
    // MARK: - Public Methods
    
    /**
     * 加载订阅数据
     */
    func loadSubscriptionData() {
        isLoading = true
        
        // 从RevenueCat获取最新数据
        subscriptionLevel = revenueCatManager.currentSubscriptionLevel.rawValue
        expirationDate = revenueCatManager.expirationDate
        customerInfo = revenueCatManager.customerInfo
        offerings = revenueCatManager.offerings
        
        isLoading = false
    }
    
    /**
     * 购买产品
     */
    func purchase(productId: String) {
        print("🔍 开始购买流程 - 产品ID: \(productId)")
        print("🔍 协议接受状态: \(agreementAccepted)")

        // 检查用户是否接受协议
        if !agreementAccepted {
            print("⚠️ 用户未接受协议，显示协议提醒")
            showAgreementAlert = true
            return
        }

        // 重置状态
        purchaseSuccess = false
        errorMessage = ""
        purchaseInProgress = true
        isLoading = true

        Task {
            let success = await revenueCatManager.purchaseProduct(productId: productId)

            await MainActor.run {
                self.isLoading = false
                self.purchaseInProgress = false

                if success {
                    self.purchaseSuccess = true
                    self.loadSubscriptionData() // 刷新数据

                    // 延迟隐藏成功状态 - 增加延迟时间，确保弹窗有足够时间显示
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                        self.purchaseSuccess = false
                    }
                } else {
                    // 确保购买失败状态正确设置
                    self.purchaseSuccess = false

                    // 错误信息已在RevenueCatManager中设置
                    if let errorMsg = self.revenueCatManager.errorMessage {
                        self.errorMessage = errorMsg

                        // 检查是否是用户取消，如果是则不显示错误提示
                        if !errorMsg.contains("用户取消") && !errorMsg.contains("cancelled") {
                            self.showError(message: errorMsg)
                        } else {
                            print("ℹ️ 用户取消购买，不显示错误提示")
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 检查订阅状态（替代恢复购买）
     */
    func restorePurchases() {
        isLoading = true

        Task {
            let success = await revenueCatManager.restorePurchases()

            await MainActor.run {
                self.isLoading = false

                if success {
                    self.restoreSuccess = true
                    self.loadSubscriptionData() // 刷新数据

                    // 延迟隐藏成功状态
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                        self.restoreSuccess = false
                    }
                } else {
                    // 错误信息已在RevenueCatManager中设置
                    if let errorMsg = self.revenueCatManager.errorMessage {
                        self.showError(message: errorMsg)
                    }
                }
            }
        }
    }
    
    /**
     * 手动领取试用（30天高级会员）
     */
    func claimTrialMembership() {
        isLoading = true
        
        Task {
            let success = await revenueCatManager.grantPremiumTrialDays(30)
            
            await MainActor.run {
                self.isLoading = false
                
                if success {
                    self.loadSubscriptionData() // 刷新数据
                    
                    // 显示成功消息
                    self.showError(message: "subscription.trial_success_message".localized)
                } else {
                    if let errorMsg = self.revenueCatManager.errorMessage {
                        self.showError(message: errorMsg)
                    }
                }
            }
        }
    }
    
    /**
     * 获取产品价格信息
     */
    func getProductPrice(for productId: String) -> String {
        let prices = revenueCatManager.getProductPrices()
        return prices[productId] ?? "加载中..."
    }
    
    /**
     * 获取基础月度套餐价格
     */
    func getBasicMonthlyPrice() -> String {
        return getProductPrice(for: SubscriptionService.ProductIds.monthlyBasic)
    }
    
    /**
     * 获取基础年度套餐价格
     */
    func getBasicYearlyPrice() -> String {
        return getProductPrice(for: SubscriptionService.ProductIds.yearlyBasic)
    }
    
    /**
     * 获取高级月度套餐价格
     */
    func getPremiumMonthlyPrice() -> String {
        return getProductPrice(for: SubscriptionService.ProductIds.monthlyPremium)
    }
    
    /**
     * 获取高级年度套餐价格
     */
    func getPremiumYearlyPrice() -> String {
        return getProductPrice(for: SubscriptionService.ProductIds.yearlyPremium)
    }
    
    /**
     * 购买基础月度订阅
     */
    func purchaseBasicMonthly() {
        purchase(productId: SubscriptionService.ProductIds.monthlyBasic)
    }
    
    /**
     * 购买基础年度订阅
     */
    func purchaseBasicYearly() {
        purchase(productId: SubscriptionService.ProductIds.yearlyBasic)
    }
    
    /**
     * 购买高级月度订阅
     */
    func purchasePremiumMonthly() {
        purchase(productId: SubscriptionService.ProductIds.monthlyPremium)
    }
    
    /**
     * 购买高级年度订阅
     */
    func purchasePremiumYearly() {
        purchase(productId: SubscriptionService.ProductIds.yearlyPremium)
    }
    
    /**
     * 获取订阅到期日期的格式化字符串
     */
    func getFormattedExpirationDate() -> String {
        guard let expirationDate = expirationDate else {
            return "subscription.status.not_subscribed".localized
        }
        
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale(identifier: "zh_CN")
        
        return "subscription.valid_until".localized + ": " + formatter.string(from: expirationDate)
    }
    
    /**
     * 检查是否为付费用户
     */
    func isPaidUser() -> Bool {
        return revenueCatManager.isPaidUser
    }
    
    /**
     * 检查是否为高级用户
     */
    func isPremiumUser() -> Bool {
        return revenueCatManager.isPremiumUser
    }
    
    /**
     * 检查是否为基础会员
     */
    func isBasicUser() -> Bool {
        return revenueCatManager.currentSubscriptionLevel == .basic
    }
    
    /**
     * 检查产品是否可用
     */
    func isProductAvailable(_ productId: String) -> Bool {
        return offerings != nil
    }
    
    /**
     * 获取本地化的产品信息
     */
    func getProductInfo(for productId: String) -> (title: String, description: String, price: String)? {
        return revenueCatManager.getLocalizedProductInfo(for: productId)
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置观察者
     */
    private func setupObservers() {
        // 监听RevenueCat状态变化
        revenueCatManager.$currentSubscriptionLevel
            .sink { [weak self] level in
                self?.subscriptionLevel = level.rawValue
            }
            .store(in: &cancellables)
        
        revenueCatManager.$customerInfo
            .sink { [weak self] customerInfo in
                self?.customerInfo = customerInfo
            }
            .store(in: &cancellables)
        
        revenueCatManager.$offerings
            .sink { [weak self] offerings in
                self?.offerings = offerings
            }
            .store(in: &cancellables)
        
        revenueCatManager.$isLoading
            .sink { [weak self] isLoading in
                if !isLoading {
                    self?.isLoading = false
                }
            }
            .store(in: &cancellables)
        
        revenueCatManager.$errorMessage
            .sink { [weak self] errorMessage in
                if let errorMessage = errorMessage {
                    self?.showError(message: errorMessage)
                }
            }
            .store(in: &cancellables)
        
        // 监听订阅状态变更通知
        NotificationCenter.default.publisher(for: .subscriptionStatusChanged)
            .sink { [weak self] _ in
                self?.loadSubscriptionData()
            }
            .store(in: &cancellables)
    }
    
    /**
     * 显示错误信息
     */
    private func showError(message: String) {
        // 检查是否是用户取消，如果是则不显示错误
        if message.contains("用户取消") || message.contains("cancelled") {
            print("ℹ️ 用户取消购买，不显示错误提示")
            return
        }

        errorMessage = message
        showError = true

        // 3秒后自动隐藏错误
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            self.showError = false
            self.errorMessage = ""
        }
    }

    /**
     * 重置购买状态
     */
    func resetPurchaseState() {
        purchaseSuccess = false
        errorMessage = ""
        showError = false
        isLoading = false
        purchaseInProgress = false
    }
    
    // MARK: - Deinitializer
    
    deinit {
        cancellables.removeAll()
    }
} 