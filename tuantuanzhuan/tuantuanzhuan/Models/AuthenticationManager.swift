//
//  AuthenticationManager.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation
import AuthenticationServices
import SwiftUI
import CoreData

/**
 * 认证管理器
 * 负责管理用户登录状态和Apple登录流程
 */
class AuthenticationManager: ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = AuthenticationManager()
    
    // MARK: - Published Properties
    @Published var isLoggedIn: Bool = false
    @Published var currentUser: User?
    @Published var isLoading: Bool = false
    
    // MARK: - Private Properties
    private let coreDataManager = CoreDataManager.shared
    private let userDefaults = UserDefaults.standard
    private let keychainManager = KeychainManager.shared
    
    // MARK: - Constants
    private enum UserDefaultsKeys {
        static let isLoggedIn = "user_is_logged_in"
        static let appleUserID = "apple_user_id"
        static let userName = "user_name"
        static let userEmail = "user_email"
    }
    
    // MARK: - Initialization
    init() {
        // 注册到Registry
        AuthenticationManagerRegistry.shared.register(self)
        // 检查登录状态
        checkLoginStatus()
    }
    
    deinit {
        // 注销Registry
        AuthenticationManagerRegistry.shared.unregister()
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查登录状态
     */
    func checkLoginStatus() {
        print("🔍 开始检查登录状态")
        isLoading = true
        
        // 检查本地存储的登录状态
        let savedLoginStatus = userDefaults.bool(forKey: UserDefaultsKeys.isLoggedIn)
        let savedAppleUserID = userDefaults.string(forKey: UserDefaultsKeys.appleUserID)
        
        print("💾 UserDefaults中的登录状态: \(savedLoginStatus)")
        print("💾 UserDefaults中的Apple用户ID: \(savedAppleUserID ?? "无")")
        
        // 如果有Apple用户ID，验证Apple登录状态
        if let appleUserID = savedAppleUserID {
            print("✅ 检测到Apple用户ID，开始验证Apple登录状态")
            // 验证Apple登录状态
            verifyAppleLoginStatus(userID: appleUserID)
        } else {
            print("❌ 未检测到Apple用户ID")
            // 未登录状态
            DispatchQueue.main.async {
                self.isLoggedIn = false
                self.isLoading = false
            }
        }
    }
    
    /**
     * 处理Apple登录成功
     */
    func handleSuccessfulLogin(userID: String, fullName: PersonNameComponents?, email: String?) {
        isLoading = true
        
        // 保存登录信息到UserDefaults
        userDefaults.set(true, forKey: UserDefaultsKeys.isLoggedIn)
        userDefaults.set(userID, forKey: UserDefaultsKeys.appleUserID)
        
        if let email = email {
            userDefaults.set(email, forKey: UserDefaultsKeys.userEmail)
        }
        
        if let fullName = fullName {
            let displayName = PersonNameComponentsFormatter().string(from: fullName)
            userDefaults.set(displayName, forKey: UserDefaultsKeys.userName)
        }
        
        // 创建或更新用户记录
        createOrUpdateUser(appleUserID: userID, fullName: fullName, email: email) { [weak self] user in
            DispatchQueue.main.async {
                self?.currentUser = user
                self?.isLoggedIn = true
                self?.isLoading = false
                
                print("✅ 用户登录成功: \(user?.name ?? "Unknown")")
                
                // 清理可能存在的删除标记，避免重复弹窗
                self?.cleanupDeletionMarksIfNeeded(for: userID)
            }
        }
    }
    
    /**
     * 处理登录失败
     */
    func handleLoginFailure(_ error: Error) {
        DispatchQueue.main.async {
            self.isLoading = false
            print("❌ 登录失败: \(error.localizedDescription)")
        }
    }
    
    /**
     * 退出登录
     */
    func logout() {
        // 清除UserDefaults中的登录信息
        userDefaults.removeObject(forKey: UserDefaultsKeys.isLoggedIn)
        userDefaults.removeObject(forKey: UserDefaultsKeys.appleUserID)
        userDefaults.removeObject(forKey: UserDefaultsKeys.userName)
        userDefaults.removeObject(forKey: UserDefaultsKeys.userEmail)
        
        // 清除Keychain中的敏感信息
        keychainManager.clearLoginInfo()
        
        // 更新状态
        DispatchQueue.main.async {
            self.currentUser = nil
            self.isLoggedIn = false
            print("🚪 用户已退出登录（UserDefaults和Keychain已清除）")
        }
    }
    
    /**
     * 获取用户邮箱
     * 优先从当前用户获取，如果没有则从UserDefaults获取
     */
    func getUserEmail() -> String? {
        // 优先从当前用户获取邮箱
        if let email = currentUser?.email, !email.isEmpty {
            return email
        }
        
        // 从UserDefaults获取保存的邮箱
        if let savedEmail = userDefaults.string(forKey: UserDefaultsKeys.userEmail), !savedEmail.isEmpty {
            return savedEmail
        }
        
        return nil
    }
    
    /**
     * 清理删除标记（如果需要）
     * 当用户重新登录时，清理可能存在的删除标记，避免重复弹窗
     */
    private func cleanupDeletionMarksIfNeeded(for appleUserID: String) {
        // 异步执行，不阻塞登录流程
        DispatchQueue.global(qos: .background).async {
            AccountDeletionManager.shared.cleanupDeletionMarks(for: appleUserID) { success in
                if success {
                    print("🧹 删除标记清理完成")
                } else {
                    print("⚠️ 删除标记清理失败，但不影响登录")
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 验证Apple登录状态
     */
    private func verifyAppleLoginStatus(userID: String) {
        let provider = ASAuthorizationAppleIDProvider()
        provider.getCredentialState(forUserID: userID) { [weak self] credentialState, error in
            DispatchQueue.main.async {
                switch credentialState {
                case .authorized:
                    // Apple登录状态有效，加载用户数据
                    self?.loadUserData(appleUserID: userID)
                case .revoked, .notFound:
                    // Apple登录状态无效，清除本地状态
                    self?.clearInvalidLoginState()
                case .transferred:
                    // 用户账号已转移，需要重新登录
                    self?.clearInvalidLoginState()
                @unknown default:
                    // 未知状态，清除本地状态
                    self?.clearInvalidLoginState()
                }
            }
        }
    }
    
    /**
     * 加载用户数据
     */
    private func loadUserData(appleUserID: String) {
        // 首先执行数据一致性检查，清理重复用户
        coreDataManager.performDataConsistencyCheck()
        
        // 从CoreData中查找用户
        let user = coreDataManager.getUserByAppleID(appleUserID)
        
        if let user = user {
            self.currentUser = user
            self.isLoggedIn = true
            print("✅ 用户数据加载成功: \(user.name ?? "Unknown") (Apple ID: \(appleUserID))")
            
            // 关联现有数据（如果有未关联的数据）
            coreDataManager.linkUserWithExistingData(appleUserID: appleUserID, user: user)
            
            // 同步到CoreDataManager
            coreDataManager.setCurrentUser(user)
            
        } else {
            // 用户数据不存在，检查是否有未关联的用户数据可以迁移
            checkAndMigrateUnlinkedData(appleUserID: appleUserID) { [weak self] user in
                DispatchQueue.main.async {
                    self?.currentUser = user
                    self?.isLoggedIn = true
                    print("✅ 用户数据迁移完成: \(user?.name ?? "Unknown")")
                    
                    // 同步到CoreDataManager
                    if let user = user {
                        self?.coreDataManager.setCurrentUser(user)
                    }
                }
            }
        }
        
        self.isLoading = false
    }
    
    /**
     * 清除无效的登录状态
     */
    func clearInvalidLoginState() {
        userDefaults.removeObject(forKey: UserDefaultsKeys.isLoggedIn)
        userDefaults.removeObject(forKey: UserDefaultsKeys.appleUserID)
        userDefaults.removeObject(forKey: UserDefaultsKeys.userName)
        userDefaults.removeObject(forKey: UserDefaultsKeys.userEmail)
        
        // 清除Keychain中的敏感信息
        keychainManager.clearLoginInfo()
        
        self.currentUser = nil
        self.isLoggedIn = false
        self.isLoading = false
        
        print("🔄 已清除无效的登录状态（UserDefaults和Keychain已清除）")
    }
    
    /**
     * 创建或更新用户
     */
    private func createOrUpdateUser(
        appleUserID: String,
        fullName: PersonNameComponents?,
        email: String?,
        completion: @escaping (User?) -> Void
    ) {
        let context = coreDataManager.viewContext
        
        // 查找现有用户
        let existingUser = coreDataManager.getUserByAppleID(appleUserID)
        
        let user: User
        if let existingUser = existingUser {
            // 更新现有用户
            user = existingUser
        } else {
            // 创建新用户
            user = User(context: context)
            user.appleUserID = appleUserID
            user.createdAt = Date()
            user.subscriptionLevel = "free" // 默认免费用户
        }
        
        // 更新用户信息
        if let fullName = fullName {
            let displayName = PersonNameComponentsFormatter().string(from: fullName)
            if !displayName.isEmpty {
                user.name = displayName
            }
        }
        
        if let email = email {
            user.email = email
        }
        
        user.lastLoginAt = Date()
        
        // 保存到CoreData
        do {
            try context.save()
            completion(user)
            print("💾 用户信息保存成功")
        } catch {
            print("❌ 用户信息保存失败: \(error)")
            completion(nil)
        }
    }
    
    /**
     * 检查并迁移未关联的数据
     */
    private func checkAndMigrateUnlinkedData(appleUserID: String, completion: @escaping (User?) -> Void) {
        // 查找是否有未关联Apple ID的用户数据
        let context = coreDataManager.viewContext
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "appleUserID == nil OR appleUserID == ''")
        
        do {
            let unlinkedUsers = try context.fetch(request)
            
            if let firstUnlinkedUser = unlinkedUsers.first {
                // 将第一个未关联用户与Apple ID关联
                firstUnlinkedUser.appleUserID = appleUserID
                
                // 更新用户信息
                let savedUserName = userDefaults.string(forKey: UserDefaultsKeys.userName)
                let savedEmail = userDefaults.string(forKey: UserDefaultsKeys.userEmail)
                
                if let savedUserName = savedUserName {
                    firstUnlinkedUser.name = savedUserName
                }
                if let savedEmail = savedEmail {
                    firstUnlinkedUser.email = savedEmail
                }
                
                firstUnlinkedUser.lastLoginAt = Date()
                
                // 删除其他未关联用户（避免重复数据）
                for i in 1..<unlinkedUsers.count {
                    context.delete(unlinkedUsers[i])
                }
                
                try context.save()
                print("🔄 已将现有数据关联到Apple ID: \(appleUserID)")
                print("📊 关联的班级数量: \(firstUnlinkedUser.classes?.count ?? 0)")
                completion(firstUnlinkedUser)
                
            } else {
                // 没有未关联数据，创建新用户
                createDefaultUser(appleUserID: appleUserID, completion: completion)
            }
            
        } catch {
            print("❌ 数据迁移检查失败: \(error)")
            // 回退到创建新用户
            createDefaultUser(appleUserID: appleUserID, completion: completion)
        }
    }
    
    /**
     * 创建默认用户
     */
    private func createDefaultUser(appleUserID: String, completion: @escaping (User?) -> Void) {
        let savedUserName = userDefaults.string(forKey: UserDefaultsKeys.userName)
        let savedEmail = userDefaults.string(forKey: UserDefaultsKeys.userEmail)
        
        createOrUpdateUser(
            appleUserID: appleUserID,
            fullName: nil,
            email: savedEmail
        ) { user in
            if let user = user, let savedUserName = savedUserName {
                user.name = savedUserName
                try? self.coreDataManager.viewContext.save()
            }
            print("🆕 创建新用户: \(user?.name ?? "Unknown") (Apple ID: \(appleUserID))")
            completion(user)
        }
    }
}

// MARK: - AuthenticationManager Registry

/**
 * AuthenticationManager注册中心
 * 解决CoreDataManager和AuthenticationManager之间的循环依赖问题
 */
class AuthenticationManagerRegistry {
    static let shared = AuthenticationManagerRegistry()
    
    weak var currentManager: AuthenticationManager?
    
    private init() {}
    
    func register(_ manager: AuthenticationManager) {
        currentManager = manager
        print("📝 已注册AuthenticationManager")
    }
    
    func unregister() {
        currentManager = nil
        print("🗑️ 已注销AuthenticationManager")
    }
}

// MARK: - CoreDataManager Extension

extension CoreDataManager {
    
    /**
     * 根据Apple用户ID查找用户
     */
    func getUserByAppleID(_ appleUserID: String) -> User? {
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "appleUserID == %@", appleUserID)
        request.fetchLimit = 1
        
        do {
            let users = try viewContext.fetch(request)
            return users.first
        } catch {
            print("❌ 查找用户失败: \(error)")
            return nil
        }
    }
    
    /**
     * 根据Apple ID关联用户和现有数据
     */
    func linkUserWithExistingData(appleUserID: String, user: User) {
        // 查找是否有其他用户的数据需要迁移
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "appleUserID == nil OR appleUserID == ''")
        
        do {
            let unlinkedUsers = try viewContext.fetch(request)
            
            for unlinkedUser in unlinkedUsers {
                if unlinkedUser != user {
                    // 迁移班级数据
                    if let classes = unlinkedUser.classes?.allObjects as? [SchoolClass] {
                        for schoolClass in classes {
                            schoolClass.owner = user
                            print("🔄 迁移班级: \(schoolClass.name ?? "Unknown") 到用户: \(user.name ?? "Unknown")")
                        }
                    }
                    
                    // 迁移订阅信息
                    if let subscription = unlinkedUser.subscription {
                        if user.subscription == nil {
                            subscription.user = user
                            user.subscription = subscription
                            print("🔄 迁移订阅信息到用户: \(user.name ?? "Unknown")")
                        }
                    }
                    
                    // 删除旧用户
                    viewContext.delete(unlinkedUser)
                    print("🗑️ 删除未关联用户")
                }
            }
            
            save()
            print("✅ 用户数据关联完成")
            
        } catch {
            print("❌ 数据关联失败: \(error)")
        }
    }
    
    /**
     * 执行数据迁移和用户关联检查
     */
    func checkAndMigrateUnlinkedData() {
        print("🔍 开始用户数据迁移检查...")
        
        // 检查是否有多个用户绑定到同一个Apple ID
        checkDuplicateAppleIDUsers()
        
        // 检查是否有孤立的数据（没有关联用户的班级等）
        checkOrphanedData()
        
        // 检查用户数据完整性
        checkUserDataIntegrity()
        
        print("✅ 用户数据迁移检查完成")
    }
    
    /**
     * 检查重复Apple ID用户
     */
    private func checkDuplicateAppleIDUsers() {
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "appleUserID != nil AND appleUserID != ''")
        
        do {
            let usersWithAppleID = try viewContext.fetch(request)
            let groupedUsers = Dictionary(grouping: usersWithAppleID) { $0.appleUserID }
            
            for (appleID, users) in groupedUsers {
                if users.count > 1 {
                    print("⚠️ 发现重复Apple ID用户: \(appleID ?? "Unknown"), 数量: \(users.count)")
                    mergeDuplicateUsers(users)
                }
            }
        } catch {
            print("❌ 检查重复Apple ID用户失败: \(error)")
        }
    }
    
    /**
     * 合并重复用户数据
     */
    private func mergeDuplicateUsers(_ users: [User]) {
        guard users.count > 1 else { return }
        
        // 选择最新的用户作为主用户
        let primaryUser = users.max { $0.lastLoginAt ?? Date.distantPast < $1.lastLoginAt ?? Date.distantPast }!
        let duplicateUsers = users.filter { $0 != primaryUser }
        
        for duplicateUser in duplicateUsers {
            // 迁移班级数据
            if let classes = duplicateUser.classes?.allObjects as? [SchoolClass] {
                for schoolClass in classes {
                    schoolClass.owner = primaryUser
                }
            }
            
            // 迁移订阅信息（如果主用户没有）
            if primaryUser.subscription == nil, let subscription = duplicateUser.subscription {
                subscription.user = primaryUser
                primaryUser.subscription = subscription
            }
            
            // 删除重复用户
            viewContext.delete(duplicateUser)
            print("🔄 合并重复用户数据到: \(primaryUser.name ?? "Unknown")")
        }
        
        save()
    }
    
    /**
     * 检查孤立数据
     */
    private func checkOrphanedData() {
        // 检查没有关联用户的班级
        let classRequest: NSFetchRequest<SchoolClass> = SchoolClass.fetchRequest()
        classRequest.predicate = NSPredicate(format: "owner == nil")
        
        do {
            let orphanedClasses = try viewContext.fetch(classRequest)
            if !orphanedClasses.isEmpty {
                print("⚠️ 发现 \(orphanedClasses.count) 个孤立班级")
                fixOrphanedClasses(orphanedClasses)
            }
        } catch {
            print("❌ 检查孤立班级失败: \(error)")
        }
    }
    
    /**
     * 修复孤立班级
     */
    private func fixOrphanedClasses(_ orphanedClasses: [SchoolClass]) {
        // 获取当前登录用户或第一个用户
        guard let currentUser = getCurrentUser() else {
            print("❌ 无法修复孤立班级：没有可用用户")
            return
        }
        
        for orphanedClass in orphanedClasses {
            orphanedClass.owner = currentUser
            print("🔧 修复孤立班级: \(orphanedClass.name ?? "Unknown") -> \(currentUser.name ?? "Unknown")")
        }
        
        save()
    }
    
    /**
     * 检查用户数据完整性
     */
    private func checkUserDataIntegrity() {
        let request: NSFetchRequest<User> = User.fetchRequest()
        
        do {
            let allUsers = try viewContext.fetch(request)
            
            for user in allUsers {
                // 检查订阅信息
                if user.subscription == nil {
                    let subscription = Subscription.createFreeSubscription(for: user, in: viewContext)
                    user.subscription = subscription
                    print("🔧 为用户创建默认订阅: \(user.name ?? "Unknown")")
                }
                
                // 检查基本信息
                if user.id == nil {
                    user.id = UUID()
                    print("🔧 为用户生成UUID: \(user.name ?? "Unknown")")
                }
                
                if user.createdAt == nil {
                    user.createdAt = Date()
                    print("🔧 为用户设置创建时间: \(user.name ?? "Unknown")")
                }
            }
            
            save()
        } catch {
            print("❌ 检查用户数据完整性失败: \(error)")
        }
    }
}