//
//  HomeViewModel.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/6/23.
//

import SwiftUI
import Foundation
import Combine
import CoreData

/**
 * 学生排序类型枚举
 */
enum StudentSortType {
    case byStudentNumber // 按学号排序
    case byScore        // 按积分排序
}

/**
 * 首页视图模型 - 管理首页的所有状态和数据
 * 使用CoreData+CloudKit进行数据管理
 */
class HomeViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var classes: [SchoolClass] = []
    @Published var selectedClassIndex: Int = 0
    @Published var searchText: String = ""
    @Published var isSearching: Bool = false
    @Published var selectedTabIndex: Int = 0
    @Published var isInitialLoad: Bool = true
    @Published var isClassSwitching: Bool = false
    @Published var isRefreshing: Bool = false
    
    // MARK: - 排序相关属性
    @Published var sortType: StudentSortType = .byStudentNumber
    @Published var showSortOptions: Bool = false
    
    // MARK: - Delete Mode Properties
    @Published var isDeleteMode: Bool = false
    @Published var showDeleteConfirmation: Bool = false
    @Published var studentToDelete: Student? = nil
    
    // MARK: - Date Range Properties
    @Published var selectedDateRange: DateRangeType = .thisMonth
    @Published var showDateRangePicker: Bool = false
    @Published var currentRangeTotalScore: Int = 0
    
    // MARK: - Add Student Properties
    @Published var showAddStudentOptions: Bool = false
    @Published var showManualAddStudent: Bool = false
    @Published var showExcelImport: Bool = false
    @Published var showNoClassAlert: Bool = false
    @Published var addStudentError: String? = nil
    @Published var isProcessingStudents: Bool = false
    
    // MARK: - Create Class Properties
    @Published var showCreateClassDialog: Bool = false
    @Published var showPermissionDeniedAlert: Bool = false
    @Published var showSubscriptionView: Bool = false
    @Published var className: String = ""
    @Published var isCreatingClass: Bool = false
    @Published var createClassErrorMessage: String? = nil
    @Published var showCreateClassSuccessAlert: Bool = false
    
    // MARK: - Class Operation Properties
    @Published var showClassOperationOptions: Bool = false
    @Published var showClassOperationForm: Bool = false
    @Published var classOperationType: ClassOperationType = .add
    @Published var isSubmittingClassOperation: Bool = false
    @Published var classOperationError: String? = nil
    
    // MARK: - 班级冻结管理相关属性
    @Published var showClassFreezeSelection: Bool = false
    @Published var showClassUnfreezeSelection: Bool = false
    @Published var frozenClasses: [SchoolClass] = []
    @Published var activeClasses: [SchoolClass] = []
    @Published var allowedActiveClassCount: Int = 1
    @Published var availableUnfreezeCount: Int = 0
    @Published var previousSubscriptionLevelName: String = ""
    @Published var currentSubscriptionLevelName: String = ""
    
    // MARK: - CoreData Manager
    private let coreDataManager = CoreDataManager.shared
    private let classManagementService = ClassManagementService.shared
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let searchDebounceTime: TimeInterval = 0.5
    private var currentUser: User?
    
    // MARK: - Computed Properties
    
    /**
     * 当前选中的班级
     */
    var selectedClass: SchoolClass? {
        guard selectedClassIndex >= 0 && selectedClassIndex < classes.count else { return nil }
        return classes[selectedClassIndex]
    }
    
    /**
     * 当前班级的学生列表（已过滤）
     */
    var filteredStudents: [Student] {
        guard let currentClass = selectedClass else { return [] }
        
        // 先根据搜索文本过滤
        let filteredBySearch = currentClass.filteredStudents(searchText: searchText)
        
        // 再根据排序类型排序
        switch sortType {
        case .byStudentNumber:
            return filteredBySearch.sorted { 
                ($0.studentNumber ?? "") < ($1.studentNumber ?? "") 
            }
        case .byScore:
            return filteredBySearch.sorted { 
                $0.point > $1.point
            }
        }
    }
    
    /**
     * 当前班级的总积分
     */
    var currentClassTotalScore: Int {
        return selectedClass?.totalPoints ?? 0
    }
    
    /**
     * 用户订阅级别显示名称
     */
    private var userLevelDisplayName: String {
        guard let user = currentUser else { return "create_class.permission_denied.free_user".localized }
        
        switch user.subscriptionLevel {
        case "free":
            return "create_class.permission_denied.free_user".localized
        case "basic":
            return "create_class.permission_denied.basic_user".localized
        case "premium":
            return "create_class.permission_denied.premium_user".localized
        default:
            return "create_class.permission_denied.free_user".localized
        }
    }
    
    // MARK: - Initialization
    
    init() {
        setupSearchDebounce()
        loadUserAndClasses()
        setupDateRangeObserver()
        setupNotificationObservers()
    }
    
    // MARK: - Data Loading
    
    /**
     * 加载用户和班级数据
     */
    private func loadUserAndClasses() {
        // 获取或创建默认用户
        currentUser = coreDataManager.getOrCreateDefaultUser()
        
        // 加载用户的日期范围偏好
        loadUserDateRangePreference()
        
        // 加载班级数据
        loadClasses()
        
        // 等待数据初始化完成
        if coreDataManager.isInitialized {
            isInitialLoad = false
        } else {
            // 监听数据初始化完成
            coreDataManager.$isInitialized
                .receive(on: DispatchQueue.main)
                .sink { [weak self] isInitialized in
                    if isInitialized {
                        self?.loadClasses()
                        self?.loadUserDateRangePreference()
                        self?.isInitialLoad = false
                    }
                }
                .store(in: &cancellables)
        }
    }
    
    /**
     * 加载班级数据
     */
    private func loadClasses() {
        guard let user = currentUser else { return }
        
        // 修改为只获取活跃状态的班级，而不是所有班级
        classes = classManagementService.getActiveClasses(for: user)
        
        // 确保选中索引有效
        if !classes.isEmpty && selectedClassIndex >= classes.count {
            selectedClassIndex = 0
        } else if classes.isEmpty {
            selectedClassIndex = -1
        }
        
        print("加载班级数据完成，共 \(classes.count) 个活跃班级，选中索引: \(selectedClassIndex)")
    }
    
    /**
     * 获取班级列表
     * 只显示活跃状态的班级
     */
    private func fetchClasses() {
        guard let user = coreDataManager.getCurrentUser() else {
            print("⚠️ 未获取到当前用户")
            return
        }
        
        currentUser = user
        
        // 只获取活跃状态的班级
        classes = classManagementService.getActiveClasses(for: user)
        
        // 如果选中的班级索引超出范围，重置为0
        if selectedClassIndex >= classes.count {
            selectedClassIndex = classes.isEmpty ? -1 : 0
        }
        
        // 如果没有班级，显示创建班级提示
        if classes.isEmpty && !isCreatingClass {
            showNoClassAlert = true
        }
        
        print("📊 获取到\(classes.count)个活跃班级")
    }
    
    // MARK: - Public Methods
    
    /**
     * 切换选中的班级
     */
    func selectClass(at index: Int) {
        guard index < classes.count else { return }
        
        // 设置班级切换状态
        isClassSwitching = true
        
        // 首次切换后禁用初始加载动画
        if isInitialLoad {
            isInitialLoad = false
        }
        
        selectedClassIndex = index
        
        // 切换班级时清空搜索
        searchText = ""
        
        // 更新当前班级的时间范围内总积分
        refreshRangeTotalScore()
        
        // 延迟恢复班级切换状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.isClassSwitching = false
        }
        
        print("班级切换: 选中索引 \(index), 班级名称: \(classes[index].name ?? "未知")")
    }
    
    /**
     * 开始搜索
     */
    func startSearch() {
        isSearching = true
    }
    
    /**
     * 结束搜索
     */
    func endSearch() {
        isSearching = false
        searchText = ""
    }
    
    /**
     * 添加学生到当前班级
     */
    func addStudent(name: String, studentNumber: String, gender: String) {
        guard let currentClass = selectedClass else { return }
        
        let newStudent = coreDataManager.createStudent(
            name: name,
            number: studentNumber,
            gender: gender,
            in: currentClass
        )
        
        // 刷新班级数据
        refreshCurrentClass()
        
        print("添加学生: \(newStudent.name ?? "未知")")
    }
    
    // MARK: - Add Student Methods
    
    /**
     * 处理添加学生按钮点击
     */
    func handleAddStudent() {
        // 清除之前的错误信息
        addStudentError = nil
        
        // 检查是否已创建班级
        if classes.isEmpty {
            showNoClassAlert = true
            print("未创建班级，显示提示弹窗")
        } else {
            showAddStudentOptions = true
            print("显示添加学生选项菜单")
        }
    }
    
    /**
     * 显示手动添加学生表单
     */
    func showManualAddStudentForm() {
        showAddStudentOptions = false
        
        // 延迟显示表单，确保选项菜单先关闭
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.showManualAddStudent = true
            print("显示手动添加学生表单")
        }
    }
    
    /**
     * 显示Excel导入界面
     */
    func showExcelImportView() {
        showAddStudentOptions = false
        
        // 延迟显示导入界面，确保选项菜单先关闭
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.showExcelImport = true
            print("显示Excel导入界面")
        }
    }
    
    /**
     * 批量添加学生
     */
    func addStudents(_ studentForms: [StudentFormData]) {
        guard let currentClass = selectedClass else {
            addStudentError = "add_student.error.no_class".localized
            return
        }
        
        // 设置处理状态
        isProcessingStudents = true
        addStudentError = nil
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            var successCount = 0
            var errorCount = 0
            var duplicateNumbers: [String] = []
            
            for studentForm in studentForms {
                // 检查学号是否已存在
                if self.coreDataManager.isStudentNumberExists(studentForm.formattedStudentNumber, in: currentClass) {
                    duplicateNumbers.append(studentForm.formattedStudentNumber)
                    errorCount += 1
                    continue
                }
                
                // 创建学生
                print("🎓 创建学生: \(studentForm.formattedName), 学号: \(studentForm.formattedStudentNumber), 初始积分: \(studentForm.initialPointsValue)")
                let student = self.coreDataManager.createStudent(
                    name: studentForm.formattedName,
                    number: studentForm.formattedStudentNumber,
                    gender: studentForm.gender,
                    initialPoints: studentForm.initialPointsValue,
                    in: currentClass
                )
                print("✅ 学生创建完成: \(student.name ?? "Unknown"), 当前积分: \(student.point)")
                successCount += 1
            }
            
            DispatchQueue.main.async {
                self.isProcessingStudents = false
                
                if errorCount > 0 {
                    if !duplicateNumbers.isEmpty {
                        self.addStudentError = "add_student.error.duplicate_numbers".localized(with: duplicateNumbers.joined(separator: ", "))
                    } else {
                        self.addStudentError = "add_student.error.partial_failure".localized(with: "\(successCount)", "\(errorCount)")
                    }
                }
                
                if successCount > 0 {
                    // 刷新班级数据
                    self.refreshCurrentClass()
                    
                    // 如果全部成功，关闭表单
                    if errorCount == 0 {
                        self.showManualAddStudent = false
                    }
                }
                
                print("批量添加学生完成: 成功 \(successCount) 个，失败 \(errorCount) 个")
            }
        }
    }
    
    /**
     * 导入Excel数据
     */
    func importStudentsFromExcel(_ data: Data) {
        print("📊 开始导入Excel数据，数据大小: \(data.count) bytes")

        // 设置处理状态
        isProcessingStudents = true
        addStudentError = nil

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            // 解析Excel数据
            let parseResult = ExcelParser.parseExcelData(data)

            DispatchQueue.main.async {
                switch parseResult {
                case .success(let studentForms):
                    print("✅ Excel解析成功，解析到 \(studentForms.count) 个学生")
                    // 使用解析的数据添加学生
                    self.addStudents(studentForms)

                    // 如果解析成功，关闭导入界面
                    self.showExcelImport = false

                case .failure(let error):
                    print("❌ Excel解析失败: \(error)")
                    print("❌ 错误详情: \(error.localizedDescription)")
                    if let recoverySuggestion = error.recoverySuggestion {
                        print("💡 恢复建议: \(recoverySuggestion)")
                    }

                    self.isProcessingStudents = false
                    // 使用具体的错误信息而不是通用的解析错误
                    self.addStudentError = error.localizedDescription
                }
            }
        }
    }
    
    /**
     * 处理创建班级请求（从无班级提示弹窗或创建班级按钮）
     */
    func handleCreateClassFromAlert() {
        showNoClassAlert = false
        
        // 检查会员权限
        guard let user = currentUser else {
            print("无法创建班级：用户不存在")
            return
        }
        
        if !user.canCreateMoreClasses() {
            showPermissionDeniedAlert = true
            return
        }
        
        // 显示创建班级弹窗
        className = ""
        showCreateClassDialog = true
        
        print("显示创建班级弹窗")
    }
    
    /**
     * 执行创建班级
     */
    func createClass() {
        // 验证班级名称
        let trimmedName = className.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if trimmedName.isEmpty {
            createClassErrorMessage = "create_class.validation.name_empty".localized
            return
        }
        
        if trimmedName.count > 30 {
            createClassErrorMessage = "create_class.validation.name_too_long".localized
            return
        }
        
        // 检查班级名称是否已存在
        if classes.contains(where: { $0.name == trimmedName }) {
            createClassErrorMessage = "create_class.validation.name_exists".localized
            return
        }
        
        // 开始创建班级
        isCreatingClass = true
        
        guard let user = currentUser else {
            createClassErrorMessage = "创建班级失败：用户不存在"
            isCreatingClass = false
            return
        }
        
        // 使用CoreData创建班级
        let newClass = coreDataManager.createClass(name: trimmedName, for: user)
        
        // 重新加载班级数据
        loadClasses()
        
        // 选中新创建的班级
        if let newIndex = classes.firstIndex(where: { $0.id == newClass.id }) {
            selectedClassIndex = newIndex
        }
        
        // 重置状态
        isCreatingClass = false
        showCreateClassDialog = false
        resetCreateClassState()
        
        // 显示成功消息
        showCreateClassSuccessAlert = true
        
        print("班级创建成功: \(trimmedName)")
    }
    
    /**
     * 重置创建班级状态
     */
    func resetCreateClassState() {
        className = ""
        isCreatingClass = false
        createClassErrorMessage = nil
    }
    
    /**
     * 获取权限不足提示消息
     */
    var permissionDeniedMessage: String {
        guard let user = currentUser else { return "" }
        
        return String(format: "create_class.permission_denied.message".localized,
                     userLevelDisplayName,
                     user.maxClassesAllowed,
                     classes.count)
    }
    
    /**
     * 关闭添加学生选项菜单
     */
    func closeAddStudentOptions() {
        showAddStudentOptions = false
        print("关闭添加学生选项菜单")
    }
    
    /**
     * 关闭手动添加学生表单
     */
    func closeManualAddStudent() {
        showManualAddStudent = false
        addStudentError = nil
        print("关闭手动添加学生表单")
    }
    
    /**
     * 关闭Excel导入界面
     */
    func closeExcelImport() {
        showExcelImport = false
        addStudentError = nil
        print("关闭Excel导入界面")
    }
    
    /**
     * 关闭无班级提示弹窗
     */
    func closeNoClassAlert() {
        showNoClassAlert = false
        print("关闭无班级提示弹窗")
    }
    
    /**
     * 清除添加学生错误信息
     */
    func clearAddStudentError() {
        addStudentError = nil
    }
    
    /**
     * 从当前班级移除学生
     */
    func removeStudent(_ student: Student) {
        coreDataManager.deleteStudent(student)
        
        // 刷新班级数据
        refreshCurrentClass()
        
        print("删除学生: \(student.name ?? "未知")")
    }
    
    /**
     * 全班加分
     */
    func addScoreToAllStudents(_ score: Int, reason: String = "全班加分") {
        guard let currentClass = selectedClass else { return }
        
        currentClass.addPointsToAllStudents(score, reason: reason, in: coreDataManager.viewContext)
        coreDataManager.save()
        
        // 刷新班级数据
        refreshCurrentClass()
        
        // 实时更新时间范围内的积分统计
        refreshRangeTotalScore()
        
        // 发送班级统计刷新通知
        sendClassStatisticsRefreshNotification(triggerSource: "class_operation")
        
        print("全班加分: +\(score) 分，原因: \(reason)")
    }
    
    /**
     * 全班扣分
     */
    func deductScoreFromAllStudents(_ score: Int, reason: String = "全班扣分") {
        guard let currentClass = selectedClass else { return }
        
        currentClass.deductPointsFromAllStudents(score, reason: reason, in: coreDataManager.viewContext)
        coreDataManager.save()
        
        // 刷新班级数据
        refreshCurrentClass()
        
        // 实时更新时间范围内的积分统计
        refreshRangeTotalScore()
        
        // 发送班级统计刷新通知
        sendClassStatisticsRefreshNotification(triggerSource: "class_operation")
        
        print("全班扣分: -\(score) 分，原因: \(reason)")
    }
    
    /**
     * 选择Tab页面
     */
    func selectTab(_ index: Int) {
        selectedTabIndex = index
    }
    
    // MARK: - Delete Mode Methods
    
    /**
     * 进入删除模式
     */
    func enterDeleteMode() {
        isDeleteMode = true
        print("进入删除模式")
    }
    
    /**
     * 退出删除模式
     */
    func exitDeleteMode() {
        isDeleteMode = false
        showDeleteConfirmation = false
        studentToDelete = nil
        print("退出删除模式")
    }
    
    /**
     * 请求删除学生
     */
    func requestDeleteStudent(_ student: Student) {
        studentToDelete = student
        showDeleteConfirmation = true
        print("请求删除学生: \(student.name ?? "未知")")
    }
    
    /**
     * 确认删除学生
     */
    func confirmDeleteStudent() {
        guard let student = studentToDelete else { return }
        
        removeStudent(student)
        exitDeleteMode()
        
        print("确认删除学生: \(student.name ?? "未知")")
    }
    
    /**
     * 取消删除学生
     */
    func cancelDeleteStudent() {
        showDeleteConfirmation = false
        studentToDelete = nil
        print("取消删除学生")
    }
    
    // MARK: - Student Data Methods
    
    /**
     * 根据学生ID获取学生信息
     */
    func getStudentById(_ studentId: String) -> Student? {
        guard let uuid = UUID(uuidString: studentId) else { return nil }
        return coreDataManager.getStudent(by: uuid)
    }
    
    /**
     * 刷新学生数据
     */
    func refreshStudentData() async {
        await MainActor.run {
            refreshCurrentClass()
        }
        print("刷新学生数据完成")
    }

    /**
     * 下拉刷新数据
     * 完整的数据刷新流程，包括班级数据、学生数据和积分统计
     */
    func pullToRefreshData() async {
        // 防止重复刷新
        await MainActor.run {
            if isRefreshing {
                print("⚠️ 正在刷新中，跳过重复请求")
                return
            }
            isRefreshing = true
        }

        print("🔄 开始下拉刷新数据...")

        await MainActor.run {
            // 1. 重新加载班级数据
            loadClasses()

            // 2. 如果有选中的班级，刷新当前班级数据
            if let selectedClass = selectedClass {
                // 刷新CoreData对象，确保获取最新数据
                coreDataManager.viewContext.refresh(selectedClass, mergeChanges: true)

                // 刷新当前班级的所有学生数据
                if let students = selectedClass.students as? Set<Student> {
                    for student in students {
                        coreDataManager.viewContext.refresh(student, mergeChanges: true)
                    }
                }
            }

            // 3. 更新积分统计
            refreshRangeTotalScore()

            // 4. 触发UI更新
            objectWillChange.send()

            print("✅ 下拉刷新完成 - 班级数: \(classes.count), 当前班级学生数: \(selectedClass?.students?.count ?? 0)")
        }

        // 延迟一小段时间确保刷新动画完成
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

        await MainActor.run {
            isRefreshing = false
        }
    }
    
    /**
     * 处理学生数据变更
     */
    func handleStudentDataChange(_ student: Student) {
        // CoreData会自动处理变更，只需保存
        coreDataManager.save()
        
        // 实时更新时间范围内的积分统计
        refreshRangeTotalScore()
        
        print("处理学生数据变更: \(student.name ?? "未知")")
    }
    
    /**
     * 重新加载班级数据
     * 用于在其他页面创建班级后刷新首页数据
     */
    func reloadClassData() {
        print("重新加载班级数据")
        loadClasses()
        
        // 确保选中索引有效
        if !classes.isEmpty {
            // 如果有班级但选中索引无效，则选中第一个班级
            if selectedClassIndex < 0 || selectedClassIndex >= classes.count {
                selectedClassIndex = 0
                print("修正选中班级索引为: 0")
            }
        } else {
            // 没有班级时设置为-1
            selectedClassIndex = -1
            print("没有班级，设置选中索引为: -1")
        }
        
        // 触发UI更新
        objectWillChange.send()
    }
    
    // MARK: - Private Methods
    
    /**
     * 刷新当前班级数据
     */
    private func refreshCurrentClass() {
        // 刷新CoreData对象
        coreDataManager.viewContext.refresh(selectedClass!, mergeChanges: true)
        
        // 触发UI更新
        objectWillChange.send()
    }
    
    /**
     * 设置搜索防抖
     */
    private func setupSearchDebounce() {
        $searchText
            .debounce(for: .seconds(searchDebounceTime), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] _ in
                // 搜索逻辑已经在 filteredStudents 计算属性中处理
                // 这里只是为了触发UI更新
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Date Range Methods
    
    /**
     * 加载用户的日期范围偏好
     */
    private func loadUserDateRangePreference() {
        guard let user = currentUser else { return }
        
        selectedDateRange = user.getDateRangePreference()
        updateCurrentRangeTotalScore()
        
        print("加载日期范围偏好: \(selectedDateRange.displayText)")
    }
    
    /**
     * 保存用户的日期范围偏好
     */
    private func saveUserDateRangePreference() {
        guard let user = currentUser else { return }
        
        user.saveDateRangePreference(selectedDateRange)
        updateCurrentRangeTotalScore()
        
        print("保存日期范围偏好: \(selectedDateRange.displayText)")
    }
    
    /**
     * 设置日期范围观察者
     */
    private func setupDateRangeObserver() {
        $selectedDateRange
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.saveUserDateRangePreference()
            }
            .store(in: &cancellables)
    }
    
    /**
     * 更新当前时间范围内的总积分
     */
    private func updateCurrentRangeTotalScore() {
        guard let user = currentUser else {
            currentRangeTotalScore = 0
            return
        }
        
        currentRangeTotalScore = user.calculateClassTotalScore(for: selectedDateRange, in: selectedClass)
        print("更新时间范围内总积分: \(currentRangeTotalScore)")
    }
    
    /**
     * 显示日期范围选择器
     */
    func showDateRangeSelector() {
        showDateRangePicker = true
        print("显示日期范围选择器")
    }
    
    /**
     * 隐藏日期范围选择器
     */
    func hideDateRangeSelector() {
        showDateRangePicker = false
        print("隐藏日期范围选择器")
    }
    
    /**
     * 获取当前时间范围的显示文本
     */
    var currentDateRangeDisplayText: String {
        return selectedDateRange.displayText
    }
    
    /**
     * 刷新时间范围内的积分统计（用于实时更新）
     */
    func refreshRangeTotalScore() {
        updateCurrentRangeTotalScore()
    }
    
    // MARK: - Class Operation Methods
    
    /**
     * 处理全班操作按钮点击
     */
    func handleClassOperation() {
        // 清除之前的错误信息
        classOperationError = nil
        
        // 检查是否已创建班级
        if classes.isEmpty {
            showNoClassAlert = true
            print("未创建班级，显示提示弹窗")
        } else {
            showClassOperationOptions = true
            print("显示全班操作选项弹窗")
        }
    }
    
    /**
     * 显示全班加分表单
     */
    func showAddPointsForm() {
        classOperationType = .add
        showClassOperationOptions = false
        showClassOperationForm = true
        print("显示全班加分表单")
    }
    
    /**
     * 显示全班扣分表单
     */
    func showDeductPointsForm() {
        classOperationType = .deduct
        showClassOperationOptions = false
        showClassOperationForm = true
        print("显示全班扣分表单")
    }
    
    /**
     * 提交全班操作
     */
    func submitClassOperation(name: String, value: Int) {
        guard selectedClass != nil else {
            classOperationError = "class_operation.error.no_class_selected".localized
            return
        }
        
        // 设置提交状态
        isSubmittingClassOperation = true
        classOperationError = nil
        
        // 延迟处理，提供用户反馈
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            switch self.classOperationType {
            case .add:
                self.addScoreToAllStudents(value, reason: name)
                print("全班加分操作: +\(value) 分，原因: \(name)")
            case .deduct:
                self.deductScoreFromAllStudents(value, reason: name)
                print("全班扣分操作: -\(value) 分，原因: \(name)")
            }
            
            // 完成操作
            self.isSubmittingClassOperation = false
            self.showClassOperationForm = false
            
            print("全班操作提交成功")
        }
    }
    
    /**
     * 关闭全班操作弹窗
     */
    func closeClassOperationDialogs() {
        showClassOperationOptions = false
        showClassOperationForm = false
        classOperationError = nil
        print("关闭全班操作弹窗")
    }
    
    /**
     * 清除全班操作错误信息
     */
    func clearClassOperationError() {
        classOperationError = nil
    }
    
    // MARK: - 通知监听方法

    /**
     * 设置通知监听器
     */
    private func setupNotificationObservers() {
        // 监听学生积分变更通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleStudentPointsChange(_:)),
            name: .studentPointsDidChange,
            object: nil
        )
        
        // 监听班级统计刷新通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleClassStatisticsRefresh(_:)),
            name: .classStatisticsNeedsRefresh,
            object: nil
        )
        
        // 监听会员降级需要处理班级冻结通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSubscriptionDowngrade(_:)),
            name: .subscriptionDowngradeNeedsAction,
            object: nil
        )
        
        // 监听会员升级可解冻班级通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSubscriptionUpgrade(_:)),
            name: .subscriptionUpgradeClassUnfreeze,
            object: nil
        )
        
        // 监听班级状态变更通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleClassStatusChanged(_:)),
            name: .classStatusChanged,
            object: nil
        )
        
        print("📝 已设置通知监听器")
    }

    /**
     * 处理学生积分变更通知
     */
    @objc private func handleStudentPointsChange(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let studentId = userInfo[NotificationUserInfoKey.studentId] as? String,
              let pointsChange = userInfo[NotificationUserInfoKey.pointsChange] as? Int,
              let reason = userInfo[NotificationUserInfoKey.reason] as? String else {
            print("⚠️ 学生积分变更通知数据不完整")
            return
        }
        
        DispatchQueue.main.async {
            // 实时更新时间范围内的积分统计
            self.refreshRangeTotalScore()
            
            print("📊 已更新统计数据：学生\(studentId) 积分变化\(pointsChange)，原因：\(reason)")
        }
    }

    /**
     * 处理班级统计刷新通知
     */
    @objc private func handleClassStatisticsRefresh(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let classId = userInfo[NotificationUserInfoKey.classId] as? String,
              let triggerSource = userInfo[NotificationUserInfoKey.triggerSource] as? String else {
            print("⚠️ 班级统计刷新通知数据不完整")
            return
        }
        
        // 检查是否为当前选中的班级
        if let currentClassId = selectedClass?.id?.uuidString,
           currentClassId == classId {
            DispatchQueue.main.async {
                // 刷新当前班级数据
                self.refreshCurrentClass()
                // 更新统计数据
                self.refreshRangeTotalScore()
                
                print("📊 已刷新班级统计：班级\(classId)，触发源：\(triggerSource)")
            }
        }
    }

    /**
     * 处理会员降级通知
     */
    @objc private func handleSubscriptionDowngrade(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let oldLevel = userInfo[NotificationUserInfoKey.oldLevel] as? String,
              let newLevel = userInfo[NotificationUserInfoKey.newLevel] as? String else {
            print("⚠️ 会员降级通知数据不完整")
            return
        }
        
        // 设置订阅等级名称
        previousSubscriptionLevelName = oldLevel
        currentSubscriptionLevelName = newLevel
        
        // 获取新等级的订阅类型
        let newSubscriptionLevel = Subscription.Level(rawValue: newLevel) ?? .free
        
        // 设置允许的活跃班级数
        allowedActiveClassCount = newSubscriptionLevel.maxClasses
        
        // 获取活跃班级
        guard let user = currentUser else { return }
        activeClasses = classManagementService.getActiveClasses(for: user)
        
        // 如果活跃班级数量超出新等级限制，显示班级冻结选择界面
        if activeClasses.count > allowedActiveClassCount {
            DispatchQueue.main.async {
                self.showClassFreezeSelection = true
            }
        }
    }
    
    /**
     * 处理会员升级通知
     */
    @objc private func handleSubscriptionUpgrade(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let oldLevel = userInfo[NotificationUserInfoKey.oldLevel] as? String,
              let newLevel = userInfo[NotificationUserInfoKey.newLevel] as? String,
              let availableCount = userInfo[NotificationUserInfoKey.availableUnfreezeCount] as? Int else {
            print("⚠️ 会员升级通知数据不完整")
            return
        }
        
        // 设置订阅等级名称
        previousSubscriptionLevelName = oldLevel
        currentSubscriptionLevelName = newLevel
        
        // 设置可解冻班级数量
        availableUnfreezeCount = availableCount
        
        // 获取冻结的班级
        guard let user = currentUser else { return }
        frozenClasses = classManagementService.getFrozenClasses(for: user)
        
        // 检查是否升级为高级会员
        let isUpgradeToAdvanced = Subscription.Level(rawValue: newLevel) == .premium
        
        // 如果升级为高级会员，自动解冻所有班级
        if isUpgradeToAdvanced && !frozenClasses.isEmpty {
            print("🔓 检测到升级为高级会员，自动解冻所有班级")
            let allFrozenClassIds = frozenClasses.compactMap { $0.id?.uuidString }
            
            // 自动解冻所有班级
            if !allFrozenClassIds.isEmpty {
                let success = classManagementService.handleClassUnfreeze(
                    for: user,
                    unfreezeClassIds: allFrozenClassIds
                )
                
                if success {
                    print("✅ 高级会员自动解冻所有班级成功")
                    // 重新获取班级列表
                    fetchClasses()
                } else {
                    print("❌ 高级会员自动解冻班级失败")
                }
            }
        }
        // 否则，如果有冻结的班级且可解冻数量大于0，显示解冻选择界面
        else if !frozenClasses.isEmpty && availableUnfreezeCount > 0 {
            DispatchQueue.main.async {
                self.showClassUnfreezeSelection = true
            }
        }
    }
    
    /**
     * 处理班级状态变更通知
     */
    @objc private func handleClassStatusChanged(_ notification: Notification) {
        // 重新获取班级列表
        fetchClasses()
    }
    
    /**
     * 清理通知监听器
     */
    private func removeNotificationObservers() {
        NotificationCenter.default.removeObserver(self, name: .studentPointsDidChange, object: nil)
        NotificationCenter.default.removeObserver(self, name: .classStatisticsNeedsRefresh, object: nil)
        NotificationCenter.default.removeObserver(self, name: .subscriptionDowngradeNeedsAction, object: nil)
        NotificationCenter.default.removeObserver(self, name: .subscriptionUpgradeClassUnfreeze, object: nil)
        NotificationCenter.default.removeObserver(self, name: .classStatusChanged, object: nil)
        print("📝 已清理通知监听器")
    }

    /**
     * 发送班级统计刷新通知
     */
    private func sendClassStatisticsRefreshNotification(triggerSource: String) {
        guard let classId = selectedClass?.id?.uuidString else { return }
        
        NotificationCenter.default.post(
            name: .classStatisticsNeedsRefresh,
            object: nil,
            userInfo: [
                NotificationUserInfoKey.classId: classId,
                NotificationUserInfoKey.triggerSource: triggerSource
            ]
        )
    }

    deinit {
        removeNotificationObservers()
    }

    // MARK: - 班级冻结管理方法
    
    /**
     * 处理会员降级冻结班级选择
     */
    func handleClassFreezeSelection(_ selectedClassIds: [String]) {
        guard let user = currentUser else { return }
        
        let oldLevel = Subscription.Level(rawValue: previousSubscriptionLevelName) ?? .free
        let newLevel = Subscription.Level(rawValue: user.subscriptionLevel) ?? .free
        
        let success = classManagementService.handleSubscriptionDowngrade(
            for: user,
            fromLevel: oldLevel,
            toLevel: newLevel,
            keepingActiveClassIds: selectedClassIds
        )
        
        if success {
            // 重新获取班级列表
            fetchClasses()
            
            // 根据选择的班级ID设置当前选中的班级
            if let selectedClassId = selectedClassIds.first,
               let selectedIndex = classes.firstIndex(where: { $0.id?.uuidString == selectedClassId }) {
                selectClass(at: selectedIndex)
            } else if !classes.isEmpty {
                selectClass(at: 0)
            }
            
            // 关闭弹窗
            showClassFreezeSelection = false
        }
    }
    
    /**
     * 处理会员升级解冻班级选择
     */
    func handleClassUnfreezeSelection(_ selectedClassIds: [String]) {
        guard let user = currentUser else { return }
        
        let success = classManagementService.handleClassUnfreeze(
            for: user,
            unfreezeClassIds: selectedClassIds
        )
        
        if success {
            // 重新获取班级列表
            fetchClasses()
            
            // 关闭弹窗
            showClassUnfreezeSelection = false
        }
    }
    
    /**
     * 取消班级解冻
     */
    func cancelClassUnfreeze() {
        showClassUnfreezeSelection = false
    }
    
    /**
     * 打开订阅页面
     */
    func openSubscriptionPage() {
        showSubscriptionView = true
    }

    // MARK: - 排序相关方法

    /**
     * 处理排序选项
     * 当用户点击已选中的班级按钮时调用
     */
    func handleSortOptions() {
        showSortOptions = true
        print("显示排序选项菜单")
    }

    /**
     * 设置排序类型为按学号排序
     */
    func sortByStudentNumber() {
        sortType = .byStudentNumber
        print("设置排序方式：按学号排序")
    }

    /**
     * 设置排序类型为按积分排序
     */
    func sortByScore() {
        sortType = .byScore
        print("设置排序方式：按积分排序")
    }
}