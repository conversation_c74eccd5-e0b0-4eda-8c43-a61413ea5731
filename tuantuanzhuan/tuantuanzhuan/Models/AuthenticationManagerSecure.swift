//
//  AuthenticationManagerSecure.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation
import AuthenticationServices
import SwiftUI
import CoreData

/**
 * 安全认证管理器
 * 使用Keychain存储敏感信息，提供更高的安全性
 */
class AuthenticationManagerSecure: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isLoggedIn: Bool = false
    @Published var currentUser: User?
    @Published var isLoading: Bool = false
    
    // MARK: - Private Properties
    private let coreDataManager = CoreDataManager.shared
    private let keychainManager = KeychainManager.shared
    
    // MARK: - Initialization
    init() {
        // 检查登录状态
        checkLoginStatus()
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查登录状态
     */
    func checkLoginStatus() {
        isLoading = true
        
        // 从Keychain检查登录状态
        let savedLoginStatus = keychainManager.isLoggedIn()
        let savedAppleUserID = keychainManager.getAppleUserID()
        
        print("💾 Keychain中的登录状态: \(savedLoginStatus)")
        print("💾 Keychain中的Apple用户ID: \(savedAppleUserID ?? "无")")
        
        // 如果有Apple用户ID，验证Apple登录状态
        if let appleUserID = savedAppleUserID {
            print("✅ 检测到Apple用户ID，开始验证Apple登录状态")
            // 验证Apple登录状态
            verifyAppleLoginStatus(userID: appleUserID)
        } else {
            print("❌ 未检测到Apple用户ID")
            // 未登录状态
            DispatchQueue.main.async {
                self.isLoggedIn = false
                self.isLoading = false
            }
        }
    }
    
    /**
     * 处理Apple登录成功
     */
    func handleSuccessfulLogin(userID: String, fullName: PersonNameComponents?, email: String?) {
        isLoading = true
        
        // 保存登录信息到Keychain（安全存储）
        let displayName = fullName != nil ? PersonNameComponentsFormatter().string(from: fullName!) : nil
        keychainManager.saveLoginInfo(
            appleUserID: userID,
            userName: displayName,
            userEmail: email
        )
        
        // 创建或更新用户记录
        createOrUpdateUser(appleUserID: userID, fullName: fullName, email: email) { [weak self] user in
            DispatchQueue.main.async {
                self?.currentUser = user
                self?.isLoggedIn = true
                self?.isLoading = false
                
                print("✅ 用户登录成功（安全存储）: \(user?.name ?? "Unknown")")
            }
        }
    }
    
    /**
     * 处理登录失败
     */
    func handleLoginFailure(_ error: Error) {
        DispatchQueue.main.async {
            self.isLoading = false
            print("❌ 登录失败: \(error.localizedDescription)")
        }
    }
    
    /**
     * 退出登录
     */
    func logout() {
        // 清除Keychain中的敏感信息
        keychainManager.clearLoginInfo()
        
        // 更新状态
        DispatchQueue.main.async {
            self.currentUser = nil
            self.isLoggedIn = false
            print("🚪 用户已安全退出登录（Keychain已清除）")
        }
    }
    
    /**
     * 更新用户信息
     */
    func updateUserInfo(name: String? = nil, email: String? = nil) {
        if let name = name {
            keychainManager.updateUserName(name)
            currentUser?.name = name
        }
        
        if let email = email {
            keychainManager.updateUserEmail(email)
            currentUser?.email = email
        }
        
        // 保存到CoreData
        try? coreDataManager.viewContext.save()
        
        print("🔄 用户信息已安全更新")
    }
    
    // MARK: - Private Methods
    
    /**
     * 验证Apple登录状态
     */
    private func verifyAppleLoginStatus(userID: String) {
        let provider = ASAuthorizationAppleIDProvider()
        provider.getCredentialState(forUserID: userID) { [weak self] credentialState, error in
            DispatchQueue.main.async {
                switch credentialState {
                case .authorized:
                    // Apple登录状态有效，加载用户数据
                    self?.loadUserData(appleUserID: userID)
                case .revoked, .notFound:
                    // Apple登录状态无效，清除本地状态
                    self?.clearInvalidLoginState()
                case .transferred:
                    // 用户账号已转移，需要重新登录
                    self?.clearInvalidLoginState()
                @unknown default:
                    // 未知状态，清除本地状态
                    self?.clearInvalidLoginState()
                }
            }
        }
    }
    
    /**
     * 加载用户数据
     */
    private func loadUserData(appleUserID: String) {
        // 从CoreData中查找用户
        let user = coreDataManager.getUserByAppleID(appleUserID)
        
        if let user = user {
            self.currentUser = user
            self.isLoggedIn = true
            print("✅ 用户数据加载成功（安全模式）: \(user.name ?? "Unknown")")
        } else {
            // 用户数据不存在，创建默认用户
            createDefaultUser(appleUserID: appleUserID) { [weak self] user in
                DispatchQueue.main.async {
                    self?.currentUser = user
                    self?.isLoggedIn = true
                }
            }
        }
        
        self.isLoading = false
    }
    
    /**
     * 清除无效的登录状态
     */
    private func clearInvalidLoginState() {
        // 安全清除Keychain中的所有登录信息
        keychainManager.clearLoginInfo()
        
        self.currentUser = nil
        self.isLoggedIn = false
        self.isLoading = false
        
        print("🔄 已安全清除无效的登录状态（Keychain已清空）")
    }
    
    /**
     * 创建或更新用户
     */
    private func createOrUpdateUser(
        appleUserID: String,
        fullName: PersonNameComponents?,
        email: String?,
        completion: @escaping (User?) -> Void
    ) {
        let context = coreDataManager.viewContext
        
        // 查找现有用户
        let existingUser = coreDataManager.getUserByAppleID(appleUserID)
        
        let user: User
        if let existingUser = existingUser {
            // 更新现有用户
            user = existingUser
        } else {
            // 创建新用户
            user = User(context: context)
            user.appleUserID = appleUserID
            user.createdAt = Date()
            user.subscriptionLevel = "free" // 默认免费用户
        }
        
        // 更新用户信息
        if let fullName = fullName {
            let displayName = PersonNameComponentsFormatter().string(from: fullName)
            if !displayName.isEmpty {
                user.name = displayName
            }
        }
        
        if let email = email {
            user.email = email
        }
        
        user.lastLoginAt = Date()
        
        // 保存到CoreData
        do {
            try context.save()
            completion(user)
            print("💾 用户信息保存成功（安全模式）")
        } catch {
            print("❌ 用户信息保存失败: \(error)")
            completion(nil)
        }
    }
    
    /**
     * 创建默认用户
     */
    private func createDefaultUser(appleUserID: String, completion: @escaping (User?) -> Void) {
        // 从Keychain获取保存的用户信息
        let savedUserName = keychainManager.getUserName()
        let savedEmail = keychainManager.getUserEmail()
        
        createOrUpdateUser(
            appleUserID: appleUserID,
            fullName: nil,
            email: savedEmail
        ) { user in
            if let user = user, let savedUserName = savedUserName {
                user.name = savedUserName
                try? self.coreDataManager.viewContext.save()
            }
            completion(user)
        }
    }
    
    // MARK: - Security Features
    
    /**
     * 获取当前存储的用户信息摘要（用于调试）
     */
    func getStoredUserInfoSummary() -> [String: String?] {
        return [
            "appleUserID": keychainManager.getAppleUserID(),
            "userName": keychainManager.getUserName(),
            "userEmail": keychainManager.getUserEmail(),
            "isLoggedIn": String(keychainManager.isLoggedIn())
        ]
    }
    
    /**
     * 验证Keychain数据完整性
     */
    func validateKeychainIntegrity() -> Bool {
        guard keychainManager.isLoggedIn(),
              let appleUserID = keychainManager.getAppleUserID(),
              !appleUserID.isEmpty else {
            print("⚠️ Keychain数据不完整")
            return false
        }
        
        print("✅ Keychain数据完整性验证通过")
        return true
    }
}