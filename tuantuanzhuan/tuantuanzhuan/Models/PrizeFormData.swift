//
//  PrizeFormData.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/16.
//

import Foundation

/**
 * 奖品表单数据模型
 * 用于奖品配置功能的数据传递和验证
 */
struct PrizeFormData: Identifiable, Equatable {
    
    let id = UUID()
    var name: String = ""
    var cost: String = ""
    var type: String = "虚拟" // "实物" 或 "虚拟"
    
    // MARK: - 初始化方法
    init(type: String = "虚拟") {
        self.type = type
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取积分成本的整数值
     */
    var costInt: Int {
        return Int(cost) ?? 10
    }
    
    /**
     * 检查表单数据是否有效
     */
    var isValid: Bool {
        return !formattedName.isEmpty &&
               costInt > 0 &&
               formattedName.count <= 20
    }
    
    /**
     * 获取格式化的奖品名称（去除空格）
     */
    var formattedName: String {
        return name.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /**
     * 获取奖品类型显示名称
     */
    var typeDisplayName: String {
        return type
    }
    
    // MARK: - Validation Methods
    
    /**
     * 验证奖品名称格式
     */
    func validateName() -> ValidationResult {
        let trimmedName = formattedName
        if trimmedName.isEmpty {
            return .invalid("prize_config.validation.name_empty".localized)
        }
        if trimmedName.count > 20 {
            return .invalid("prize_config.validation.name_too_long".localized)
        }
        return .valid
    }
    
    /**
     * 验证积分成本格式
     */
    func validateCost() -> ValidationResult {
        if cost.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return .invalid("prize_config.validation.cost_empty".localized)
        }
        
        guard let intValue = Int(cost.trimmingCharacters(in: .whitespacesAndNewlines)) else {
            return .invalid("prize_config.validation.cost_invalid".localized)
        }
        
        if intValue <= 0 {
            return .invalid("prize_config.validation.cost_positive".localized)
        }
        
        if intValue > 1000 {
            return .invalid("prize_config.validation.cost_too_high".localized)
        }
        
        return .valid
    }
    
    /**
     * 检查奖品名称是否与现有奖品模板重复
     */
    func validateUniqueName(existingPrizes: [PrizeTemplate]) -> ValidationResult {
        let trimmedName = formattedName
        let duplicateExists = existingPrizes.contains { prize in
            guard let prizeName = prize.name else { return false }
            return prizeName.trimmingCharacters(in: .whitespacesAndNewlines).lowercased() == 
                   trimmedName.lowercased() && prize.type == type
        }
        
        if duplicateExists {
            return .invalid("prize_config.validation.duplicate_name".localized)
        }
        
        return .valid
    }
    
    /**
     * 完整验证（包括重复检查）
     */
    func validate(existingPrizes: [PrizeTemplate] = []) -> ValidationResult {
        // 1. 验证名称
        let nameResult = validateName()
        if case .invalid = nameResult {
            return nameResult
        }
        
        // 2. 验证积分成本
        let costResult = validateCost()
        if case .invalid = costResult {
            return costResult
        }
        
        // 3. 验证唯一性
        let uniqueResult = validateUniqueName(existingPrizes: existingPrizes)
        if case .invalid = uniqueResult {
            return uniqueResult
        }
        
        return .valid
    }
}

// MARK: - 批量验证扩展
extension Array where Element == PrizeFormData {
    
    /**
     * 批量验证奖品表单数据
     */
    func validateBatch(existingPrizes: [PrizeTemplate] = []) -> PrizeBatchValidationResult {
        var errorMessages: [String] = []
        var validPrizes: [PrizeFormData] = []
        
        // 检查内部重复
        var seenNames: [String: Int] = [:]
        
        for (index, prize) in self.enumerated() {
            let prizeIndex = index + 1
            let trimmedName = prize.formattedName.lowercased()
            
            // 检查基本验证
            let basicValidation = prize.validate(existingPrizes: existingPrizes)
            if case .invalid(let message) = basicValidation {
                errorMessages.append("prize_config.validation.row_error".localized(with: "\(prizeIndex)", message))
                continue
            }
            
            // 检查批次内重复
            if let firstIndex = seenNames[trimmedName] {
                errorMessages.append("prize_config.validation.row_error".localized(with: "\(prizeIndex)", 
                    "prize_config.validation.internal_duplicate".localized(with: "\(firstIndex)")))
                continue
            }
            
            seenNames[trimmedName] = prizeIndex
            validPrizes.append(prize)
        }
        
        return PrizeBatchValidationResult(
            isValid: errorMessages.isEmpty,
            errorMessages: errorMessages,
            validPrizes: validPrizes
        )
    }
}

// MARK: - 奖品批量验证结果
struct PrizeBatchValidationResult {
    let isValid: Bool
    let errorMessages: [String]
    let validPrizes: [PrizeFormData]
    
    var errorSummary: String {
        return errorMessages.joined(separator: "\n")
    }
} 