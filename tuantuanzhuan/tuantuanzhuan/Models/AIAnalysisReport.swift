//
//  AIAnalysisReport.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation

/**
 * AI分析报告数据模型
 * 存储DeepSeek API返回的学生行为分析结果
 */

/**
 * AI分析报告类型
 */
enum ReportType: String, Codable {
    case professional = "professional" // 专业分析报告（面向老师和家长）
    case parentFeedback = "parentFeedback" // 家长反馈（与家长沟通）
}

struct AIAnalysisReport: Codable, Identifiable {
    let id: UUID
    let studentName: String         // 学生姓名
    let studentGender: String       // 学生性别
    let gradeLevel: String          // 年级信息
    let totalRecords: Int           // 总记录数
    let positiveRecords: Int        // 加分记录数
    let negativeRecords: Int        // 扣分记录数
    let analysisContent: String     // AI生成的分析内容
    let generatedAt: Date           // 生成时间
    let reportType: ReportType      // 报告类型
    
    /**
     * 初始化分析报告
     */
    init(
        studentName: String,
        studentGender: String,
        gradeLevel: String,
        statistics: AnalysisStatistics,
        analysisContent: String,
        reportType: ReportType = .professional
    ) {
        self.id = UUID()
        self.studentName = studentName
        self.studentGender = studentGender
        self.gradeLevel = gradeLevel
        self.totalRecords = statistics.totalRecords
        self.positiveRecords = statistics.positiveRecords
        self.negativeRecords = statistics.negativeRecords
        self.analysisContent = analysisContent
        self.generatedAt = Date()
        self.reportType = reportType
    }
    
    /**
     * 格式化生成时间
     */
    var formattedGeneratedTime: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale.current
        return formatter.string(from: generatedAt)
    }
    
    /**
     * 学生性别显示文本
     */
    var genderDisplayText: String {
        return studentGender == "male" ? "student_detail.gender_male".localized : "student_detail.gender_female".localized
    }
    
    /**
     * 积极行为比例
     */
    var positiveRatio: Double {
        guard totalRecords > 0 else { return 0.0 }
        return Double(positiveRecords) / Double(totalRecords) * 100
    }
    
    /**
     * 格式化的积极行为比例文本
     */
    var formattedPositiveRatio: String {
        return String(format: "%.1f%%", positiveRatio)
    }
    
    /**
     * 报告标题
     */
    var reportTitle: String {
        switch reportType {
        case .professional:
            return "ai_analysis.report_title".localized
        case .parentFeedback:
            return "ai_analysis.parent_feedback_title".localized
        }
    }
    
    /**
     * 报告摘要信息
     */
    var summary: String {
        return """
        学生基本信息：\(studentName)
        行为记录统计：共\(totalRecords)条记录，其中加分\(positiveRecords)次，扣分\(negativeRecords)次
        积极行为比例：\(formattedPositiveRatio)
        生成时间：\(formattedGeneratedTime)
        """
    }
    
    /**
     * 完整报告文本（用于复制）
     */
    var fullReportText: String {
        return """
        【\(reportTitle)】
        
        \(summary)
        
        【详细分析】
        \(analysisContent)
        
        ---
        本报告由团团转AI分析系统生成
        """
    }
}

/**
 * AI分析错误类型
 */
enum AIAnalysisError: Error, LocalizedError {
    case permissionDenied(reason: String)
    case insufficientData(recordCount: Int)
    case networkError(Error)
    case apiError(statusCode: Int, message: String)
    case parseError(Error)
    case unknownError(String)
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied(let reason):
            return reason
        case .insufficientData(let count):
            return String(format: "ai_analysis.error.insufficient_records".localized, count)
        case .networkError(let error):
            // 将网络错误转换为用户友好的消息
            if error.localizedDescription.contains("timeout") || error.localizedDescription.contains("超时") {
                return "网络连接超时，请检查网络后重试"
            } else if error.localizedDescription.contains("offline") || error.localizedDescription.contains("disconnected") {
                return "网络连接不可用，请检查网络设置"
            } else {
                return "ai_analysis.error.network".localized
            }
        case .apiError(let statusCode, let message):
            // 为不同状态码提供友好的错误信息，避免显示技术细节
            switch statusCode {
            case 400:
                return "请求格式有误，请稍后重试"
            case 401:
                return "服务认证失败，请联系客服"
            case 429:
                return "请求过于频繁，请稍后重试"
            case 500, 502, 503:
                return "服务暂时不可用，请稍后重试"
            default:
                // 避免直接显示API错误信息给用户
                if message.contains("Internal Server Error") || message.contains("internal_error") {
                    return "服务暂时不可用，请稍后重试"
                } else if message.contains("invalid_request") {
                    return "请求格式有误，请稍后重试"
                } else if message.contains("rate_limit") {
                    return "请求过于频繁，请稍后重试"
                } else {
                    return "网络服务异常，请稍后重试"
                }
            }
        case .parseError(_):
            return "数据处理失败，请重新尝试"
        case .unknownError(let message):
            // 检查是否是重复请求的错误
            if message.contains("正在生成报告中") {
                return message
            } else {
                return "操作失败，请重新尝试"
            }
        }
    }
    
    var recoveryMessage: String {
        switch self {
        case .permissionDenied(_):
            return "ai_analysis.recovery.upgrade".localized
        case .insufficientData(_):
            return "ai_analysis.recovery.more_records".localized
        case .networkError(_):
            return "ai_analysis.recovery.check_network".localized
        case .apiError(let statusCode, _):
            switch statusCode {
            case 429:
                return "请等待片刻后重试"
            case 500, 502, 503:
                return "ai_analysis.recovery.try_later".localized
            default:
                return "ai_analysis.recovery.try_again".localized
            }
        case .parseError(_):
            return "ai_analysis.recovery.try_again".localized
        case .unknownError(let message):
            if message.contains("正在生成报告中") {
                return "请等待当前生成完成"
            } else {
                return "ai_analysis.recovery.contact_support".localized
            }
        }
    }
}

/**
 * AI分析权限验证结果
 */
struct AIAnalysisPermissionResult {
    let hasPermission: Bool
    let reason: String?
    let canUpgrade: Bool
    
    static func allowed() -> AIAnalysisPermissionResult {
        return AIAnalysisPermissionResult(hasPermission: true, reason: nil, canUpgrade: false)
    }
    
    static func denied(reason: String, canUpgrade: Bool = false) -> AIAnalysisPermissionResult {
        return AIAnalysisPermissionResult(hasPermission: false, reason: reason, canUpgrade: canUpgrade)
    }
} 