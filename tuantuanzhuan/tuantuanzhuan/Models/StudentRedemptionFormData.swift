//
//  StudentRedemptionFormData.swift
//  tuantuanzhuan
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation
import SwiftUI

/**
 * 兑换表单验证结果
 */
enum StudentRedemptionFormValidationResult {
    case valid
    case invalidName(String)
    case invalidCost(String)
    case insufficientPoints(String)
    
    /**
     * 是否为有效结果
     */
    var isValid: Bool {
        if case .valid = self {
            return true
        }
        return false
    }
    
    /**
     * 获取错误消息
     */
    var errorMessage: String? {
        switch self {
        case .valid:
            return nil
        case .invalidName(let message):
            return message
        case .invalidCost(let message):
            return message
        case .insufficientPoints(let message):
            return message
        }
    }
}

/**
 * 兑换表单数据管理
 */
@MainActor
class StudentRedemptionFormData: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var items: [StudentRedemptionOperationItem] = []
    @Published var validationError: String? = nil
    
    // MARK: - Properties
    
    let studentPoints: Int
    
    // MARK: - Computed Properties
    
    /**
     * 计算总消耗积分
     */
    var totalCost: Int {
        return items.reduce(0) { $0 + $1.cost }
    }
    
    /**
     * 检查学生积分是否足够
     */
    var canAfford: Bool {
        return studentPoints >= totalCost
    }
    
    /**
     * 获取有效的兑换操作项
     */
    var validItems: [StudentRedemptionOperationItem] {
        return items.filter { $0.isValid }
    }
    
    /**
     * 检查是否有有效数据
     */
    var hasValidData: Bool {
        return !validItems.isEmpty && canAfford
    }
    
    // MARK: - Initialization
    
    /**
     * 初始化方法
     */
    init(studentPoints: Int) {
        self.studentPoints = studentPoints
        self.items = [StudentRedemptionOperationItem()] // 默认添加一个空项
    }
    
    // MARK: - Public Methods
    
    /**
     * 添加新的兑换项
     */
    func addItem() {
        items.append(StudentRedemptionOperationItem())
        clearValidationError()
    }
    
    /**
     * 移除指定位置的兑换项
     */
    func removeItem(at index: Int) {
        guard index >= 0 && index < items.count && items.count > 1 else { return }
        items.remove(at: index)
        clearValidationError()
    }
    
    /**
     * 验证表单数据
     */
    func validateForm() -> StudentRedemptionFormValidationResult {
        // 检查是否有有效的兑换项
        let validItems = self.validItems
        if validItems.isEmpty {
            return .invalidName("redemption.form.error.no_valid_items".localized)
        }
        
        // 检查每个项目的名称
        for (index, item) in validItems.enumerated() {
            if item.formattedName.isEmpty {
                return .invalidName("redemption.form.error.empty_name".localized(with: "\(index + 1)"))
            }
        }
        
        // 检查每个项目的积分消耗
        for (index, item) in validItems.enumerated() {
            if item.cost <= 0 {
                return .invalidCost("redemption.form.error.invalid_cost".localized(with: "\(index + 1)"))
            }
        }
        
        // 检查积分是否足够
        if !canAfford {
            return .insufficientPoints("redemption.form.error.insufficient_points".localized(with: "\(totalCost)", "\(studentPoints)"))
        }
        
        return .valid
    }
    
    /**
     * 清除验证错误
     */
    func clearValidationError() {
        validationError = nil
    }
    
    /**
     * 设置验证错误
     */
    func setValidationError(_ error: String) {
        validationError = error
    }
    
    /**
     * 重置表单数据
     */
    func reset() {
        items = [StudentRedemptionOperationItem()]
        clearValidationError()
    }
    
    /**
     * 更新指定位置的兑换项名称
     */
    func updateItemName(at index: Int, name: String) {
        guard index >= 0 && index < items.count else { return }
        items[index].name = name
        clearValidationError()
    }
    
    /**
     * 更新指定位置的兑换项积分消耗
     */
    func updateItemCost(at index: Int, cost: Int) {
        guard index >= 0 && index < items.count else { return }
        items[index].cost = max(0, cost)
        clearValidationError()
    }
    
    /**
     * 更新指定位置的兑换项积分消耗（字符串输入）
     */
    func updateItemCostString(at index: Int, costString: String) {
        guard index >= 0 && index < items.count else { return }
        let cost = Int(costString) ?? 0
        updateItemCost(at: index, cost: cost)
    }
    
    /**
     * 获取指定位置的积分消耗字符串
     */
    func getItemCostString(at index: Int) -> String {
        guard index >= 0 && index < items.count else { return "0" }
        let cost = items[index].cost
        return cost > 0 ? "\(cost)" : ""
    }
} 