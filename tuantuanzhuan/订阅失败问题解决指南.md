# 订阅失败问题解决指南

## 问题概述

根据日志分析，订阅失败的主要原因是：
1. App Store Connect中的订阅产品状态为 `MISSING_METADATA`
2. StoreKit认证错误
3. 沙盒测试环境配置不完整

## 解决方案分步指南

### 第一步：App Store Connect 产品配置

#### 1.1 登录 App Store Connect
1. 访问 https://appstoreconnect.apple.com
2. 使用开发者账号登录

#### 1.2 配置订阅产品
1. 选择你的应用 "团团转"
2. 进入 **功能** → **App内购买项目**
3. 对于每个订阅产品，确保以下信息已完整填写：

**产品ID清单**：
- `com.tuantuanzhuan.subscription.monthly.basic` (初级会员月度订阅)
- `com.tuantuanzhuan.subscription.yearly.basic` (初级会员年度订阅)  
- `com.tuantuanzhuan.subscription.monthly.premium` (高级会员月度订阅)
- `com.tuantuanzhuan.subscription.yearly.premium` (高级会员年度订阅)

**必填信息**：
- **产品ID**: 与代码中完全一致
- **参考名称**: 如"初级会员月度订阅"
- **订阅群组**: 创建并分配到同一个订阅群组
- **订阅时长**: 月度/年度
- **价格**: 设置合适的价格点
- **显示名称**: 用户看到的名称
- **描述**: 产品功能描述
- **促销图像**: 可选，但建议上传

#### 1.3 本地化配置
为每个产品添加中文本地化：
- **显示名称**: "初级会员"、"高级会员"等
- **描述**: 详细的功能说明

#### 1.4 提交审核
1. 确保所有产品状态显示为"准备提交"
2. 点击"提交以供审核"
3. 等待苹果审核通过（通常1-7天）

### 第二步：RevenueCat Dashboard 配置

#### 2.1 登录 RevenueCat
1. 访问 https://app.revenuecat.com
2. 登录你的RevenueCat账号

#### 2.2 验证产品配置
1. 进入 **Products** 页面
2. 确保所有4个产品ID已正确添加：
   - Store Product ID 与 App Store Connect 完全匹配
   - RevenueCat ID 可以自定义，但要记住用于代码中

#### 2.3 配置 Offerings
1. 进入 **Offerings** 页面
2. 编辑 "default" offering
3. 确保包含所有4个产品：
   - `$rc_monthly` → monthly.basic
   - `$rc_annual` → yearly.basic  
   - `custom` → yearly.premium
   - `custom_com.tuantuanzhuan.subscription.monthly.premium` → monthly.premium

#### 2.4 配置 Entitlements
1. 进入 **Entitlements** 页面
2. 确保有以下权限：
   - `basic_member` → 关联到 basic 产品
   - `premium_member` → 关联到 premium 产品

### 第三步：沙盒测试账号配置

#### 3.1 创建沙盒测试用户
1. 在 App Store Connect 中进入 **用户和访问** → **沙盒测试员**
2. 点击"+"创建新的测试账号
3. 填写：
   - **邮箱**: 使用一个全新的邮箱地址
   - **密码**: 设置强密码
   - **名字/姓氏**: 随意填写
   - **国家/地区**: 选择中国
   - **App Store Connect 访问权限**: 关闭

#### 3.2 在设备上配置沙盒账号
1. 在iOS设备上打开 **设置**
2. 进入 **App Store**
3. 如果已登录，先退出登录
4. 不要在这里登录沙盒账号！

#### 3.3 测试购买流程
1. 启动应用
2. 进入订阅页面
3. 点击购买按钮
4. 系统会弹出登录弹窗，此时输入沙盒测试账号
5. 完成购买流程

### 第四步：代码优化建议

#### 4.1 增强错误信息显示

在 `RevenueCatManager.swift` 中增强错误处理：

```swift
} catch {
    await MainActor.run {
        let detailedError = self.extractDetailedError(from: error)
        self.errorMessage = detailedError
        self.isLoading = false
    }
    print("❌ 购买失败: \(error)")
    print("❌ 详细错误: \(error.localizedDescription)")
    return false
}

private func extractDetailedError(from error: Error) -> String {
    if let purchasesError = error as? PurchasesError {
        switch purchasesError {
        case .productNotFoundError:
            return "产品未找到，请稍后重试"
        case .purchaseNotAllowedError:
            return "当前无法购买，请检查账号设置"
        case .purchaseInvalidError:
            return "购买信息无效"
        case .paymentPendingError:
            return "支付处理中，请稍候"
        case .networkError:
            return "网络错误，请检查网络连接"
        default:
            return "购买失败：\(purchasesError.localizedDescription)"
        }
    }
    return "购买失败：\(error.localizedDescription)"
}
```

#### 4.2 添加产品可用性检查

```swift
func checkProductAvailability() -> Bool {
    guard let offerings = offerings else {
        errorMessage = "产品信息未加载，请稍后重试"
        return false
    }
    
    guard !offerings.all.isEmpty else {
        errorMessage = "暂无可用产品，请稍后重试"
        return false
    }
    
    return true
}
```

### 第五步：临时测试方案

在等待App Store Connect审核期间，可以添加本地测试功能：

```swift
#if DEBUG
func simulatePurchase(productId: String) {
    print("🧪 模拟购买: \(productId)")
    
    // 模拟成功购买
    currentSubscriptionLevel = productId.contains("premium") ? .premium : .basic
    
    // 发送成功通知
    NotificationCenter.default.post(name: .subscriptionStatusChanged, object: nil)
    
    print("✅ 模拟购买成功")
}
#endif
```

## 验证清单

### App Store Connect
- [ ] 所有4个订阅产品已创建
- [ ] 产品状态不再是 MISSING_METADATA
- [ ] 价格和时长配置正确
- [ ] 本地化信息完整
- [ ] 订阅群组配置正确

### RevenueCat
- [ ] API Key 正确配置
- [ ] 产品ID 与 App Store Connect 匹配
- [ ] Offerings 包含所有产品
- [ ] Entitlements 正确关联

### 测试环境
- [ ] 沙盒测试账号已创建
- [ ] 设备上正确配置测试环境
- [ ] 能够触发购买弹窗

### 代码
- [ ] 错误处理完善
- [ ] 日志信息详细
- [ ] 产品可用性检查

## 预期结果

完成以上配置后，日志应该显示：

```
🛒 订阅按钮被点击 - 初级会员 月会员
🆔 准备购买产品: com.tuantuanzhuan.subscription.monthly.basic
✅ 购买成功: com.tuantuanzhuan.subscription.monthly.basic
```

## 常见问题

**Q: 为什么产品状态一直是 MISSING_METADATA？**
A: 需要完整填写产品的所有必填信息，包括描述、价格、本地化等。

**Q: 沙盒测试账号登录失败怎么办？**
A: 确保使用全新的邮箱地址，不要复用已有的Apple ID。

**Q: RevenueCat显示产品未同步怎么办？**
A: 等待App Store Connect审核通过后，RevenueCat会自动同步产品状态。

**Q: 真机测试时提示"此项目不可用"？**
A: 确保使用沙盒测试账号，且在正确的测试环境中。

## 需要帮助？

如果遇到问题，请提供：
1. 完整的错误日志
2. App Store Connect 产品状态截图
3. RevenueCat 配置截图
4. 具体的测试步骤和结果 