# 账号删除对象生命周期问题修复方案

## 问题描述

在执行账号删除操作时，到步骤5时显示删除失败，根本原因是删除账号在第5步"撤销Apple登录"时出现`userNotFound`错误。这是一个典型的对象生命周期问题：

### 问题根源

1. **步骤3：删除本地数据**
   - `context.delete(user)` 已经删除了用户对象
   - CoreData上下文中的User对象被标记为删除状态

2. **步骤5：撤销Apple登录**
   - 代码仍尝试访问 `user.appleUserID` 属性
   - 但对象已被删除，属性访问返回nil
   - 触发 `userNotFound` 错误

### 技术分析

```swift
// 问题代码示例
private func revokeAppleLogin(for user: User, completion: @escaping (Result<Void, Error>) -> Void) {
    // 此时user对象可能已在步骤3中被删除
    guard let appleUserID = user.appleUserID else {
        // 访问已删除对象的属性返回nil，导致错误
        completion(.failure(AccountDeletionManager.DeletionError.userNotFound))
        return
    }
}
```

## 修复方案

### 1. 引入UserDeletionInfo结构体

创建一个专门的数据结构来保存删除过程中需要的用户信息：

```swift
private struct UserDeletionInfo {
    let appleUserID: String?
    let userID: NSManagedObjectID
}
```

### 2. 在删除前保存关键信息

在执行删除步骤前，提前保存必要的用户信息：

```swift
private func executeDeleteionSteps(for user: User, completion: @escaping (Result<Void, Error>) -> Void) {
    // 在删除前保存必要的用户信息，避免对象生命周期问题
    let userInfo = UserDeletionInfo(
        appleUserID: user.appleUserID,
        userID: user.objectID
    )
    // ...
}
```

### 3. 修改方法签名使用UserDeletionInfo

将受影响的方法修改为使用保存的用户信息：

```swift
// 修复后的方法
private func revokeAppleLogin(userInfo: UserDeletionInfo, completion: @escaping (Result<Void, Error>) -> Void) {
    guard let appleUserID = userInfo.appleUserID else {
        print("❌ 账号删除失败: userNotFound")
        completion(.failure(AccountDeletionManager.DeletionError.userNotFound))
        return
    }
    // 使用保存的appleUserID继续处理
}
```

### 4. 智能对象传递

根据删除步骤的进度，智能地传递user对象或nil：

```swift
// 步骤3删除本地数据后，user对象将被删除，后续步骤只能使用userInfo
let currentUser = (stepIndex < 3) ? user : nil
executeIndividualStep(step, user: currentUser, userInfo: userInfo) { result in
    // 处理结果
}
```

## 修复效果

### 修复前
- ❌ 步骤5访问已删除对象导致userNotFound错误
- ❌ 删除流程在第5步失败
- ❌ 用户看到删除失败提示

### 修复后
- ✅ 使用保存的用户信息，避免访问已删除对象
- ✅ 删除流程能够完整执行到第6步
- ✅ 用户能够成功完成账号删除

## 代码质量改进建议

### 1. 错误处理增强

```swift
// 建议：添加更详细的错误日志
guard let appleUserID = userInfo.appleUserID else {
    let errorMsg = "Apple用户ID为空，无法撤销登录"
    print("❌ 账号删除失败: \(errorMsg)")
    completion(.failure(AccountDeletionManager.DeletionError.userNotFound))
    return
}
```

### 2. 防御性编程

```swift
// 建议：添加更多的安全检查
private func executeIndividualStep(_ step: DeletionStep, user: User?, userInfo: UserDeletionInfo, completion: @escaping (Result<Void, Error>) -> Void) {
    
    // 验证userInfo的有效性
    guard !userInfo.userID.isTemporaryID else {
        completion(.failure(AccountDeletionManager.DeletionError.userNotFound))
        return
    }
    
    switch step {
    // ...
    }
}
```

### 3. 单元测试建议

```swift
// 建议：添加针对对象生命周期的测试
func testUserObjectLifecycleInDeletion() {
    // 测试删除过程中对象状态的变化
    // 验证UserDeletionInfo能够正确保存信息
    // 确保后续步骤不依赖已删除的对象
}
```

### 4. 文档改进

```swift
/**
 * 步骤5: 撤销Apple登录
 * 
 * 注意：此方法使用UserDeletionInfo而非User对象，
 * 因为在步骤3中User对象已被删除。
 * 
 * @param userInfo 包含删除过程中需要的用户信息
 * @param completion 完成回调
 */
private func revokeAppleLogin(userInfo: UserDeletionInfo, completion: @escaping (Result<Void, Error>) -> Void)
```

## 测试验证

### 测试步骤

1. **创建测试用户**
   - 使用Apple ID登录
   - 创建一些测试数据

2. **执行删除操作**
   - 调用 `deleteCurrentUserAccount`
   - 观察每个步骤的执行情况

3. **验证修复效果**
   - 确认步骤5不再出现userNotFound错误
   - 验证删除流程能够完整执行
   - 检查最终清理是否成功

### 预期结果

```
📋 执行删除步骤 1/6: 准备删除用户账号
✅ 步骤1完成
📋 执行删除步骤 2/6: 创建删除标记
✅ 步骤2完成
📋 执行删除步骤 3/6: 删除本地数据
✅ 步骤3完成
📋 执行删除步骤 4/6: 删除云端数据
✅ 步骤4完成
📋 执行删除步骤 5/6: 撤销Apple登录
✅ 步骤5完成  // 修复后不再失败
📋 执行删除步骤 6/6: 最终清理
✅ 步骤6完成
🎉 账号删除成功
```

## 总结

这个修复方案通过引入`UserDeletionInfo`结构体和智能对象传递机制，彻底解决了账号删除过程中的对象生命周期问题。修复后的代码更加健壮，能够正确处理CoreData对象的删除状态，确保删除流程的完整性和可靠性。

### 关键改进点

1. **对象生命周期管理**：通过UserDeletionInfo避免访问已删除对象
2. **错误处理增强**：添加更详细的错误日志和状态检查
3. **代码健壮性**：使用防御性编程技术提高代码可靠性
4. **可维护性**：清晰的文档和注释说明设计意图

这种设计模式也可以应用到其他涉及对象生命周期管理的场景中，是一个很好的最佳实践示例。