班级积分大师PDF&PRD

# 班级积分大师

## 一、页面结构文档（结构清单）

### 1. 启动页 & 登录页

* 启动页（Splash）
  * 应用 Logo
  * 品牌标语（如“积分培养好习惯”）
  * 1\~2 秒自动跳转

* 登录页
  * Apple ID 登录按钮
  * 用户协议 & 隐私政策勾选
  * 登录后进入首页

### 2. 首页（班级视图）

* 顶部搜索框（支持学号 / 姓名模糊搜索）
* 班级切换区（横向滚动，点击切换班级）
* 操作按钮
  * “…”菜单：添加学生 / 批量导入
  * “全班”按钮：执行全班加分/扣分
  * 全班加分统计：显示分数按钮（初始为 0），点击后选择时间范围

* 学生卡片列表（正方形/长方形卡片，双列排布）
  * 男生：浅蓝色；女生：粉红色
  * 显示：学号 + 姓名 + 当前积分
  * 点击卡片：进入学生详情页
  * 长按：出现“X”，确认后可删除该学生

### 3. 学生详情页

* 显示学生姓名、学号、积分
* 操作按钮：加分 / 扣分
  * 弹出常用规则（最多 5 条）+ 自定义入口

* 查看历史记录
  * 加/扣分记录（带时间、原因、分值）
  * 奖品兑换与抽奖记录（与加扣分记录区分）

* 奖品兑换
 * 显示预先配置的奖品
 * 点击后直接扣除所需积分，无需弹窗
 * 自动生成兑换记录，不计入分析

* 抽奖入口（所有用户均显示）
  * 点击后选择道具（大转盘、盲盒、刮刮卡）
  * 权限不足时弹窗提示并引导订阅

* 生成 AI 分析报告按钮（所有用户均显示）
  * 检查是否为高级会员 + 是否有 10 条以上加/扣分记录
  * 权限不足时弹窗提示 + 展示亮点 + 引导订阅

### 4. 抽奖功能页面

* 大转盘：中心为抽奖按钮，点击即执行
* 盲盒：点击盒子即执行抽奖
* 刮刮卡：点击刮卡图层，刮开显示奖品
* 积分不足时禁用抽奖或无法点击
* 抽奖结果直接展示在当前页面（无弹窗）
* 抽奖记录写入兑换记录，不计入分析

### 5. 设置页面

* 班级管理
  * 班级卡片：显示班级名 + 班级人数
  * 创建班级：检查会员权限 → 填写名称 → 完成
  * 点击班级：进入该班级的设置页面
    * 设置常用加/扣分规则（各 5 条）
    * 奖品设置（从奖品库导入或自定义添加）

* 奖品库
  * 添加奖品支持批量录入（“+” 添加行）

* 规则库
  * 添加规则支持批量录入（“+” 添加行）

* 道具配置
  * 选择班级 → 选择道具 → 设置奖品 + 消耗积分

### 6. 个人中心页面

* 用户信息卡片（头像 / 昵称 / 当前会员等级）
* 订阅管理
  * 当前状态 + 升级会员按钮 + 权益说明
* 系统设置
  * 语言切换（简体中文 / 英文）
  * 关于与帮助
  * 用户服务协议 / 隐私政策（WebView）
  * 删除账号
  * 退出登录

### 7. AI 分析报告页面

* 顶部返回 + 复制按钮
* 报告内容：累计加/扣分+结构化文本 + 行为趋分析
* 生成条件：高级会员 + ≥10 条记录 + 联网

---

## 二、完整详细的需求文档 PRD（产品需求文档）

### 1. 产品背景与目标

* 班级积分大师是一款帮助幼儿园与小学教师基于积分制度激励学生、记录行为、执行奖惩的教育工具类 App。

### 2. 用户与使用场景

* 目标用户：幼儿园教师、小学教师（3\~12 岁学生）
* 使用场景：

  * 在学校期间快速记录学生表现
  * 课后根据积分执行奖惩
  * 与家长沟通学生表现

### 3. 功能概览

| 功能       | 免费   | 初级会员 | 高级会员 |
| -------- | ---- | ---- | ---- |
| 班级管理     | 1 个班 | 2 个班 | 5 个班 |
| 学生管理     | ✅    | ✅    | ✅    |
| 加/扣分     | ✅    | ✅    | ✅    |
| 奖品兑换     | ✅    | ✅    | ✅    |
| 大转盘抽奖    | ❌    | ✅    | ✅    |
| 盲盒 / 刮刮卡 | ❌    | ❌    | ✅    |
| AI 分析报告  | ❌    | ❌    | ✅    |
| 多设备同步    |  ✅  | ✅    | ✅    |
| 常被规则数量    |  5  | 10   | 10   |

补充功能描述：
1.当用初级/高级会员到期后，如果用户创建了多个班级，提醒用户会员已到期，正在管理的多个班级，只能选择管理其中一个班级，其他班级将被冻结，但数据会继续保存。请用户选择继续管理的班级，或重新订阅。

2.新注册用户可领取1个月高级会员的试用。

### 4. 技术方案

* 架构：MVVM + SwiftUI
* 本地存储：CoreData
* 云同步：CloudKit
* AI 接口：DeepSeek API
* 订阅管理：RevenueCat

### 5. 数据模型

* 班级：名称、学生列表、规则、奖品、抽奖设置
* 学生：姓名、性别、学号、积分、历史记录
* 历史记录：加分、扣分、兑换、抽奖（含时间）

### 6. 核心交互流程（略）

* 已在流程图阶段规划，另附流程图文档

### 7. 非功能需求

*支持iOS 15.6以上
* 多语言支持：首发中文，预留英文
* UI 风格：儿童友好，颜色丰富，交互明确
* 响应速度：本地记录操作 < 1 秒

### 8. 风险控制

* 所有敏感数据脱敏后调用 AI
* 所有积分操作可撤销（加/扣分记录）
* 道具抽奖记录分类写入，避免影响分析报告

### 9. 版本规划

* v1.0：功能完备、数据同步、订阅体系
* v1.1：支持导出报告、分享、班级归档功能
* v1.2:  新用户赠送1个月高级会员卡：你有一个福利可领取 →点击查看详情 →感谢您对教育事业的贡献，赠送您一个月的高级会员卡。