本设计用于支持多设备同步、多班级独立配置、积分记录、抽奖记录、奖品兑换、AI 分析、权限判断等功能。采用 CoreData + CloudKit 同步机制，确保本地缓存与 iCloud 同步一致。

---

## 模型概览

```
User（用户）
└── hasMany → Class（班级）
       └── hasMany → Student（学生）
              └── hasMany → PointRecord（积分记录）
              └── hasMany → RedemptionRecord（奖品兑换记录）
              └── hasMany → LotteryRecord（抽奖记录）
       └── hasMany → Rule（该班级使用的规则）
       └── hasMany → Prize（该班级使用的奖品）

RuleTemplate（规则库）
PrizeTemplate（奖品库）
Subscription（订阅信息）
```

---

## User（用户）

| 属性名       | 类型     | 说明          |
| --------- | ------ | ----------- |
| id        | UUID   | 主键          |
| nickname  | String | 昵称          |
| email     | String | Apple ID 邮箱 |
| createdAt | Date   | 创建时间        |

---

## Class（班级）

| 属性名          | 类型     | 说明           |
| ------------ | ------ | ------------ |
| id           | UUID   | 主键           |
| name         | String | 班级名称         |
| studentCount | Int    | 当前学生数量（自动计算） |
| createdAt    | Date   | 创建时间         |
| owner        | User   | 所属用户         |

---

## Student（学生）

| 属性名           | 类型     | 说明                |
| ------------- | ------ | ----------------- |
| id            | UUID   | 主键                |
| name          | String | 姓名                |
| studentNumber | String | 学号                |
| gender        | String | "male" / "female" |
| point         | Int    | 当前积分              |
| createdAt     | Date   | 创建时间              |
| class         | Class  | 所属班级              |

---

## PointRecord（加扣分记录）

| 属性名        | 类型      | 说明      |
| ---------- | ------- | ------- |
| id         | UUID    | 主键      |
| reason     | String  | 原因说明    |
| value      | Int     | 加/扣的分数  |
| timestamp  | Date    | 发生时间    |
| student    | Student | 关联学生    |
| isReversed | Bool    | 是否为撤销记录 |

---

## Prize（班级奖品）

| 属性名   | 类型     | 说明      |
| ----- | ------ | ------- |
| id    | UUID   | 主键      |
| name  | String | 奖品名称    |
| cost  | Int    | 所需积分    |
| type  | String | 实物 / 虚拟 |
| class | Class  | 所属班级    |

---

## RedemptionRecord（奖品兑换记录）

| 属性名       | 类型      | 说明              |
| --------- | ------- | --------------- |
| id        | UUID    | 主键              |
| prizeName | String  | 兑换的奖品名称（避免奖品变动） |
| cost      | Int     | 消耗的积分           |
| timestamp | Date    | 兑换时间            |
| student   | Student | 所属学生            |

---

## LotteryRecord（抽奖记录）

| 属性名         | 类型      | 说明             |
| ----------- | ------- | -------------- |
| id          | UUID    | 主键             |
| toolType    | String  | 大转盘 / 盲盒 / 刮刮卡 |
| prizeResult | String  | 抽中结果           |
| cost        | Int     | 消耗积分           |
| timestamp   | Date    | 抽奖时间           |
| student     | Student | 所属学生           |

---

## Rule（班级常用规则）

| 属性名        | 类型     | 说明           |
| ---------- | ------ | ------------ |
| id         | UUID   | 主键           |
| name       | String | 规则名（如“发言积极”） |
| value      | Int    | 加/扣的分值       |
| type       | String | add / deduct |
| isFrequent | Bool   | 是否为常用规则      |
| class      | Class  | 所属班级         |

---

## RuleTemplate（全局规则库）

| 属性名   | 类型     | 说明           |
| ----- | ------ | ------------ |
| id    | UUID   | 主键           |
| name  | String | 规则名          |
| value | Int    | 分值           |
| type  | String | add / deduct |

---

## PrizeTemplate（全局奖品库）

| 属性名  | 类型     | 说明      |
| ---- | ------ | ------- |
| id   | UUID   | 主键      |
| name | String | 奖品名称    |
| cost | Int    | 积分消耗    |
| type | String | 实物 / 虚拟 |

---

## Subscription（订阅信息）

| 属性名        | 类型     | 说明                     |
| ---------- | ------ | ---------------------- |
| user       | User   | 关联用户                   |
| level      | String | free / basic / premium |
| maxClasses | Int    | 可创建的班级数                |
| updatedAt  | Date   | 更新时间（订阅刷新）             |

---